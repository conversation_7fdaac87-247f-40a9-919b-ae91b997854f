import React from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "../../../supabase/auth";
import { 
  Lock, 
  Crown, 
  Check, 
  Star, 
  Zap, 
  BookOpen, 
  Users, 
  Download 
} from "lucide-react";

interface PaywallProps {
  articleTitle: string;
  previewContent: string;
  onUpgrade?: () => void;
}

export default function Paywall({ 
  articleTitle, 
  previewContent, 
  onUpgrade 
}: PaywallProps) {
  const { user } = useAuth();

  const premiumFeatures = [
    { icon: BookOpen, text: "Unlimited access to premium articles" },
    { icon: Download, text: "Download articles for offline reading" },
    { icon: Users, text: "Exclusive community access" },
    { icon: Zap, text: "Ad-free reading experience" },
    { icon: Star, text: "Early access to new content" },
  ];

  return (
    <div className="relative">
      {/* Preview Content with Fade */}
      <div className="relative">
        <div className="prose prose-lg max-w-none">
          <p className="text-gray-700 leading-relaxed">
            {previewContent}
          </p>
        </div>
        
        {/* Gradient Overlay */}
        <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-white via-white/80 to-transparent" />
      </div>

      {/* Paywall Card */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="relative -mt-16 z-10"
      >
        <Card className="border-2 border-yellow-200 bg-gradient-to-br from-yellow-50 to-orange-50 shadow-xl">
          <CardHeader className="text-center pb-4">
            <div className="flex justify-center mb-4">
              <div className="relative">
                <Crown className="w-16 h-16 text-yellow-600" />
                <div className="absolute -top-1 -right-1">
                  <Lock className="w-6 h-6 text-yellow-700 bg-yellow-100 rounded-full p-1" />
                </div>
              </div>
            </div>
            
            <CardTitle className="text-2xl font-bold text-gray-900 mb-2">
              Continue Reading "{articleTitle}"
            </CardTitle>
            
            <div className="flex justify-center">
              <Badge className="bg-yellow-600 text-white px-4 py-1">
                Premium Content
              </Badge>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            <div className="text-center">
              <p className="text-gray-700 mb-4">
                This article is available to premium subscribers only. 
                Unlock unlimited access to our entire library of premium content.
              </p>
            </div>

            {/* Pricing Options */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <motion.div
                whileHover={{ scale: 1.02 }}
                className="relative"
              >
                <Card className="border-2 border-blue-200 bg-white">
                  <CardContent className="p-4 text-center">
                    <h3 className="font-bold text-lg mb-2">Monthly</h3>
                    <div className="mb-3">
                      <span className="text-3xl font-bold">$9.99</span>
                      <span className="text-gray-500">/month</span>
                    </div>
                    <Button className="w-full bg-blue-600 hover:bg-blue-700" asChild>
                      <Link to="/subscribe?plan=monthly">
                        Start Monthly Plan
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.02 }}
                className="relative"
              >
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                  <Badge className="bg-green-600 text-white px-3 py-1">
                    Best Value
                  </Badge>
                </div>
                <Card className="border-2 border-green-200 bg-white">
                  <CardContent className="p-4 text-center">
                    <h3 className="font-bold text-lg mb-2">Annual</h3>
                    <div className="mb-1">
                      <span className="text-3xl font-bold">$99.99</span>
                      <span className="text-gray-500">/year</span>
                    </div>
                    <p className="text-sm text-green-600 mb-3">Save 17%</p>
                    <Button className="w-full bg-green-600 hover:bg-green-700" asChild>
                      <Link to="/subscribe?plan=annual">
                        Start Annual Plan
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            {/* Features List */}
            <div className="bg-white rounded-lg p-4 border border-gray-200">
              <h4 className="font-semibold mb-3 text-center">What you'll get:</h4>
              <div className="space-y-2">
                {premiumFeatures.map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="flex items-center space-x-3"
                  >
                    <Check className="w-5 h-5 text-green-600 flex-shrink-0" />
                    <span className="text-sm text-gray-700">{feature.text}</span>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Alternative Actions */}
            <div className="text-center space-y-3 pt-4 border-t border-gray-200">
              <p className="text-sm text-gray-600">
                Already a subscriber?{" "}
                <Link to="/login" className="text-blue-600 hover:underline font-medium">
                  Sign in here
                </Link>
              </p>
              
              {!user && (
                <p className="text-sm text-gray-600">
                  New to The Chronicle?{" "}
                  <Link to="/signup" className="text-blue-600 hover:underline font-medium">
                    Create a free account
                  </Link>
                </p>
              )}
              
              <Button variant="outline" size="sm" asChild>
                <Link to="/articles">
                  Browse Free Articles
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
