import { supabase } from "../../supabase/supabase";

export async function seedDatabase() {
  try {
    console.log("🌱 Starting database seeding...");

    // Check if we're already seeded
    const { data: existingCategories } = await supabase
      .from('categories')
      .select('id')
      .limit(1);

    if (existingCategories && existingCategories.length > 0) {
      console.log("✅ Database already seeded!");
      return { success: true, message: "Database already seeded" };
    }

    // Seed categories
    console.log("📝 Seeding categories...");
    const { error: categoriesError } = await supabase
      .from('categories')
      .insert([
        {
          id: '550e8400-e29b-41d4-a716-446655440001',
          name: 'Technology',
          slug: 'technology',
          description: 'Latest tech news and innovations',
          color: '#3B82F6'
        },
        {
          id: '550e8400-e29b-41d4-a716-446655440002',
          name: 'Business',
          slug: 'business',
          description: 'Business insights and market analysis',
          color: '#10B981'
        },
        {
          id: '550e8400-e29b-41d4-a716-446655440003',
          name: 'Health',
          slug: 'health',
          description: 'Health and wellness articles',
          color: '#EF4444'
        },
        {
          id: '550e8400-e29b-41d4-a716-446655440004',
          name: 'Finance',
          slug: 'finance',
          description: 'Financial news and investment advice',
          color: '#F59E0B'
        },
        {
          id: '550e8400-e29b-41d4-a716-446655440005',
          name: 'Lifestyle',
          slug: 'lifestyle',
          description: 'Lifestyle and culture content',
          color: '#8B5CF6'
        }
      ]);

    if (categoriesError) {
      console.error("❌ Error seeding categories:", categoriesError);
      throw categoriesError;
    }

    // Seed product categories
    console.log("🛍️ Seeding product categories...");
    const { error: productCategoriesError } = await supabase
      .from('product_categories')
      .insert([
        {
          id: '650e8400-e29b-41d4-a716-446655440001',
          name: 'Digital Products',
          slug: 'digital-products',
          description: 'Courses, ebooks, and digital downloads',
          is_active: true
        },
        {
          id: '650e8400-e29b-41d4-a716-446655440002',
          name: 'Subscriptions',
          slug: 'subscriptions',
          description: 'Premium memberships and subscriptions',
          is_active: true
        },
        {
          id: '650e8400-e29b-41d4-a716-446655440003',
          name: 'Physical Products',
          slug: 'physical-products',
          description: 'Books, merchandise, and physical items',
          is_active: true
        }
      ]);

    if (productCategoriesError) {
      console.error("❌ Error seeding product categories:", productCategoriesError);
      throw productCategoriesError;
    }

    // Seed products
    console.log("📦 Seeding products...");
    const { error: productsError } = await supabase
      .from('products')
      .insert([
        {
          id: '750e8400-e29b-41d4-a716-446655440001',
          name: 'Premium Annual Subscription',
          slug: 'premium-annual-subscription',
          description: 'Get unlimited access to all premium content for a full year. Includes exclusive articles, ad-free reading experience, early access to new features, and member-only events.',
          short_description: 'Unlimited access to premium content for one year',
          price: 99.99,
          compare_price: 119.99,
          type: 'digital',
          category_id: '650e8400-e29b-41d4-a716-446655440002',
          images: ['https://images.unsplash.com/photo-1586892477838-2b96e85e0f96?w=800&q=80'],
          stock: 999,
          status: 'active',
          featured: true,
          attributes: {
            features: ['Unlimited premium articles', 'Ad-free experience', 'Early access', 'Member events'],
            duration: '12 months'
          }
        },
        {
          id: '750e8400-e29b-41d4-a716-446655440002',
          name: 'The Ultimate Productivity Journal',
          slug: 'ultimate-productivity-journal',
          description: 'A beautifully designed journal to track your goals, habits, and daily progress. Made with premium materials including thick, cream-colored paper and a durable hardcover binding.',
          short_description: 'Premium journal for goal tracking and productivity',
          price: 29.99,
          type: 'physical',
          category_id: '650e8400-e29b-41d4-a716-446655440003',
          images: ['https://images.unsplash.com/photo-1531346878377-a5be20888e57?w=800&q=80'],
          stock: 45,
          status: 'active',
          featured: true,
          attributes: {
            pages: 200,
            material: 'hardcover',
            size: 'A5',
            color: 'charcoal'
          }
        },
        {
          id: '750e8400-e29b-41d4-a716-446655440003',
          name: 'Digital Marketing Masterclass',
          slug: 'digital-marketing-masterclass',
          description: 'Learn the latest digital marketing strategies from industry experts. This comprehensive course covers SEO, social media marketing, content strategy, email marketing, and analytics.',
          short_description: 'Complete digital marketing course with expert insights',
          price: 49.99,
          compare_price: 79.99,
          type: 'digital',
          category_id: '650e8400-e29b-41d4-a716-446655440001',
          images: ['https://images.unsplash.com/photo-1432888498266-38ffec3eaf0a?w=800&q=80'],
          stock: 999,
          status: 'active',
          featured: true,
          attributes: {
            duration: '8 hours',
            modules: 12,
            templates: 25,
            certificate: true
          }
        }
      ]);

    if (productsError) {
      console.error("❌ Error seeding products:", productsError);
      throw productsError;
    }

    // Seed product tags
    console.log("🏷️ Seeding product tags...");
    const { error: productTagsError } = await supabase
      .from('product_tags')
      .insert([
        { product_id: '750e8400-e29b-41d4-a716-446655440001', tag: 'Premium' },
        { product_id: '750e8400-e29b-41d4-a716-446655440001', tag: 'Subscription' },
        { product_id: '750e8400-e29b-41d4-a716-446655440001', tag: 'Best Value' },
        { product_id: '750e8400-e29b-41d4-a716-446655440002', tag: 'Productivity' },
        { product_id: '750e8400-e29b-41d4-a716-446655440002', tag: 'Journal' },
        { product_id: '750e8400-e29b-41d4-a716-446655440002', tag: 'Physical' },
        { product_id: '750e8400-e29b-41d4-a716-446655440003', tag: 'Marketing' },
        { product_id: '750e8400-e29b-41d4-a716-446655440003', tag: 'Course' },
        { product_id: '750e8400-e29b-41d4-a716-446655440003', tag: 'Digital' }
      ]);

    if (productTagsError) {
      console.error("❌ Error seeding product tags:", productTagsError);
      throw productTagsError;
    }

    // Get the current user to seed articles
    const { data: { user } } = await supabase.auth.getUser();
    
    if (user) {
      console.log("📰 Seeding articles...");
      const { error: articlesError } = await supabase
        .from('articles')
        .insert([
          {
            id: '850e8400-e29b-41d4-a716-446655440001',
            title: 'The Future of AI in Content Creation',
            slug: 'future-ai-content-creation',
            excerpt: 'How artificial intelligence is revolutionizing the way we create and consume content across industries, from journalism to marketing.',
            content: 'Artificial intelligence is transforming content creation in unprecedented ways. From automated news writing to personalized content recommendations, AI is reshaping how we produce, distribute, and consume information...',
            author_id: user.id,
            category_id: '550e8400-e29b-41d4-a716-446655440001',
            featured_image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&q=80',
            is_premium: true,
            is_published: true,
            published_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            read_time: 5,
            views: 12450,
            likes: 245,
            comments_count: 56,
            shares: 89
          },
          {
            id: '850e8400-e29b-41d4-a716-446655440002',
            title: 'Sustainable Fashion: Beyond the Buzzword',
            slug: 'sustainable-fashion-beyond-buzzword',
            excerpt: 'Exploring what it truly means for fashion to be sustainable in today\'s global economy and consumer culture.',
            content: 'Sustainability in fashion has become more than just a trend—it\'s a necessity. As consumers become more conscious of their environmental impact, brands are being forced to reconsider their practices...',
            author_id: user.id,
            category_id: '550e8400-e29b-41d4-a716-446655440005',
            featured_image: 'https://images.unsplash.com/photo-1523381210434-271e8be1f52b?w=800&q=80',
            is_premium: false,
            is_published: true,
            published_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            read_time: 8,
            views: 8920,
            likes: 189,
            comments_count: 32,
            shares: 45
          },
          {
            id: '850e8400-e29b-41d4-a716-446655440003',
            title: 'The Science of Productivity',
            slug: 'science-of-productivity',
            excerpt: 'Research-backed strategies to maximize your efficiency and well-being in both personal and professional settings.',
            content: 'Productivity isn\'t just about doing more—it\'s about doing the right things efficiently. Recent research in cognitive science and psychology has revealed fascinating insights...',
            author_id: user.id,
            category_id: '550e8400-e29b-41d4-a716-446655440003',
            featured_image: 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=800&q=80',
            is_premium: true,
            is_published: true,
            published_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            read_time: 6,
            views: 15670,
            likes: 312,
            comments_count: 47,
            shares: 78
          },
          {
            id: '850e8400-e29b-41d4-a716-446655440004',
            title: 'Crypto Markets: Analysis and Predictions',
            slug: 'crypto-markets-analysis-predictions',
            excerpt: 'Expert insights on the current state of cryptocurrency markets and where digital assets might be headed in the coming year.',
            content: 'The cryptocurrency market continues to evolve at a rapid pace. With institutional adoption growing and regulatory frameworks taking shape, the landscape is changing dramatically...',
            author_id: user.id,
            category_id: '550e8400-e29b-41d4-a716-446655440004',
            featured_image: 'https://images.unsplash.com/photo-1621761191319-c6fb62004040?w=800&q=80',
            is_premium: false,
            is_published: true,
            published_at: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
            read_time: 10,
            views: 9340,
            likes: 276,
            comments_count: 89,
            shares: 134
          }
        ]);

      if (articlesError) {
        console.error("❌ Error seeding articles:", articlesError);
        throw articlesError;
      }

      // Seed article tags
      console.log("🏷️ Seeding article tags...");
      const { error: articleTagsError } = await supabase
        .from('article_tags')
        .insert([
          { article_id: '850e8400-e29b-41d4-a716-446655440001', tag: 'AI' },
          { article_id: '850e8400-e29b-41d4-a716-446655440001', tag: 'Technology' },
          { article_id: '850e8400-e29b-41d4-a716-446655440001', tag: 'Innovation' },
          { article_id: '850e8400-e29b-41d4-a716-446655440002', tag: 'Fashion' },
          { article_id: '850e8400-e29b-41d4-a716-446655440002', tag: 'Sustainability' },
          { article_id: '850e8400-e29b-41d4-a716-446655440003', tag: 'Productivity' },
          { article_id: '850e8400-e29b-41d4-a716-446655440003', tag: 'Health' },
          { article_id: '850e8400-e29b-41d4-a716-446655440004', tag: 'Cryptocurrency' },
          { article_id: '850e8400-e29b-41d4-a716-446655440004', tag: 'Finance' }
        ]);

      if (articleTagsError) {
        console.error("❌ Error seeding article tags:", articleTagsError);
        throw articleTagsError;
      }
    }

    console.log("✅ Database seeding completed successfully!");
    return { success: true, message: "Database seeded successfully" };

  } catch (error) {
    console.error("❌ Database seeding failed:", error);
    return { success: false, error: error.message };
  }
}
