import React from 'react';
import { Button, ButtonProps } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LoadingButtonProps extends ButtonProps {
  loading?: boolean;
  loadingText?: string;
  children: React.ReactNode;
}

export function LoadingButton({
  loading = false,
  loadingText,
  children,
  disabled,
  className,
  ...props
}: LoadingButtonProps) {
  return (
    <Button
      disabled={disabled || loading}
      className={cn(
        'relative transition-all duration-200',
        loading && 'cursor-not-allowed',
        className
      )}
      {...props}
    >
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <Loader2 className="h-4 w-4 animate-spin" />
          {loadingText && (
            <span className="ml-2 text-sm">{loadingText}</span>
          )}
        </div>
      )}
      <div className={cn(
        'flex items-center justify-center transition-opacity duration-200',
        loading ? 'opacity-0' : 'opacity-100'
      )}>
        {children}
      </div>
    </Button>
  );
}

// Enhanced loading button with more visual feedback
export function EnhancedLoadingButton({
  loading = false,
  loadingText = 'Loading...',
  children,
  disabled,
  className,
  variant = 'default',
  ...props
}: LoadingButtonProps) {
  return (
    <Button
      disabled={disabled || loading}
      variant={variant}
      className={cn(
        'relative overflow-hidden transition-all duration-300 transform',
        loading && 'scale-95 cursor-not-allowed',
        !loading && 'hover:scale-105 active:scale-95',
        className
      )}
      {...props}
    >
      {/* Loading overlay */}
      {loading && (
        <div className="absolute inset-0 bg-current opacity-10 animate-pulse" />
      )}
      
      {/* Loading spinner and text */}
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <Loader2 className="h-4 w-4 animate-spin mr-2" />
          <span className="text-sm font-medium">{loadingText}</span>
        </div>
      )}
      
      {/* Button content */}
      <div className={cn(
        'flex items-center justify-center transition-all duration-300',
        loading ? 'opacity-0 scale-90' : 'opacity-100 scale-100'
      )}>
        {children}
      </div>
    </Button>
  );
}

// Pulse loading button for special actions
export function PulseLoadingButton({
  loading = false,
  loadingText,
  children,
  disabled,
  className,
  ...props
}: LoadingButtonProps) {
  return (
    <Button
      disabled={disabled || loading}
      className={cn(
        'relative transition-all duration-200',
        loading && 'animate-pulse cursor-not-allowed',
        className
      )}
      {...props}
    >
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-current bg-opacity-20 rounded-md">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
            <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
            <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
          </div>
          {loadingText && (
            <span className="ml-3 text-sm font-medium">{loadingText}</span>
          )}
        </div>
      )}
      <div className={cn(
        'transition-opacity duration-200',
        loading ? 'opacity-0' : 'opacity-100'
      )}>
        {children}
      </div>
    </Button>
  );
}
