@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%; /* Blue */
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%; /* Blue */
    --radius: 0.5rem;
  }

  .dark {
    /* Dark theme colors with gold accents */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 45 93% 58%; /* Gold */
    --primary-foreground: 26 83% 14%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 45 93% 58%; /* Gold */
    --accent-foreground: 26 83% 14%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 45 93% 58%; /* Gold */
  }
}

@layer base {
  * {
    @apply border-border;
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
  }

  body {
    @apply bg-background text-foreground;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Enhanced focus styles for dark mode */
  .dark *:focus-visible {
    @apply ring-gold-500 ring-offset-background;
  }

  /* Custom selection colors */
  ::selection {
    @apply bg-primary/20 text-primary-foreground;
  }

  .dark ::selection {
    @apply bg-gold-500/30 text-gold-900;
  }

  /* Gold glow effect for dark mode interactive elements */
  .dark .gold-glow {
    box-shadow: 0 0 10px rgba(245, 158, 11, 0.3);
  }

  .dark .gold-glow:hover {
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.5);
  }
}

/* Newspaper-style typography */
.article-title {
  font-family: Georgia, "Times New Roman", Times, serif;
  letter-spacing: -0.02em;
}

.article-body {
  font-family: Georgia, "Times New Roman", Times, serif;
  line-height: 1.7;
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
