// Enterprise Error Tracking System
// Comprehensive error monitoring and reporting

export interface ErrorContext {
  userId?: string;
  userEmail?: string;
  sessionId?: string;
  url?: string;
  userAgent?: string;
  timestamp?: string;
  buildVersion?: string;
  environment?: string;
  component?: string;
  action?: string;
  metadata?: Record<string, any>;
}

export interface ErrorReport {
  id: string;
  message: string;
  stack?: string;
  type: 'javascript' | 'network' | 'validation' | 'auth' | 'payment' | 'custom';
  severity: 'low' | 'medium' | 'high' | 'critical';
  context: ErrorContext;
  fingerprint: string;
  count: number;
  firstSeen: string;
  lastSeen: string;
  resolved: boolean;
  tags: string[];
}

export interface NetworkError {
  url: string;
  method: string;
  status: number;
  statusText: string;
  responseTime: number;
  requestId?: string;
}

export interface PerformanceMetric {
  name: string;
  value: number;
  unit: 'ms' | 'bytes' | 'count' | 'percentage';
  timestamp: string;
  context?: Record<string, any>;
}

class ErrorTracker {
  private errors: Map<string, ErrorReport> = new Map();
  private context: ErrorContext = {};
  private isEnabled: boolean = true;
  private maxErrors: number = 1000;
  private reportingEndpoint?: string;
  private onError?: (error: ErrorReport) => void;

  constructor(config?: {
    enabled?: boolean;
    maxErrors?: number;
    reportingEndpoint?: string;
    onError?: (error: ErrorReport) => void;
  }) {
    this.isEnabled = config?.enabled ?? true;
    this.maxErrors = config?.maxErrors ?? 1000;
    this.reportingEndpoint = config?.reportingEndpoint;
    this.onError = config?.onError;

    if (this.isEnabled) {
      this.setupGlobalErrorHandlers();
      this.setupUnhandledRejectionHandler();
      this.setupNetworkErrorTracking();
    }
  }

  // Set global context for all errors
  setContext(context: Partial<ErrorContext>) {
    this.context = { ...this.context, ...context };
  }

  // Update user context
  setUser(userId: string, email?: string) {
    this.context.userId = userId;
    this.context.userEmail = email;
  }

  // Clear user context (on logout)
  clearUser() {
    delete this.context.userId;
    delete this.context.userEmail;
  }

  // Manually capture an error
  captureError(
    error: Error | string,
    context?: Partial<ErrorContext>,
    severity: ErrorReport['severity'] = 'medium'
  ) {
    if (!this.isEnabled) return;

    const errorMessage = typeof error === 'string' ? error : error.message;
    const stack = typeof error === 'object' ? error.stack : undefined;
    
    const errorReport = this.createErrorReport(
      errorMessage,
      stack,
      'custom',
      severity,
      context
    );

    this.processError(errorReport);
  }

  // Capture network errors
  captureNetworkError(networkError: NetworkError, context?: Partial<ErrorContext>) {
    const message = `Network Error: ${networkError.method} ${networkError.url} - ${networkError.status} ${networkError.statusText}`;
    
    const errorReport = this.createErrorReport(
      message,
      undefined,
      'network',
      networkError.status >= 500 ? 'high' : 'medium',
      {
        ...context,
        metadata: {
          ...context?.metadata,
          networkError,
        },
      }
    );

    this.processError(errorReport);
  }

  // Capture validation errors
  captureValidationError(
    field: string,
    message: string,
    value?: any,
    context?: Partial<ErrorContext>
  ) {
    const errorMessage = `Validation Error: ${field} - ${message}`;
    
    const errorReport = this.createErrorReport(
      errorMessage,
      undefined,
      'validation',
      'low',
      {
        ...context,
        metadata: {
          ...context?.metadata,
          field,
          value,
        },
      }
    );

    this.processError(errorReport);
  }

  // Capture authentication errors
  captureAuthError(message: string, context?: Partial<ErrorContext>) {
    const errorReport = this.createErrorReport(
      `Auth Error: ${message}`,
      undefined,
      'auth',
      'high',
      context
    );

    this.processError(errorReport);
  }

  // Capture payment errors
  capturePaymentError(message: string, context?: Partial<ErrorContext>) {
    const errorReport = this.createErrorReport(
      `Payment Error: ${message}`,
      undefined,
      'payment',
      'critical',
      context
    );

    this.processError(errorReport);
  }

  // Get error statistics
  getErrorStats() {
    const errors = Array.from(this.errors.values());
    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    return {
      total: errors.length,
      unresolved: errors.filter(e => !e.resolved).length,
      critical: errors.filter(e => e.severity === 'critical').length,
      last24Hours: errors.filter(e => new Date(e.lastSeen) > last24Hours).length,
      byType: this.groupBy(errors, 'type'),
      bySeverity: this.groupBy(errors, 'severity'),
      topErrors: errors
        .sort((a, b) => b.count - a.count)
        .slice(0, 10)
        .map(e => ({ message: e.message, count: e.count, severity: e.severity })),
    };
  }

  // Get all errors
  getErrors(filters?: {
    type?: ErrorReport['type'];
    severity?: ErrorReport['severity'];
    resolved?: boolean;
    limit?: number;
  }) {
    let errors = Array.from(this.errors.values());

    if (filters?.type) {
      errors = errors.filter(e => e.type === filters.type);
    }
    if (filters?.severity) {
      errors = errors.filter(e => e.severity === filters.severity);
    }
    if (filters?.resolved !== undefined) {
      errors = errors.filter(e => e.resolved === filters.resolved);
    }

    errors.sort((a, b) => new Date(b.lastSeen).getTime() - new Date(a.lastSeen).getTime());

    if (filters?.limit) {
      errors = errors.slice(0, filters.limit);
    }

    return errors;
  }

  // Mark error as resolved
  resolveError(errorId: string) {
    const error = this.errors.get(errorId);
    if (error) {
      error.resolved = true;
      this.errors.set(errorId, error);
    }
  }

  // Clear all errors
  clearErrors() {
    this.errors.clear();
  }

  // Private methods
  private setupGlobalErrorHandlers() {
    window.addEventListener('error', (event) => {
      const errorReport = this.createErrorReport(
        event.message,
        event.error?.stack,
        'javascript',
        'high',
        {
          url: event.filename,
          metadata: {
            lineno: event.lineno,
            colno: event.colno,
          },
        }
      );
      this.processError(errorReport);
    });
  }

  private setupUnhandledRejectionHandler() {
    window.addEventListener('unhandledrejection', (event) => {
      const error = event.reason;
      const message = error instanceof Error ? error.message : String(error);
      const stack = error instanceof Error ? error.stack : undefined;

      const errorReport = this.createErrorReport(
        `Unhandled Promise Rejection: ${message}`,
        stack,
        'javascript',
        'high'
      );
      this.processError(errorReport);
    });
  }

  private setupNetworkErrorTracking() {
    // Intercept fetch requests
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const startTime = performance.now();
      const url = typeof args[0] === 'string' ? args[0] : args[0].url;
      const method = args[1]?.method || 'GET';

      try {
        const response = await originalFetch(...args);
        const endTime = performance.now();

        if (!response.ok) {
          this.captureNetworkError({
            url,
            method,
            status: response.status,
            statusText: response.statusText,
            responseTime: endTime - startTime,
          });
        }

        return response;
      } catch (error) {
        const endTime = performance.now();
        this.captureNetworkError({
          url,
          method,
          status: 0,
          statusText: 'Network Error',
          responseTime: endTime - startTime,
        });
        throw error;
      }
    };
  }

  private createErrorReport(
    message: string,
    stack?: string,
    type: ErrorReport['type'] = 'custom',
    severity: ErrorReport['severity'] = 'medium',
    additionalContext?: Partial<ErrorContext>
  ): ErrorReport {
    const fingerprint = this.generateFingerprint(message, stack);
    const timestamp = new Date().toISOString();
    
    const existingError = this.errors.get(fingerprint);
    if (existingError) {
      existingError.count++;
      existingError.lastSeen = timestamp;
      return existingError;
    }

    return {
      id: this.generateId(),
      message,
      stack,
      type,
      severity,
      context: {
        ...this.context,
        ...additionalContext,
        timestamp,
        url: window.location.href,
        userAgent: navigator.userAgent,
        environment: import.meta.env.MODE,
        buildVersion: import.meta.env.VITE_BUILD_VERSION || 'unknown',
      },
      fingerprint,
      count: 1,
      firstSeen: timestamp,
      lastSeen: timestamp,
      resolved: false,
      tags: this.generateTags(type, severity),
    };
  }

  private processError(errorReport: ErrorReport) {
    // Store error
    this.errors.set(errorReport.fingerprint, errorReport);

    // Cleanup old errors if we exceed the limit
    if (this.errors.size > this.maxErrors) {
      const oldestError = Array.from(this.errors.values())
        .sort((a, b) => new Date(a.firstSeen).getTime() - new Date(b.firstSeen).getTime())[0];
      this.errors.delete(oldestError.fingerprint);
    }

    // Call error callback
    if (this.onError) {
      this.onError(errorReport);
    }

    // Report to external service
    if (this.reportingEndpoint) {
      this.reportToService(errorReport);
    }

    // Log to console in development
    if (import.meta.env.DEV) {
      console.error('Error tracked:', errorReport);
    }
  }

  private async reportToService(errorReport: ErrorReport) {
    try {
      await fetch(this.reportingEndpoint!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(errorReport),
      });
    } catch (error) {
      console.error('Failed to report error to service:', error);
    }
  }

  private generateFingerprint(message: string, stack?: string): string {
    const content = stack || message;
    // Simple hash function for fingerprinting
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private generateTags(type: ErrorReport['type'], severity: ErrorReport['severity']): string[] {
    const tags = [type, severity];
    
    if (this.context.userId) {
      tags.push('authenticated');
    }
    
    if (this.context.environment) {
      tags.push(this.context.environment);
    }

    return tags;
  }

  private groupBy<T>(array: T[], key: keyof T): Record<string, number> {
    return array.reduce((acc, item) => {
      const value = String(item[key]);
      acc[value] = (acc[value] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }
}

// Global error tracker instance
export const errorTracker = new ErrorTracker({
  enabled: true,
  maxErrors: 1000,
  reportingEndpoint: import.meta.env.VITE_ERROR_REPORTING_ENDPOINT,
});

// React Error Boundary integration
export const captureComponentError = (error: Error, errorInfo: any) => {
  errorTracker.captureError(error, {
    component: errorInfo.componentStack,
    metadata: { errorInfo },
  }, 'high');
};

// Utility functions
export const withErrorTracking = <T extends (...args: any[]) => any>(
  fn: T,
  context?: Partial<ErrorContext>
): T => {
  return ((...args: any[]) => {
    try {
      const result = fn(...args);
      if (result instanceof Promise) {
        return result.catch((error) => {
          errorTracker.captureError(error, context);
          throw error;
        });
      }
      return result;
    } catch (error) {
      errorTracker.captureError(error as Error, context);
      throw error;
    }
  }) as T;
};

export default errorTracker;
