// CloudFlare CDN Configuration for The Chronicle
// Advanced caching, security, and performance optimization

// CloudFlare Worker for advanced edge logic
addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request));
});

/**
 * Main request handler with intelligent caching and security
 */
async function handleRequest(request) {
  const url = new URL(request.url);
  const cache = caches.default;
  
  // Security checks
  if (await isBlocked(request)) {
    return new Response('Access Denied', { status: 403 });
  }
  
  // Rate limiting
  if (await isRateLimited(request)) {
    return new Response('Rate Limited', { 
      status: 429,
      headers: {
        'Retry-After': '60',
        'X-RateLimit-Limit': '100',
        'X-RateLimit-Remaining': '0'
      }
    });
  }
  
  // Handle different request types
  if (url.pathname.startsWith('/api/')) {
    return handleApiRequest(request, cache);
  } else if (url.pathname.startsWith('/assets/')) {
    return handleStaticAssets(request, cache);
  } else if (url.pathname.match(/\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/)) {
    return handleStaticAssets(request, cache);
  } else {
    return handlePageRequest(request, cache);
  }
}

/**
 * Handle API requests with intelligent caching
 */
async function handleApiRequest(request, cache) {
  const url = new URL(request.url);
  const method = request.method;
  
  // Only cache GET requests
  if (method !== 'GET') {
    return fetch(request);
  }
  
  // Check for authentication - don't cache authenticated requests
  if (request.headers.get('Authorization')) {
    return fetch(request);
  }
  
  // Create cache key
  const cacheKey = new Request(url.toString(), request);
  
  // Check cache first
  let response = await cache.match(cacheKey);
  
  if (!response) {
    // Fetch from origin
    response = await fetch(request);
    
    // Only cache successful responses
    if (response.status === 200) {
      // Clone response for caching
      const responseToCache = response.clone();
      
      // Set cache headers based on endpoint
      const cacheHeaders = getCacheHeaders(url.pathname, 'api');
      
      // Create new response with cache headers
      const cachedResponse = new Response(responseToCache.body, {
        status: responseToCache.status,
        statusText: responseToCache.statusText,
        headers: {
          ...Object.fromEntries(responseToCache.headers),
          ...cacheHeaders,
          'X-Cache': 'MISS',
          'X-Cache-Date': new Date().toISOString()
        }
      });
      
      // Cache the response
      event.waitUntil(cache.put(cacheKey, cachedResponse.clone()));
      
      return cachedResponse;
    }
  } else {
    // Add cache hit header
    const newHeaders = new Headers(response.headers);
    newHeaders.set('X-Cache', 'HIT');
    
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: newHeaders
    });
  }
  
  return response;
}

/**
 * Handle static assets with aggressive caching
 */
async function handleStaticAssets(request, cache) {
  const url = new URL(request.url);
  
  // Create cache key
  const cacheKey = new Request(url.toString(), {
    method: 'GET',
    headers: request.headers
  });
  
  // Check cache first
  let response = await cache.match(cacheKey);
  
  if (!response) {
    // Fetch from origin
    response = await fetch(request);
    
    if (response.status === 200) {
      // Clone for caching
      const responseToCache = response.clone();
      
      // Get cache headers for static assets
      const cacheHeaders = getCacheHeaders(url.pathname, 'static');
      
      // Create cached response
      const cachedResponse = new Response(responseToCache.body, {
        status: responseToCache.status,
        statusText: responseToCache.statusText,
        headers: {
          ...Object.fromEntries(responseToCache.headers),
          ...cacheHeaders,
          'X-Cache': 'MISS'
        }
      });
      
      // Cache for a long time
      event.waitUntil(cache.put(cacheKey, cachedResponse.clone()));
      
      return cachedResponse;
    }
  } else {
    // Add cache hit header
    const newHeaders = new Headers(response.headers);
    newHeaders.set('X-Cache', 'HIT');
    
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: newHeaders
    });
  }
  
  return response;
}

/**
 * Handle page requests with smart caching
 */
async function handlePageRequest(request, cache) {
  const url = new URL(request.url);
  
  // Don't cache authenticated requests
  if (request.headers.get('Authorization') || request.headers.get('Cookie')) {
    return fetch(request);
  }
  
  // Create cache key
  const cacheKey = new Request(url.toString(), {
    method: 'GET'
  });
  
  // Check cache
  let response = await cache.match(cacheKey);
  
  if (!response) {
    // Fetch from origin
    response = await fetch(request);
    
    if (response.status === 200) {
      const responseToCache = response.clone();
      const cacheHeaders = getCacheHeaders(url.pathname, 'page');
      
      const cachedResponse = new Response(responseToCache.body, {
        status: responseToCache.status,
        statusText: responseToCache.statusText,
        headers: {
          ...Object.fromEntries(responseToCache.headers),
          ...cacheHeaders,
          'X-Cache': 'MISS'
        }
      });
      
      event.waitUntil(cache.put(cacheKey, cachedResponse.clone()));
      
      return cachedResponse;
    }
  } else {
    const newHeaders = new Headers(response.headers);
    newHeaders.set('X-Cache', 'HIT');
    
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: newHeaders
    });
  }
  
  return response;
}

/**
 * Get appropriate cache headers based on content type
 */
function getCacheHeaders(pathname, type) {
  const headers = {};
  
  switch (type) {
    case 'static':
      // Static assets - cache for 1 year
      headers['Cache-Control'] = 'public, max-age=31536000, immutable';
      headers['Expires'] = new Date(Date.now() + 31536000000).toUTCString();
      break;
      
    case 'api':
      // API responses - cache for 5 minutes
      if (pathname.includes('/articles') || pathname.includes('/products')) {
        headers['Cache-Control'] = 'public, max-age=300, s-maxage=300';
      } else {
        headers['Cache-Control'] = 'public, max-age=60, s-maxage=60';
      }
      break;
      
    case 'page':
      // HTML pages - cache for 10 minutes
      headers['Cache-Control'] = 'public, max-age=600, s-maxage=600';
      break;
      
    default:
      headers['Cache-Control'] = 'public, max-age=300';
  }
  
  // Add security headers
  headers['X-Content-Type-Options'] = 'nosniff';
  headers['X-Frame-Options'] = 'SAMEORIGIN';
  headers['X-XSS-Protection'] = '1; mode=block';
  headers['Referrer-Policy'] = 'strict-origin-when-cross-origin';
  
  return headers;
}

/**
 * Security checks for blocking malicious requests
 */
async function isBlocked(request) {
  const url = new URL(request.url);
  const userAgent = request.headers.get('User-Agent') || '';
  const ip = request.headers.get('CF-Connecting-IP');
  
  // Block known bad user agents
  const badUserAgents = [
    'sqlmap',
    'nikto',
    'nessus',
    'openvas',
    'nmap',
    'masscan'
  ];
  
  if (badUserAgents.some(agent => userAgent.toLowerCase().includes(agent))) {
    return true;
  }
  
  // Block suspicious paths
  const suspiciousPaths = [
    '/wp-admin',
    '/wp-login',
    '/.env',
    '/config.php',
    '/admin.php',
    '/phpmyadmin',
    '/.git',
    '/backup'
  ];
  
  if (suspiciousPaths.some(path => url.pathname.includes(path))) {
    return true;
  }
  
  // Block SQL injection attempts
  const sqlPatterns = [
    /union.*select/i,
    /select.*from/i,
    /insert.*into/i,
    /delete.*from/i,
    /drop.*table/i
  ];
  
  const queryString = url.search;
  if (sqlPatterns.some(pattern => pattern.test(queryString))) {
    return true;
  }
  
  return false;
}

/**
 * Rate limiting implementation
 */
async function isRateLimited(request) {
  const ip = request.headers.get('CF-Connecting-IP');
  const url = new URL(request.url);
  
  // Different limits for different endpoints
  let limit = 100; // requests per minute
  let window = 60; // seconds
  
  if (url.pathname.startsWith('/api/auth/')) {
    limit = 10; // Stricter for auth endpoints
  } else if (url.pathname.startsWith('/api/')) {
    limit = 60; // API endpoints
  }
  
  // Use CloudFlare KV for rate limiting (if available)
  if (typeof RATE_LIMIT !== 'undefined') {
    const key = `rate_limit:${ip}:${Math.floor(Date.now() / (window * 1000))}`;
    
    try {
      const current = await RATE_LIMIT.get(key);
      const count = current ? parseInt(current) : 0;
      
      if (count >= limit) {
        return true;
      }
      
      // Increment counter
      await RATE_LIMIT.put(key, (count + 1).toString(), { expirationTtl: window });
    } catch (error) {
      // If KV fails, allow the request
      console.error('Rate limiting error:', error);
    }
  }
  
  return false;
}

/**
 * CloudFlare Page Rules Configuration
 */
const pageRules = [
  {
    url: "thechronicle.com/assets/*",
    settings: {
      cache_level: "cache_everything",
      edge_cache_ttl: 31536000, // 1 year
      browser_cache_ttl: 31536000
    }
  },
  {
    url: "thechronicle.com/api/articles*",
    settings: {
      cache_level: "cache_everything",
      edge_cache_ttl: 300, // 5 minutes
      browser_cache_ttl: 300
    }
  },
  {
    url: "thechronicle.com/api/products*",
    settings: {
      cache_level: "cache_everything",
      edge_cache_ttl: 600, // 10 minutes
      browser_cache_ttl: 300
    }
  },
  {
    url: "thechronicle.com/api/auth/*",
    settings: {
      cache_level: "bypass",
      security_level: "high"
    }
  },
  {
    url: "thechronicle.com/*",
    settings: {
      cache_level: "cache_everything",
      edge_cache_ttl: 600, // 10 minutes
      browser_cache_ttl: 300,
      minify: {
        html: true,
        css: true,
        js: true
      }
    }
  }
];

/**
 * CloudFlare Security Settings
 */
const securitySettings = {
  security_level: "medium",
  challenge_ttl: 1800,
  browser_integrity_check: true,
  hotlink_protection: true,
  ip_geolocation: true,
  email_obfuscation: true,
  server_side_exclude: true,
  waf: true,
  rate_limiting: {
    threshold: 100,
    period: 60,
    action: "challenge"
  },
  ddos_protection: true,
  bot_management: {
    fight_mode: true,
    session_score: true,
    javascript_detections: true
  }
};

/**
 * CloudFlare Performance Settings
 */
const performanceSettings = {
  minify: {
    html: true,
    css: true,
    js: true
  },
  brotli: true,
  early_hints: true,
  http2: true,
  http3: true,
  zero_rtt: true,
  ipv6: true,
  websockets: true,
  pseudo_ipv4: true,
  ip_geolocation: true,
  rocket_loader: false, // Disabled for React apps
  mirage: true,
  polish: "lossy",
  webp: true,
  always_online: true
};

// Export configuration for CloudFlare API
export {
  pageRules,
  securitySettings,
  performanceSettings
};
