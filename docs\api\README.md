# API Documentation

The Chronicle API provides comprehensive endpoints for content management, user authentication, ecommerce functionality, and analytics. Built on Supabase with PostgreSQL and Row Level Security (RLS).

## Base URL

```
Production: https://api.thechronicle.com
Development: http://localhost:3000/api
```

## Authentication

All API requests require authentication using JWT tokens provided by Supabase Auth.

### Headers

```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

### Authentication Flow

```javascript
// Login
const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password'
});

// Use the session token for API requests
const token = data.session?.access_token;
```

## Rate Limiting

- **General API**: 100 requests per minute per IP
- **Authentication**: 10 requests per minute per IP
- **File Uploads**: 20 requests per minute per user

## Error Handling

All API responses follow a consistent error format:

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "issue": "Invalid email format"
    }
  }
}
```

### Error Codes

- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `429` - Rate Limit Exceeded
- `500` - Internal Server Error

## Endpoints

### Authentication

#### POST /auth/signup
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword",
  "firstName": "John",
  "lastName": "Doe"
}
```

**Response:**
```json
{
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "user",
    "createdAt": "2024-01-01T00:00:00Z"
  },
  "session": {
    "access_token": "jwt_token",
    "refresh_token": "refresh_token",
    "expires_at": **********
  }
}
```

#### POST /auth/signin
Authenticate user and get session token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

#### POST /auth/signout
Sign out user and invalidate session.

#### POST /auth/refresh
Refresh expired access token.

**Request Body:**
```json
{
  "refresh_token": "refresh_token"
}
```

#### POST /auth/forgot-password
Send password reset email.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

#### POST /auth/reset-password
Reset password with token.

**Request Body:**
```json
{
  "token": "reset_token",
  "password": "newpassword"
}
```

### Articles

#### GET /articles
Get paginated list of articles.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10, max: 100)
- `category` (string): Filter by category slug
- `tag` (string): Filter by tag
- `author` (string): Filter by author ID
- `featured` (boolean): Filter featured articles
- `published` (boolean): Filter published articles
- `search` (string): Search in title and content

**Response:**
```json
{
  "data": [
    {
      "id": "uuid",
      "title": "Article Title",
      "slug": "article-title",
      "excerpt": "Article excerpt...",
      "content": "Full article content...",
      "featuredImage": "https://example.com/image.jpg",
      "author": {
        "id": "uuid",
        "name": "Author Name",
        "avatar": "https://example.com/avatar.jpg"
      },
      "category": {
        "id": "uuid",
        "name": "Technology",
        "slug": "technology"
      },
      "tags": ["react", "javascript"],
      "isPremium": false,
      "isPublished": true,
      "publishedAt": "2024-01-01T00:00:00Z",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z",
      "readTime": 5,
      "views": 1250,
      "likes": 45,
      "comments": 12
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 150,
    "pages": 15
  }
}
```

#### GET /articles/:id
Get single article by ID.

#### GET /articles/slug/:slug
Get single article by slug.

#### POST /articles
Create new article (Admin/Author only).

**Request Body:**
```json
{
  "title": "New Article Title",
  "content": "Article content...",
  "excerpt": "Article excerpt...",
  "categoryId": "uuid",
  "tags": ["tag1", "tag2"],
  "featuredImage": "https://example.com/image.jpg",
  "isPremium": false,
  "isPublished": true,
  "publishedAt": "2024-01-01T00:00:00Z"
}
```

#### PUT /articles/:id
Update article (Admin/Author only).

#### DELETE /articles/:id
Delete article (Admin only).

#### POST /articles/:id/like
Like/unlike article.

#### POST /articles/:id/view
Track article view.

### Products

#### GET /products
Get paginated list of products.

**Query Parameters:**
- `page` (number): Page number
- `limit` (number): Items per page
- `category` (string): Filter by category
- `type` (string): Filter by product type (digital, physical)
- `featured` (boolean): Filter featured products
- `minPrice` (number): Minimum price filter
- `maxPrice` (number): Maximum price filter
- `search` (string): Search in name and description

**Response:**
```json
{
  "data": [
    {
      "id": "uuid",
      "name": "Product Name",
      "slug": "product-name",
      "description": "Product description...",
      "shortDescription": "Short description...",
      "price": 29.99,
      "comparePrice": 39.99,
      "type": "digital",
      "category": {
        "id": "uuid",
        "name": "Digital Products",
        "slug": "digital-products"
      },
      "images": [
        "https://example.com/product1.jpg",
        "https://example.com/product2.jpg"
      ],
      "stock": 100,
      "status": "active",
      "featured": true,
      "tags": ["ebook", "guide"],
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 50,
    "pages": 5
  }
}
```

#### GET /products/:id
Get single product by ID.

#### GET /products/slug/:slug
Get single product by slug.

#### POST /products
Create new product (Admin only).

#### PUT /products/:id
Update product (Admin only).

#### DELETE /products/:id
Delete product (Admin only).

### Orders

#### GET /orders
Get user's orders.

#### GET /orders/:id
Get single order by ID.

#### POST /orders
Create new order.

**Request Body:**
```json
{
  "items": [
    {
      "productId": "uuid",
      "quantity": 1,
      "price": 29.99
    }
  ],
  "shippingAddress": {
    "name": "John Doe",
    "address": "123 Main St",
    "city": "New York",
    "state": "NY",
    "zipCode": "10001",
    "country": "US"
  },
  "paymentMethodId": "stripe_payment_method_id"
}
```

#### PUT /orders/:id/status
Update order status (Admin only).

### Categories

#### GET /categories
Get all categories.

#### GET /categories/:id
Get single category.

#### POST /categories
Create category (Admin only).

#### PUT /categories/:id
Update category (Admin only).

#### DELETE /categories/:id
Delete category (Admin only).

### Users

#### GET /users/profile
Get current user profile.

#### PUT /users/profile
Update user profile.

#### GET /users/:id
Get user by ID (Admin only).

#### GET /users
Get paginated list of users (Admin only).

#### PUT /users/:id/role
Update user role (Admin only).

#### DELETE /users/:id
Delete user (Admin only).

### Analytics

#### GET /analytics/overview
Get analytics overview (Admin only).

#### GET /analytics/articles
Get article analytics (Admin only).

#### GET /analytics/products
Get product analytics (Admin only).

#### GET /analytics/users
Get user analytics (Admin only).

### File Upload

#### POST /upload/image
Upload image file.

**Request:** Multipart form data with `file` field.

**Response:**
```json
{
  "url": "https://storage.supabase.co/object/public/images/filename.jpg",
  "filename": "filename.jpg",
  "size": 1024000,
  "mimeType": "image/jpeg"
}
```

#### POST /upload/document
Upload document file.

#### DELETE /upload/:filename
Delete uploaded file.

## Webhooks

### Stripe Webhooks

#### POST /webhooks/stripe
Handle Stripe webhook events.

**Events Handled:**
- `payment_intent.succeeded`
- `payment_intent.payment_failed`
- `customer.subscription.created`
- `customer.subscription.updated`
- `customer.subscription.deleted`
- `invoice.payment_succeeded`
- `invoice.payment_failed`

## SDKs and Libraries

### JavaScript/TypeScript

```bash
npm install @thechronicle/api-client
```

```javascript
import { ChronicleAPI } from '@thechronicle/api-client';

const api = new ChronicleAPI({
  baseURL: 'https://api.thechronicle.com',
  apiKey: 'your_api_key'
});

// Get articles
const articles = await api.articles.list({
  page: 1,
  limit: 10,
  category: 'technology'
});

// Create article
const newArticle = await api.articles.create({
  title: 'New Article',
  content: 'Article content...',
  categoryId: 'category-uuid'
});
```

### Python

```bash
pip install chronicle-api
```

```python
from chronicle_api import ChronicleAPI

api = ChronicleAPI(
    base_url='https://api.thechronicle.com',
    api_key='your_api_key'
)

# Get articles
articles = api.articles.list(page=1, limit=10, category='technology')

# Create article
new_article = api.articles.create({
    'title': 'New Article',
    'content': 'Article content...',
    'category_id': 'category-uuid'
})
```

## Testing

### Postman Collection

Import our Postman collection for easy API testing:

[Download Postman Collection](./postman/chronicle-api.json)

### Test Environment

Use our test environment for development:

```
Base URL: https://api-test.thechronicle.com
Test API Key: test_key_123456789
```

## Support

For API support and questions:

- **Documentation**: [https://docs.thechronicle.com](https://docs.thechronicle.com)
- **Support Email**: <EMAIL>
- **Discord**: [Join our Discord](https://discord.gg/thechronicle)
- **GitHub Issues**: [Report issues](https://github.com/thechronicle/app/issues)
