import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { RichTextEditor } from '@/components/ui/rich-text-editor';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { ArrowLeft, Save, Send } from 'lucide-react';
import { useAuth } from '../../../../supabase/auth';
import { blogService } from '@/lib/supabase-services';
import { supabase } from '../../../../supabase/supabase';

interface ArticleData {
  title: string;
  excerpt: string;
  content: string;
  tags: string[];
  category: string;
  isPremium: boolean;
  featuredImage?: string;
  seoTitle?: string;
  seoDescription?: string;
}

export function CreateArticle() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();
  const [categories, setCategories] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      const categoriesData = await blogService.getCategories();
      setCategories(categoriesData);
    } catch (error) {
      console.error('Error loading categories:', error);
      toast({
        title: 'Error',
        description: 'Failed to load categories',
        variant: 'destructive',
      });
    }
  };

  const handleSave = async (data: ArticleData) => {
    if (!user) {
      toast({
        title: 'Error',
        description: 'You must be logged in to create articles',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSaving(true);

      // Create article in database
      const { data: article, error } = await supabase
        .from('articles')
        .insert({
          title: data.title,
          slug: generateSlug(data.title),
          excerpt: data.excerpt,
          content: data.content,
          author_id: user.id,
          category_id: data.category,
          featured_image: data.featuredImage,
          is_premium: data.isPremium,
          is_published: false, // Save as draft
          seo_title: data.seoTitle,
          seo_description: data.seoDescription,
          read_time: calculateReadTime(data.content),
        })
        .select()
        .single();

      if (error) throw error;

      // Add tags
      if (data.tags.length > 0) {
        const tagInserts = data.tags.map(tag => ({
          article_id: article.id,
          tag: tag,
        }));

        const { error: tagsError } = await supabase
          .from('article_tags')
          .insert(tagInserts);

        if (tagsError) {
          console.error('Error adding tags:', tagsError);
        }
      }

      toast({
        title: 'Success!',
        description: 'Article saved as draft',
      });

      navigate('/dashboard/articles');
    } catch (error) {
      console.error('Error saving article:', error);
      toast({
        title: 'Error',
        description: 'Failed to save article',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handlePublish = async (data: ArticleData) => {
    if (!user) {
      toast({
        title: 'Error',
        description: 'You must be logged in to publish articles',
        variant: 'destructive',
      });
      return;
    }

    if (!data.title || !data.content || !data.excerpt) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsLoading(true);

      // Create and publish article
      const { data: article, error } = await supabase
        .from('articles')
        .insert({
          title: data.title,
          slug: generateSlug(data.title),
          excerpt: data.excerpt,
          content: data.content,
          author_id: user.id,
          category_id: data.category,
          featured_image: data.featuredImage,
          is_premium: data.isPremium,
          is_published: true,
          published_at: new Date().toISOString(),
          seo_title: data.seoTitle,
          seo_description: data.seoDescription,
          read_time: calculateReadTime(data.content),
        })
        .select()
        .single();

      if (error) throw error;

      // Add tags
      if (data.tags.length > 0) {
        const tagInserts = data.tags.map(tag => ({
          article_id: article.id,
          tag: tag,
        }));

        const { error: tagsError } = await supabase
          .from('article_tags')
          .insert(tagInserts);

        if (tagsError) {
          console.error('Error adding tags:', tagsError);
        }
      }

      toast({
        title: 'Success!',
        description: 'Article published successfully',
      });

      navigate('/dashboard/articles');
    } catch (error) {
      console.error('Error publishing article:', error);
      toast({
        title: 'Error',
        description: 'Failed to publish article',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const generateSlug = (title: string): string => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const calculateReadTime = (content: string): number => {
    const wordsPerMinute = 200;
    const wordCount = content.split(/\s+/).length;
    return Math.ceil(wordCount / wordsPerMinute);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate('/dashboard/articles')}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Create Article</h1>
            <p className="text-gray-600">Write and publish your content</p>
          </div>
        </div>
      </div>

      {/* Editor */}
      <RichTextEditor
        categories={categories}
        onSave={handleSave}
        onPublish={handlePublish}
        isLoading={isLoading || isSaving}
      />

      {/* Tips Card */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Writing Tips</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Content Guidelines</h4>
              <ul className="space-y-1">
                <li>• Write engaging headlines that grab attention</li>
                <li>• Use clear and concise language</li>
                <li>• Include relevant images and media</li>
                <li>• Structure content with headings and lists</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">SEO Best Practices</h4>
              <ul className="space-y-1">
                <li>• Include target keywords naturally</li>
                <li>• Write compelling meta descriptions</li>
                <li>• Use descriptive alt text for images</li>
                <li>• Add relevant tags and categories</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
