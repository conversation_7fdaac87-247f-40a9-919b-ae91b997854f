import React, { createContext, useContext, useReducer, useEffect } from "react";
import { Cart, CartItem, Product } from "@/types/ecommerce";
import { useToast } from "@/components/ui/use-toast";

interface CartState {
  cart: Cart;
  isLoading: boolean;
}

type CartAction =
  | { type: "ADD_ITEM"; payload: { product: Product; quantity?: number } }
  | { type: "REMOVE_ITEM"; payload: { productId: string } }
  | { type: "UPDATE_QUANTITY"; payload: { productId: string; quantity: number } }
  | { type: "CLEAR_CART" }
  | { type: "SET_LOADING"; payload: boolean }
  | { type: "LOAD_CART"; payload: Cart };

interface CartContextType extends CartState {
  addItem: (product: Product, quantity?: number) => void;
  removeItem: (productId: string) => void;
  updateQuantity: (productId: string, quantity: number) => void;
  clearCart: () => void;
  getItemCount: () => number;
  getSubtotal: () => number;
  getTotal: () => number;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

const initialCart: Cart = {
  id: "temp-cart",
  items: [],
  subtotal: 0,
  tax: 0,
  shipping: 0,
  total: 0,
  currency: "USD",
  createdAt: new Date(),
  updatedAt: new Date(),
};

function cartReducer(state: CartState, action: CartAction): CartState {
  switch (action.type) {
    case "ADD_ITEM": {
      const { product, quantity = 1 } = action.payload;
      const existingItemIndex = state.cart.items.findIndex(
        (item) => item.productId === product.id
      );

      let newItems: CartItem[];
      
      if (existingItemIndex >= 0) {
        // Update existing item
        newItems = state.cart.items.map((item, index) =>
          index === existingItemIndex
            ? { ...item, quantity: item.quantity + quantity }
            : item
        );
      } else {
        // Add new item
        const newItem: CartItem = {
          id: `cart-item-${Date.now()}`,
          productId: product.id,
          product,
          quantity,
          price: product.price,
          addedAt: new Date(),
        };
        newItems = [...state.cart.items, newItem];
      }

      const subtotal = newItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      const tax = subtotal * 0.08; // 8% tax
      const shipping = subtotal > 50 ? 0 : 9.99; // Free shipping over $50
      const total = subtotal + tax + shipping;

      const updatedCart: Cart = {
        ...state.cart,
        items: newItems,
        subtotal,
        tax,
        shipping,
        total,
        updatedAt: new Date(),
      };

      return {
        ...state,
        cart: updatedCart,
      };
    }

    case "REMOVE_ITEM": {
      const newItems = state.cart.items.filter(
        (item) => item.productId !== action.payload.productId
      );

      const subtotal = newItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      const tax = subtotal * 0.08;
      const shipping = subtotal > 50 ? 0 : 9.99;
      const total = subtotal + tax + shipping;

      const updatedCart: Cart = {
        ...state.cart,
        items: newItems,
        subtotal,
        tax,
        shipping,
        total,
        updatedAt: new Date(),
      };

      return {
        ...state,
        cart: updatedCart,
      };
    }

    case "UPDATE_QUANTITY": {
      const { productId, quantity } = action.payload;
      
      if (quantity <= 0) {
        return cartReducer(state, { type: "REMOVE_ITEM", payload: { productId } });
      }

      const newItems = state.cart.items.map((item) =>
        item.productId === productId ? { ...item, quantity } : item
      );

      const subtotal = newItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      const tax = subtotal * 0.08;
      const shipping = subtotal > 50 ? 0 : 9.99;
      const total = subtotal + tax + shipping;

      const updatedCart: Cart = {
        ...state.cart,
        items: newItems,
        subtotal,
        tax,
        shipping,
        total,
        updatedAt: new Date(),
      };

      return {
        ...state,
        cart: updatedCart,
      };
    }

    case "CLEAR_CART":
      return {
        ...state,
        cart: { ...initialCart, id: state.cart.id },
      };

    case "SET_LOADING":
      return {
        ...state,
        isLoading: action.payload,
      };

    case "LOAD_CART":
      return {
        ...state,
        cart: action.payload,
      };

    default:
      return state;
  }
}

export function CartProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(cartReducer, {
    cart: initialCart,
    isLoading: false,
  });
  
  const { toast } = useToast();

  // Load cart from localStorage on mount
  useEffect(() => {
    const savedCart = localStorage.getItem("cart");
    if (savedCart) {
      try {
        const parsedCart = JSON.parse(savedCart);
        dispatch({ type: "LOAD_CART", payload: parsedCart });
      } catch (error) {
        console.error("Failed to load cart from localStorage:", error);
      }
    }
  }, []);

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem("cart", JSON.stringify(state.cart));
  }, [state.cart]);

  const addItem = (product: Product, quantity = 1) => {
    dispatch({ type: "ADD_ITEM", payload: { product, quantity } });
    toast({
      title: "Added to cart",
      description: `${product.name} has been added to your cart.`,
      duration: 3000,
    });
  };

  const removeItem = (productId: string) => {
    const item = state.cart.items.find(item => item.productId === productId);
    dispatch({ type: "REMOVE_ITEM", payload: { productId } });
    
    if (item) {
      toast({
        title: "Removed from cart",
        description: `${item.product.name} has been removed from your cart.`,
        duration: 3000,
      });
    }
  };

  const updateQuantity = (productId: string, quantity: number) => {
    dispatch({ type: "UPDATE_QUANTITY", payload: { productId, quantity } });
  };

  const clearCart = () => {
    dispatch({ type: "CLEAR_CART" });
    toast({
      title: "Cart cleared",
      description: "All items have been removed from your cart.",
      duration: 3000,
    });
  };

  const getItemCount = () => {
    return state.cart.items.reduce((count, item) => count + item.quantity, 0);
  };

  const getSubtotal = () => state.cart.subtotal;
  const getTotal = () => state.cart.total;

  const value: CartContextType = {
    ...state,
    addItem,
    removeItem,
    updateQuantity,
    clearCart,
    getItemCount,
    getSubtotal,
    getTotal,
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
}

export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error("useCart must be used within a CartProvider");
  }
  return context;
}
