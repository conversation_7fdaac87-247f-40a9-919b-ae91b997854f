# Development Guide

This guide covers setting up a development environment and contributing to The Chronicle.

## Prerequisites

- **Node.js 18+** - [Download](https://nodejs.org/)
- **npm 8+** or **yarn 1.22+**
- **Git** - [Download](https://git-scm.com/)
- **VS Code** (recommended) - [Download](https://code.visualstudio.com/)

## Development Setup

### 1. Clone Repository

```bash
git clone https://github.com/thechronicle/app.git
cd the-chronicle
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Environment Configuration

```bash
# Copy environment template
cp .env.example .env.local

# Edit environment variables
nano .env.local
```

Required environment variables:

```env
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Stripe Configuration (use test keys)
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key

# Analytics (optional for development)
VITE_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Development Tools
VITE_TEMPO=true  # Enable Tempo devtools
```

### 4. Database Setup

```bash
# Run database migrations
npm run db:migrate

# Seed database with sample data
npm run db:seed
```

### 5. Start Development Server

```bash
npm run dev
```

The application will be available at [http://localhost:5173](http://localhost:5173).

## Project Structure

```
the-chronicle/
├── 📁 .github/              # GitHub workflows and templates
├── 📁 docs/                 # Documentation
├── 📁 public/               # Static assets
├── 📁 scripts/              # Build and deployment scripts
├── 📁 src/                  # Source code
│   ├── 📁 components/       # React components
│   │   ├── auth/            # Authentication components
│   │   ├── admin/           # Admin dashboard components
│   │   ├── ecommerce/       # Shopping components
│   │   ├── pages/           # Page components
│   │   └── ui/              # Base UI components
│   ├── 📁 hooks/            # Custom React hooks
│   ├── 📁 utils/            # Utility functions
│   ├── 📁 types/            # TypeScript definitions
│   ├── 📁 styles/           # Global styles
│   └── 📁 supabase/         # Database client
├── 📁 supabase/             # Database schema
├── 📁 test/                 # Test files
├── 📄 package.json          # Dependencies and scripts
├── 📄 tsconfig.json         # TypeScript configuration
├── 📄 tailwind.config.js    # Tailwind CSS configuration
└── 📄 vite.config.ts        # Vite configuration
```

## Development Workflow

### 1. Feature Development

```bash
# Create feature branch
git checkout -b feature/new-feature

# Make changes and commit
git add .
git commit -m "feat: add new feature"

# Push branch
git push origin feature/new-feature

# Create pull request on GitHub
```

### 2. Code Quality

```bash
# Run type checking
npm run type-check

# Run linting
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format
```

### 3. Testing

```bash
# Run unit tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run integration tests
npm run test:integration

# Run E2E tests
npm run test:e2e
```

## Component Development

### Component Structure

```typescript
// components/example/ExampleComponent.tsx
import React from 'react';
import { cn } from '@/lib/utils';

interface ExampleComponentProps {
  title: string;
  description?: string;
  className?: string;
  children?: React.ReactNode;
}

export function ExampleComponent({
  title,
  description,
  className,
  children,
}: ExampleComponentProps) {
  return (
    <div className={cn('example-component', className)}>
      <h2 className="text-xl font-semibold">{title}</h2>
      {description && (
        <p className="text-gray-600">{description}</p>
      )}
      {children}
    </div>
  );
}

export default ExampleComponent;
```

### Component Testing

```typescript
// components/example/ExampleComponent.test.tsx
import { render, screen } from '@testing-library/react';
import { ExampleComponent } from './ExampleComponent';

describe('ExampleComponent', () => {
  it('renders title correctly', () => {
    render(<ExampleComponent title="Test Title" />);
    expect(screen.getByText('Test Title')).toBeInTheDocument();
  });

  it('renders description when provided', () => {
    render(
      <ExampleComponent 
        title="Test Title" 
        description="Test Description" 
      />
    );
    expect(screen.getByText('Test Description')).toBeInTheDocument();
  });
});
```

## Custom Hooks

### Hook Development

```typescript
// hooks/useExample.ts
import { useState, useEffect } from 'react';

interface UseExampleOptions {
  initialValue?: string;
  autoUpdate?: boolean;
}

export function useExample(options: UseExampleOptions = {}) {
  const { initialValue = '', autoUpdate = false } = options;
  const [value, setValue] = useState(initialValue);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (autoUpdate) {
      // Auto-update logic
    }
  }, [autoUpdate]);

  const updateValue = async (newValue: string) => {
    setLoading(true);
    setError(null);
    
    try {
      // Update logic
      setValue(newValue);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return {
    value,
    loading,
    error,
    updateValue,
  };
}
```

### Hook Testing

```typescript
// hooks/useExample.test.ts
import { renderHook, act } from '@testing-library/react';
import { useExample } from './useExample';

describe('useExample', () => {
  it('initializes with default value', () => {
    const { result } = renderHook(() => useExample());
    expect(result.current.value).toBe('');
  });

  it('updates value correctly', async () => {
    const { result } = renderHook(() => useExample());
    
    await act(async () => {
      await result.current.updateValue('new value');
    });
    
    expect(result.current.value).toBe('new value');
  });
});
```

## Database Development

### Schema Changes

1. **Create Migration**
   ```bash
   # Create new migration file
   supabase migration new add_new_table
   ```

2. **Write Migration**
   ```sql
   -- supabase/migrations/20240101000000_add_new_table.sql
   CREATE TABLE new_table (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     name TEXT NOT NULL,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );

   -- Enable RLS
   ALTER TABLE new_table ENABLE ROW LEVEL SECURITY;

   -- Create policies
   CREATE POLICY "Users can view their own records" ON new_table
     FOR SELECT USING (auth.uid() = user_id);
   ```

3. **Apply Migration**
   ```bash
   supabase db push
   ```

4. **Generate Types**
   ```bash
   npm run types:supabase
   ```

### Database Queries

```typescript
// utils/database.ts
import { supabase } from '@/supabase/client';

export async function getArticles(options: {
  page?: number;
  limit?: number;
  category?: string;
}) {
  const { page = 1, limit = 10, category } = options;
  const offset = (page - 1) * limit;

  let query = supabase
    .from('articles')
    .select(`
      *,
      author:profiles(*),
      category:categories(*)
    `)
    .eq('is_published', true)
    .order('published_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (category) {
    query = query.eq('category.slug', category);
  }

  const { data, error, count } = await query;

  if (error) throw error;

  return {
    data,
    pagination: {
      page,
      limit,
      total: count || 0,
      pages: Math.ceil((count || 0) / limit),
    },
  };
}
```

## Styling Guidelines

### Tailwind CSS

Use Tailwind CSS utility classes for styling:

```tsx
// Good: Utility classes
<div className="flex items-center justify-between p-4 bg-white rounded-lg shadow-md">
  <h2 className="text-xl font-semibold text-gray-900">Title</h2>
  <button className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
    Action
  </button>
</div>

// Use cn() utility for conditional classes
<div className={cn(
  'base-classes',
  isActive && 'active-classes',
  className
)}>
  Content
</div>
```

### Custom Components

For reusable UI components, use the shadcn/ui pattern:

```tsx
// components/ui/button.tsx
import { cn } from '@/lib/utils';
import { VariantProps, cva } from 'class-variance-authority';

const buttonVariants = cva(
  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-input hover:bg-accent hover:text-accent-foreground',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {}

export function Button({ className, variant, size, ...props }: ButtonProps) {
  return (
    <button
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  );
}
```

## State Management

### Context Pattern

```typescript
// contexts/ExampleContext.tsx
import React, { createContext, useContext, useReducer } from 'react';

interface ExampleState {
  items: string[];
  loading: boolean;
  error: string | null;
}

type ExampleAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ITEMS'; payload: string[] }
  | { type: 'SET_ERROR'; payload: string | null };

const ExampleContext = createContext<{
  state: ExampleState;
  dispatch: React.Dispatch<ExampleAction>;
} | null>(null);

function exampleReducer(state: ExampleState, action: ExampleAction): ExampleState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ITEMS':
      return { ...state, items: action.payload, loading: false };
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    default:
      return state;
  }
}

export function ExampleProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(exampleReducer, {
    items: [],
    loading: false,
    error: null,
  });

  return (
    <ExampleContext.Provider value={{ state, dispatch }}>
      {children}
    </ExampleContext.Provider>
  );
}

export function useExample() {
  const context = useContext(ExampleContext);
  if (!context) {
    throw new Error('useExample must be used within ExampleProvider');
  }
  return context;
}
```

## Error Handling

### Error Boundaries

```typescript
// components/ErrorBoundary.tsx
import React, { Component, ErrorInfo, ReactNode } from 'react';
import { captureComponentError } from '@/utils/errorTracking';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Uncaught error:', error, errorInfo);
    captureComponentError(error, errorInfo);
  }

  public render() {
    if (this.state.hasError) {
      return this.props.fallback || <DefaultErrorFallback />;
    }

    return this.props.children;
  }
}
```

### API Error Handling

```typescript
// utils/api.ts
import { supabase } from '@/supabase/client';
import { errorTracker } from '@/utils/errorTracking';

export async function apiCall<T>(
  operation: () => Promise<{ data: T; error: any }>
): Promise<T> {
  try {
    const { data, error } = await operation();
    
    if (error) {
      errorTracker.captureError(error, {
        context: 'API Call',
        operation: operation.name,
      });
      throw new Error(error.message);
    }
    
    return data;
  } catch (error) {
    errorTracker.captureError(error as Error);
    throw error;
  }
}

// Usage
const articles = await apiCall(() =>
  supabase.from('articles').select('*')
);
```

## Performance Optimization

### Code Splitting

```typescript
// Lazy load components
import { lazy, Suspense } from 'react';

const AdminDashboard = lazy(() => import('@/components/admin/Dashboard'));

function App() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <AdminDashboard />
    </Suspense>
  );
}
```

### Memoization

```typescript
// Memoize expensive calculations
import { useMemo } from 'react';

function ExpensiveComponent({ data }: { data: any[] }) {
  const processedData = useMemo(() => {
    return data.map(item => expensiveProcessing(item));
  }, [data]);

  return <div>{/* Render processed data */}</div>;
}

// Memoize components
import { memo } from 'react';

const OptimizedComponent = memo(function Component({ prop }: { prop: string }) {
  return <div>{prop}</div>;
});
```

## Debugging

### Development Tools

1. **React Developer Tools**
   - Install browser extension
   - Inspect component tree and props

2. **Tempo Devtools**
   - Set `VITE_TEMPO=true` in environment
   - Monitor performance and re-renders

3. **Supabase Dashboard**
   - Monitor database queries
   - Check RLS policies
   - View real-time data

### Debugging Techniques

```typescript
// Debug hooks
import { useEffect } from 'react';

function useDebugValue(value: any, label: string) {
  useEffect(() => {
    console.log(`[${label}]:`, value);
  }, [value, label]);
}

// Debug renders
function DebugRender({ children, name }: { children: React.ReactNode; name: string }) {
  console.log(`Rendering: ${name}`);
  return <>{children}</>;
}
```

## Contributing Guidelines

### Code Style

- Use TypeScript for all new code
- Follow ESLint and Prettier configurations
- Write meaningful commit messages
- Add tests for new features
- Update documentation

### Pull Request Process

1. Create feature branch from `develop`
2. Make changes and add tests
3. Run quality checks: `npm run check`
4. Create pull request with description
5. Address review feedback
6. Merge after approval

### Commit Message Format

```
type(scope): description

feat(auth): add social login support
fix(cart): resolve quantity update issue
docs(api): update authentication endpoints
test(components): add button component tests
```

## Troubleshooting

### Common Issues

1. **Module Resolution Errors**
   ```bash
   # Clear cache and reinstall
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **TypeScript Errors**
   ```bash
   # Regenerate types
   npm run types:supabase
   npm run type-check
   ```

3. **Build Failures**
   ```bash
   # Check environment variables
   # Verify all imports are correct
   # Run linting: npm run lint
   ```

## Resources

- **React Documentation**: [https://react.dev](https://react.dev)
- **TypeScript Handbook**: [https://www.typescriptlang.org/docs](https://www.typescriptlang.org/docs)
- **Tailwind CSS**: [https://tailwindcss.com/docs](https://tailwindcss.com/docs)
- **Supabase Docs**: [https://supabase.com/docs](https://supabase.com/docs)
- **Vite Guide**: [https://vitejs.dev/guide](https://vitejs.dev/guide)
