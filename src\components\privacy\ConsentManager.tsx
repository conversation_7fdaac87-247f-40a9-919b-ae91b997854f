// Comprehensive Consent Management System for GDPR/CCPA Compliance
// Cookie consent, data processing consent, and privacy controls

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { 
  Shield, 
  Cookie, 
  Eye, 
  BarChart3, 
  MapPin, 
  Settings, 
  Download,
  Trash2,
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react';

export interface ConsentPreferences {
  necessary: boolean;
  analytics: boolean;
  marketing: boolean;
  personalization: boolean;
  social: boolean;
  advertising: boolean;
}

export interface PrivacySettings {
  dataProcessing: boolean;
  profileAnalytics: boolean;
  emailMarketing: boolean;
  pushNotifications: boolean;
  dataSharing: boolean;
  locationTracking: boolean;
  behaviorTracking: boolean;
}

export interface UserDataSummary {
  personalData: {
    profile: number;
    preferences: number;
    activity: number;
  };
  cookies: {
    necessary: number;
    analytics: number;
    marketing: number;
  };
  thirdParty: {
    google: boolean;
    stripe: boolean;
    supabase: boolean;
  };
}

interface ConsentManagerProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'banner' | 'settings' | 'full';
}

export function ConsentManager({ isOpen, onClose, mode }: ConsentManagerProps) {
  const [consentPreferences, setConsentPreferences] = useState<ConsentPreferences>({
    necessary: true,
    analytics: false,
    marketing: false,
    personalization: false,
    social: false,
    advertising: false,
  });

  const [privacySettings, setPrivacySettings] = useState<PrivacySettings>({
    dataProcessing: true,
    profileAnalytics: false,
    emailMarketing: false,
    pushNotifications: false,
    dataSharing: false,
    locationTracking: false,
    behaviorTracking: false,
  });

  const [userDataSummary, setUserDataSummary] = useState<UserDataSummary | null>(null);
  const [hasConsented, setHasConsented] = useState(false);
  const [showDataExport, setShowDataExport] = useState(false);
  const [showDataDeletion, setShowDataDeletion] = useState(false);

  useEffect(() => {
    loadConsentPreferences();
    loadPrivacySettings();
    loadUserDataSummary();
  }, []);

  const loadConsentPreferences = useCallback(() => {
    const stored = localStorage.getItem('chronicle-consent-preferences');
    if (stored) {
      setConsentPreferences(JSON.parse(stored));
      setHasConsented(true);
    }
  }, []);

  const loadPrivacySettings = useCallback(() => {
    const stored = localStorage.getItem('chronicle-privacy-settings');
    if (stored) {
      setPrivacySettings(JSON.parse(stored));
    }
  }, []);

  const loadUserDataSummary = useCallback(async () => {
    // This would fetch actual data summary from your backend
    const summary: UserDataSummary = {
      personalData: {
        profile: 15,
        preferences: 8,
        activity: 142,
      },
      cookies: {
        necessary: 3,
        analytics: 5,
        marketing: 12,
      },
      thirdParty: {
        google: true,
        stripe: true,
        supabase: true,
      },
    };
    setUserDataSummary(summary);
  }, []);

  const saveConsentPreferences = useCallback((preferences: ConsentPreferences) => {
    localStorage.setItem('chronicle-consent-preferences', JSON.stringify(preferences));
    localStorage.setItem('chronicle-consent-timestamp', new Date().toISOString());
    setConsentPreferences(preferences);
    setHasConsented(true);
    
    // Apply consent preferences
    applyConsentPreferences(preferences);
  }, []);

  const savePrivacySettings = useCallback((settings: PrivacySettings) => {
    localStorage.setItem('chronicle-privacy-settings', JSON.stringify(settings));
    setPrivacySettings(settings);
    
    // Apply privacy settings
    applyPrivacySettings(settings);
  }, []);

  const applyConsentPreferences = useCallback((preferences: ConsentPreferences) => {
    // Enable/disable analytics
    if (preferences.analytics) {
      // Enable Google Analytics
      if (typeof gtag !== 'undefined') {
        gtag('consent', 'update', {
          analytics_storage: 'granted',
        });
      }
    } else {
      // Disable analytics
      if (typeof gtag !== 'undefined') {
        gtag('consent', 'update', {
          analytics_storage: 'denied',
        });
      }
    }

    // Enable/disable marketing cookies
    if (preferences.marketing) {
      if (typeof gtag !== 'undefined') {
        gtag('consent', 'update', {
          ad_storage: 'granted',
          ad_user_data: 'granted',
          ad_personalization: 'granted',
        });
      }
    } else {
      if (typeof gtag !== 'undefined') {
        gtag('consent', 'update', {
          ad_storage: 'denied',
          ad_user_data: 'denied',
          ad_personalization: 'denied',
        });
      }
    }

    // Clear non-consented cookies
    clearNonConsentedCookies(preferences);
  }, []);

  const applyPrivacySettings = useCallback((settings: PrivacySettings) => {
    // Apply privacy settings to various services
    console.log('Applying privacy settings:', settings);
    
    // This would integrate with your backend to update user privacy preferences
  }, []);

  const clearNonConsentedCookies = useCallback((preferences: ConsentPreferences) => {
    const cookiesToClear: string[] = [];

    if (!preferences.analytics) {
      cookiesToClear.push('_ga', '_ga_*', '_gid', '_gat');
    }

    if (!preferences.marketing) {
      cookiesToClear.push('_fbp', '_fbc', 'fr');
    }

    if (!preferences.social) {
      cookiesToClear.push('_twitter_sess', 'personalization_id');
    }

    // Clear cookies
    cookiesToClear.forEach(cookieName => {
      if (cookieName.includes('*')) {
        // Handle wildcard cookies
        const prefix = cookieName.replace('*', '');
        document.cookie.split(';').forEach(cookie => {
          const name = cookie.split('=')[0].trim();
          if (name.startsWith(prefix)) {
            document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
          }
        });
      } else {
        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
      }
    });
  }, []);

  const acceptAllConsent = useCallback(() => {
    const allAccepted: ConsentPreferences = {
      necessary: true,
      analytics: true,
      marketing: true,
      personalization: true,
      social: true,
      advertising: true,
    };
    saveConsentPreferences(allAccepted);
    onClose();
  }, [saveConsentPreferences, onClose]);

  const acceptNecessaryOnly = useCallback(() => {
    const necessaryOnly: ConsentPreferences = {
      necessary: true,
      analytics: false,
      marketing: false,
      personalization: false,
      social: false,
      advertising: false,
    };
    saveConsentPreferences(necessaryOnly);
    onClose();
  }, [saveConsentPreferences, onClose]);

  const saveCustomPreferences = useCallback(() => {
    saveConsentPreferences(consentPreferences);
    onClose();
  }, [consentPreferences, saveConsentPreferences, onClose]);

  const exportUserData = useCallback(async () => {
    try {
      // This would call your backend API to generate user data export
      const response = await fetch('/api/privacy/export-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth-token')}`,
        },
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `chronicle-data-export-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Data export failed:', error);
    }
  }, []);

  const requestDataDeletion = useCallback(async () => {
    try {
      const response = await fetch('/api/privacy/delete-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth-token')}`,
        },
      });

      if (response.ok) {
        alert('Data deletion request submitted. You will receive a confirmation email.');
        setShowDataDeletion(false);
      }
    } catch (error) {
      console.error('Data deletion request failed:', error);
    }
  }, []);

  if (mode === 'banner' && hasConsented) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Shield className="w-5 h-5 mr-2" />
            Privacy & Cookie Settings
          </DialogTitle>
          <DialogDescription>
            Manage your privacy preferences and understand how we use your data.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="consent" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="consent">Consent</TabsTrigger>
            <TabsTrigger value="privacy">Privacy</TabsTrigger>
            <TabsTrigger value="data">Your Data</TabsTrigger>
            <TabsTrigger value="rights">Your Rights</TabsTrigger>
          </TabsList>

          {/* Consent Management */}
          <TabsContent value="consent" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Cookie className="w-4 h-4 mr-2" />
                  Cookie Preferences
                </CardTitle>
                <CardDescription>
                  Choose which cookies and tracking technologies you're comfortable with.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <ConsentOption
                  title="Necessary Cookies"
                  description="Essential for the website to function properly. Cannot be disabled."
                  enabled={consentPreferences.necessary}
                  required={true}
                  onChange={() => {}}
                  icon={<Shield className="w-4 h-4" />}
                />
                
                <ConsentOption
                  title="Analytics Cookies"
                  description="Help us understand how visitors interact with our website."
                  enabled={consentPreferences.analytics}
                  onChange={(enabled) => 
                    setConsentPreferences(prev => ({ ...prev, analytics: enabled }))
                  }
                  icon={<BarChart3 className="w-4 h-4" />}
                />
                
                <ConsentOption
                  title="Marketing Cookies"
                  description="Used to deliver personalized advertisements and track campaign effectiveness."
                  enabled={consentPreferences.marketing}
                  onChange={(enabled) => 
                    setConsentPreferences(prev => ({ ...prev, marketing: enabled }))
                  }
                  icon={<Eye className="w-4 h-4" />}
                />
                
                <ConsentOption
                  title="Personalization"
                  description="Remember your preferences and provide customized content."
                  enabled={consentPreferences.personalization}
                  onChange={(enabled) => 
                    setConsentPreferences(prev => ({ ...prev, personalization: enabled }))
                  }
                  icon={<Settings className="w-4 h-4" />}
                />
                
                <ConsentOption
                  title="Social Media"
                  description="Enable social media features and content sharing."
                  enabled={consentPreferences.social}
                  onChange={(enabled) => 
                    setConsentPreferences(prev => ({ ...prev, social: enabled }))
                  }
                  icon={<Eye className="w-4 h-4" />}
                />
                
                <ConsentOption
                  title="Advertising"
                  description="Show relevant advertisements based on your interests."
                  enabled={consentPreferences.advertising}
                  onChange={(enabled) => 
                    setConsentPreferences(prev => ({ ...prev, advertising: enabled }))
                  }
                  icon={<Eye className="w-4 h-4" />}
                />
              </CardContent>
            </Card>

            <div className="flex space-x-2">
              <Button onClick={acceptAllConsent} className="flex-1">
                Accept All
              </Button>
              <Button onClick={acceptNecessaryOnly} variant="outline" className="flex-1">
                Necessary Only
              </Button>
              <Button onClick={saveCustomPreferences} variant="secondary" className="flex-1">
                Save Preferences
              </Button>
            </div>
          </TabsContent>

          {/* Privacy Settings */}
          <TabsContent value="privacy" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Data Processing Preferences</CardTitle>
                <CardDescription>
                  Control how we process and use your personal data.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <PrivacyOption
                  title="Profile Analytics"
                  description="Analyze your reading patterns to improve content recommendations."
                  enabled={privacySettings.profileAnalytics}
                  onChange={(enabled) => 
                    setPrivacySettings(prev => ({ ...prev, profileAnalytics: enabled }))
                  }
                />
                
                <PrivacyOption
                  title="Email Marketing"
                  description="Receive personalized email newsletters and promotional content."
                  enabled={privacySettings.emailMarketing}
                  onChange={(enabled) => 
                    setPrivacySettings(prev => ({ ...prev, emailMarketing: enabled }))
                  }
                />
                
                <PrivacyOption
                  title="Push Notifications"
                  description="Receive browser notifications for breaking news and updates."
                  enabled={privacySettings.pushNotifications}
                  onChange={(enabled) => 
                    setPrivacySettings(prev => ({ ...prev, pushNotifications: enabled }))
                  }
                />
                
                <PrivacyOption
                  title="Data Sharing"
                  description="Share anonymized data with trusted partners for research."
                  enabled={privacySettings.dataSharing}
                  onChange={(enabled) => 
                    setPrivacySettings(prev => ({ ...prev, dataSharing: enabled }))
                  }
                />
                
                <PrivacyOption
                  title="Location Tracking"
                  description="Use your location to provide local news and content."
                  enabled={privacySettings.locationTracking}
                  onChange={(enabled) => 
                    setPrivacySettings(prev => ({ ...prev, locationTracking: enabled }))
                  }
                />
                
                <PrivacyOption
                  title="Behavior Tracking"
                  description="Track your browsing behavior to improve user experience."
                  enabled={privacySettings.behaviorTracking}
                  onChange={(enabled) => 
                    setPrivacySettings(prev => ({ ...prev, behaviorTracking: enabled }))
                  }
                />
              </CardContent>
            </Card>

            <Button onClick={() => savePrivacySettings(privacySettings)} className="w-full">
              Save Privacy Settings
            </Button>
          </TabsContent>

          {/* Your Data */}
          <TabsContent value="data" className="space-y-4">
            {userDataSummary && (
              <>
                <Card>
                  <CardHeader>
                    <CardTitle>Data We Store About You</CardTitle>
                    <CardDescription>
                      Overview of the personal data we have collected and stored.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold">{userDataSummary.personalData.profile}</div>
                        <div className="text-sm text-gray-600">Profile Data Points</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold">{userDataSummary.personalData.preferences}</div>
                        <div className="text-sm text-gray-600">Preferences</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold">{userDataSummary.personalData.activity}</div>
                        <div className="text-sm text-gray-600">Activity Records</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Cookies & Tracking</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold">{userDataSummary.cookies.necessary}</div>
                        <div className="text-sm text-gray-600">Necessary Cookies</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold">{userDataSummary.cookies.analytics}</div>
                        <div className="text-sm text-gray-600">Analytics Cookies</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold">{userDataSummary.cookies.marketing}</div>
                        <div className="text-sm text-gray-600">Marketing Cookies</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Third-Party Services</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>Google Analytics</span>
                        <Badge variant={userDataSummary.thirdParty.google ? "default" : "secondary"}>
                          {userDataSummary.thirdParty.google ? "Active" : "Inactive"}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>Stripe Payments</span>
                        <Badge variant={userDataSummary.thirdParty.stripe ? "default" : "secondary"}>
                          {userDataSummary.thirdParty.stripe ? "Active" : "Inactive"}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>Supabase Database</span>
                        <Badge variant={userDataSummary.thirdParty.supabase ? "default" : "secondary"}>
                          {userDataSummary.thirdParty.supabase ? "Active" : "Inactive"}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </TabsContent>

          {/* Your Rights */}
          <TabsContent value="rights" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Your Privacy Rights</CardTitle>
                <CardDescription>
                  Under GDPR and CCPA, you have several rights regarding your personal data.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button 
                    variant="outline" 
                    className="h-auto p-4 flex flex-col items-start"
                    onClick={exportUserData}
                  >
                    <div className="flex items-center mb-2">
                      <Download className="w-4 h-4 mr-2" />
                      <span className="font-semibold">Export Your Data</span>
                    </div>
                    <span className="text-sm text-gray-600 text-left">
                      Download a copy of all personal data we have about you.
                    </span>
                  </Button>

                  <Button 
                    variant="outline" 
                    className="h-auto p-4 flex flex-col items-start"
                    onClick={() => setShowDataDeletion(true)}
                  >
                    <div className="flex items-center mb-2">
                      <Trash2 className="w-4 h-4 mr-2" />
                      <span className="font-semibold">Delete Your Data</span>
                    </div>
                    <span className="text-sm text-gray-600 text-left">
                      Request permanent deletion of your personal data.
                    </span>
                  </Button>

                  <Button 
                    variant="outline" 
                    className="h-auto p-4 flex flex-col items-start"
                    onClick={() => window.open('/privacy-policy', '_blank')}
                  >
                    <div className="flex items-center mb-2">
                      <Info className="w-4 h-4 mr-2" />
                      <span className="font-semibold">Privacy Policy</span>
                    </div>
                    <span className="text-sm text-gray-600 text-left">
                      Read our complete privacy policy and data practices.
                    </span>
                  </Button>

                  <Button 
                    variant="outline" 
                    className="h-auto p-4 flex flex-col items-start"
                    onClick={() => window.open('/contact?subject=privacy', '_blank')}
                  >
                    <div className="flex items-center mb-2">
                      <AlertTriangle className="w-4 h-4 mr-2" />
                      <span className="font-semibold">Contact Us</span>
                    </div>
                    <span className="text-sm text-gray-600 text-left">
                      Have questions about your privacy rights? Contact our DPO.
                    </span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Data Deletion Confirmation Dialog */}
        <Dialog open={showDataDeletion} onOpenChange={setShowDataDeletion}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="flex items-center text-red-600">
                <AlertTriangle className="w-5 h-5 mr-2" />
                Confirm Data Deletion
              </DialogTitle>
              <DialogDescription>
                This action cannot be undone. All your personal data, including your account, 
                articles, comments, and preferences will be permanently deleted.
              </DialogDescription>
            </DialogHeader>
            <div className="flex space-x-2">
              <Button 
                variant="destructive" 
                onClick={requestDataDeletion}
                className="flex-1"
              >
                Yes, Delete My Data
              </Button>
              <Button 
                variant="outline" 
                onClick={() => setShowDataDeletion(false)}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </DialogContent>
    </Dialog>
  );
}

// Helper Components

interface ConsentOptionProps {
  title: string;
  description: string;
  enabled: boolean;
  required?: boolean;
  onChange: (enabled: boolean) => void;
  icon: React.ReactNode;
}

function ConsentOption({ title, description, enabled, required = false, onChange, icon }: ConsentOptionProps) {
  return (
    <div className="flex items-start space-x-3 p-3 border rounded-lg">
      <div className="flex-shrink-0 mt-1">
        {icon}
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <h4 className="font-medium">{title}</h4>
          <Switch 
            checked={enabled} 
            onCheckedChange={onChange}
            disabled={required}
          />
        </div>
        <p className="text-sm text-gray-600 mt-1">{description}</p>
        {required && (
          <Badge variant="secondary" className="mt-2">
            Required
          </Badge>
        )}
      </div>
    </div>
  );
}

interface PrivacyOptionProps {
  title: string;
  description: string;
  enabled: boolean;
  onChange: (enabled: boolean) => void;
}

function PrivacyOption({ title, description, enabled, onChange }: PrivacyOptionProps) {
  return (
    <div className="flex items-start justify-between p-3 border rounded-lg">
      <div className="flex-1">
        <h4 className="font-medium">{title}</h4>
        <p className="text-sm text-gray-600 mt-1">{description}</p>
      </div>
      <Switch 
        checked={enabled} 
        onCheckedChange={onChange}
        className="ml-3"
      />
    </div>
  );
}

export default ConsentManager;
