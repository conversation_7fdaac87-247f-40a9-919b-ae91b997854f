import React from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useTheme } from '@/contexts/ThemeContext';
import { Sun, Moon, Monitor, Palette } from 'lucide-react';

export function ThemeToggle() {
  const { theme, setTheme, actualTheme } = useTheme();

  const getIcon = () => {
    switch (theme) {
      case 'light':
        return <Sun className="h-4 w-4" />;
      case 'dark':
        return <Moon className="h-4 w-4" />;
      case 'system':
        return <Monitor className="h-4 w-4" />;
      default:
        return <Sun className="h-4 w-4" />;
    }
  };

  const getThemeLabel = (themeValue: string) => {
    switch (themeValue) {
      case 'light':
        return 'Light';
      case 'dark':
        return 'Dark (Gold)';
      case 'system':
        return 'System';
      default:
        return 'Light';
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="relative"
          aria-label="Toggle theme"
        >
          {getIcon()}
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuItem
          onClick={() => setTheme('light')}
          className={`cursor-pointer ${theme === 'light' ? 'bg-accent' : ''}`}
        >
          <Sun className="mr-2 h-4 w-4" />
          <span>Light</span>
          {theme === 'light' && (
            <div className="ml-auto h-2 w-2 rounded-full bg-primary" />
          )}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setTheme('dark')}
          className={`cursor-pointer ${theme === 'dark' ? 'bg-accent' : ''}`}
        >
          <Moon className="mr-2 h-4 w-4" />
          <span>Dark (Gold)</span>
          {theme === 'dark' && (
            <div className="ml-auto h-2 w-2 rounded-full bg-primary" />
          )}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setTheme('system')}
          className={`cursor-pointer ${theme === 'system' ? 'bg-accent' : ''}`}
        >
          <Monitor className="mr-2 h-4 w-4" />
          <span>System</span>
          {theme === 'system' && (
            <div className="ml-auto h-2 w-2 rounded-full bg-primary" />
          )}
        </DropdownMenuItem>
        <div className="px-2 py-1.5 text-xs text-muted-foreground border-t mt-1">
          <div className="flex items-center gap-2">
            <Palette className="h-3 w-3" />
            <span>Current: {getThemeLabel(theme)}</span>
          </div>
          <div className="text-xs opacity-75 mt-1">
            {actualTheme === 'dark' ? '✨ Gold accents active' : '🔵 Blue accents active'}
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Simple theme toggle button (for mobile or minimal UI)
export function SimpleThemeToggle() {
  const { toggleTheme, actualTheme } = useTheme();

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleTheme}
      className="relative"
      aria-label="Toggle theme"
    >
      {actualTheme === 'dark' ? (
        <Sun className="h-4 w-4 text-yellow-500" />
      ) : (
        <Moon className="h-4 w-4" />
      )}
      <span className="sr-only">Toggle theme</span>
    </Button>
  );
}

// Theme indicator component
export function ThemeIndicator() {
  const { theme, actualTheme } = useTheme();

  return (
    <div className="flex items-center gap-2 text-xs text-muted-foreground">
      <div className="flex items-center gap-1">
        {actualTheme === 'dark' ? (
          <Moon className="h-3 w-3 text-yellow-500" />
        ) : (
          <Sun className="h-3 w-3 text-blue-500" />
        )}
        <span>{actualTheme === 'dark' ? 'Dark' : 'Light'}</span>
      </div>
      {theme === 'system' && (
        <div className="flex items-center gap-1 opacity-75">
          <Monitor className="h-3 w-3" />
          <span>Auto</span>
        </div>
      )}
      {actualTheme === 'dark' && (
        <div className="flex items-center gap-1 text-yellow-600 dark:text-yellow-400">
          <Palette className="h-3 w-3" />
          <span>Gold</span>
        </div>
      )}
    </div>
  );
}
