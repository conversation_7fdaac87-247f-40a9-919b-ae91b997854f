
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Article } from "@/types/blog";
import { Clock, Eye, TrendingUp } from "lucide-react";

interface HeroSectionProps {
  featuredArticle: Article;
  trendingArticles: Article[];
}

export default function HeroSection({ featuredArticle, trendingArticles }: HeroSectionProps) {
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    }).format(date);
  };

  return (
    <section className="relative bg-gradient-to-br from-gray-50 to-white py-16 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23000000%22%20fill-opacity%3D%220.1%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%222%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
          {/* Featured Article */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="lg:col-span-2"
          >
            <div className="relative group cursor-pointer">
              <Link to={`/articles/${featuredArticle.slug}`}>
                <div className="relative overflow-hidden rounded-2xl">
                  <img
                    src={featuredArticle.featuredImage}
                    alt={featuredArticle.title}
                    className="w-full h-[400px] object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                  
                  {/* Content Overlay */}
                  <div className="absolute bottom-0 left-0 right-0 p-8 text-white">
                    <div className="space-y-4">
                      <div className="flex items-center space-x-3">
                        <Badge className="bg-red-600 text-white">
                          Breaking News
                        </Badge>
                        <Badge variant="secondary" className="bg-white/20 text-white">
                          {featuredArticle.category.name}
                        </Badge>
                      </div>
                      
                      <h1 className="text-3xl md:text-4xl font-bold leading-tight">
                        {featuredArticle.title}
                      </h1>
                      
                      <p className="text-lg text-gray-200 line-clamp-2">
                        {featuredArticle.excerpt}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4 text-sm">
                          <span>By {featuredArticle.author.name}</span>
                          <div className="flex items-center space-x-1">
                            <Clock className="w-4 h-4" />
                            <span>{featuredArticle.readTime} min read</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Eye className="w-4 h-4" />
                            <span>{featuredArticle.views.toLocaleString()}</span>
                          </div>
                        </div>
                        <span className="text-sm">{formatDate(featuredArticle.publishedAt)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          </motion.div>

          {/* Trending Articles Sidebar */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="space-y-6"
          >
            <div className="flex items-center space-x-2 text-lg font-bold">
              <TrendingUp className="w-5 h-5 text-red-600" />
              <span>Trending Now</span>
            </div>
            
            <div className="space-y-4">
              {trendingArticles.map((article, index) => (
                <motion.div
                  key={article.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.3 + index * 0.1 }}
                  className="group"
                >
                  <Link to={`/articles/${article.slug}`}>
                    <div className="flex space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="flex-shrink-0">
                        <span className="text-2xl font-bold text-gray-300 group-hover:text-red-600 transition-colors">
                          {String(index + 1).padStart(2, '0')}
                        </span>
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold text-sm leading-tight group-hover:text-blue-600 transition-colors line-clamp-2">
                          {article.title}
                        </h3>
                        
                        <div className="flex items-center space-x-2 mt-2 text-xs text-gray-500">
                          <span>{article.author.name}</span>
                          <span>•</span>
                          <div className="flex items-center space-x-1">
                            <Eye className="w-3 h-3" />
                            <span>{article.views.toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                      
                      {article.featuredImage && (
                        <div className="flex-shrink-0">
                          <img
                            src={article.featuredImage}
                            alt={article.title}
                            className="w-16 h-16 object-cover rounded-lg"
                          />
                        </div>
                      )}
                    </div>
                  </Link>
                </motion.div>
              ))}
            </div>
            
            <div className="pt-4 border-t border-gray-200">
              <Button variant="outline" className="w-full" asChild>
                <Link to="/articles">
                  View All Articles
                </Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
