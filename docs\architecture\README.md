# Architecture Documentation

This document describes the technical architecture of The Chronicle platform.

## System Overview

The Chronicle is built as a modern, scalable web application using a serverless architecture with the following key components:

- **Frontend**: React 18 + TypeScript SPA
- **Backend**: Supabase (PostgreSQL + Auth + Storage)
- **Payments**: Stripe integration
- **Deployment**: Vercel serverless platform
- **Monitoring**: Custom analytics + error tracking

## Architecture Diagram

```mermaid
graph TB
    subgraph "Client Layer"
        Web[Web Browser]
        Mobile[Mobile Browser]
    end

    subgraph "CDN Layer"
        Vercel[Vercel Edge Network]
    end

    subgraph "Application Layer"
        React[React SPA]
        Router[React Router]
        State[Context + Hooks]
    end

    subgraph "API Layer"
        Supabase[Supabase API]
        Stripe[Stripe API]
        Analytics[Analytics API]
    end

    subgraph "Data Layer"
        PostgreSQL[(PostgreSQL)]
        Storage[Supabase Storage]
        Cache[Edge Cache]
    end

    subgraph "External Services"
        GA[Google Analytics]
        Sentry[Error Tracking]
        Email[Email Service]
    end

    Web --> Vercel
    Mobile --> Vercel
    Vercel --> React
    React --> Router
    React --> State
    React --> Supabase
    React --> Stripe
    React --> Analytics
    Supabase --> PostgreSQL
    Supabase --> Storage
    React --> GA
    React --> Sentry
    Supabase --> Email
```

## Frontend Architecture

### Component Architecture

```
src/
├── components/
│   ├── ui/              # Base UI components (shadcn/ui)
│   │   ├── button.tsx
│   │   ├── input.tsx
│   │   └── card.tsx
│   ├── layout/          # Layout components
│   │   ├── Header.tsx
│   │   ├── Footer.tsx
│   │   └── Sidebar.tsx
│   ├── auth/            # Authentication components
│   │   ├── LoginForm.tsx
│   │   ├── RegisterForm.tsx
│   │   └── MFASetup.tsx
│   ├── admin/           # Admin dashboard components
│   │   ├── Dashboard.tsx
│   │   ├── UserManagement.tsx
│   │   └── Analytics.tsx
│   ├── ecommerce/       # E-commerce components
│   │   ├── ProductCard.tsx
│   │   ├── ShoppingCart.tsx
│   │   └── Checkout.tsx
│   └── pages/           # Page-level components
│       ├── HomePage.tsx
│       ├── ArticlePage.tsx
│       └── ProductPage.tsx
```

### State Management

The application uses React Context API with custom hooks for state management:

```typescript
// Global state structure
interface AppState {
  auth: AuthState;
  cart: CartState;
  ui: UIState;
  articles: ArticleState;
  products: ProductState;
}

// Context providers
<AuthProvider>
  <CartProvider>
    <UIProvider>
      <App />
    </UIProvider>
  </CartProvider>
</AuthProvider>
```

### Data Flow

1. **User Interaction** → Component
2. **Component** → Custom Hook
3. **Custom Hook** → Supabase Client
4. **Supabase Client** → Database/API
5. **Response** → Hook → Component → UI Update

### Routing Strategy

```typescript
// Route structure
const routes = [
  { path: '/', component: HomePage },
  { path: '/articles', component: ArticlesPage },
  { path: '/articles/:slug', component: ArticlePage },
  { path: '/products', component: ProductsPage },
  { path: '/products/:slug', component: ProductPage },
  { path: '/cart', component: CartPage },
  { path: '/checkout', component: CheckoutPage },
  { path: '/dashboard', component: DashboardPage, protected: true },
  { path: '/admin', component: AdminPage, role: 'admin' },
];
```

## Backend Architecture

### Supabase Services

#### Database (PostgreSQL)

```sql
-- Core tables structure
Tables:
├── profiles              # User profiles
├── articles             # Blog articles
├── products             # E-commerce products
├── categories           # Content categories
├── tags                 # Content tags
├── orders               # Purchase orders
├── order_items          # Order line items
├── comments             # Article comments
├── subscriptions        # User subscriptions
└── analytics_events     # Custom analytics
```

#### Row Level Security (RLS)

```sql
-- Example RLS policies
CREATE POLICY "Users can view published articles" ON articles
  FOR SELECT USING (is_published = true);

CREATE POLICY "Authors can manage their articles" ON articles
  FOR ALL USING (auth.uid() = author_id);

CREATE POLICY "Users can view their own orders" ON orders
  FOR SELECT USING (auth.uid() = user_id);
```

#### Real-time Subscriptions

```typescript
// Real-time data subscriptions
const subscription = supabase
  .channel('articles')
  .on('postgres_changes', {
    event: 'INSERT',
    schema: 'public',
    table: 'articles'
  }, (payload) => {
    // Handle new article
  })
  .subscribe();
```

### API Design

#### RESTful Endpoints

```typescript
// API endpoint patterns
GET    /api/articles              # List articles
GET    /api/articles/:id          # Get article
POST   /api/articles              # Create article
PUT    /api/articles/:id          # Update article
DELETE /api/articles/:id          # Delete article

GET    /api/products              # List products
GET    /api/products/:id          # Get product
POST   /api/orders                # Create order
GET    /api/orders/:id            # Get order
```

#### GraphQL-like Queries

```typescript
// Supabase query with relations
const { data } = await supabase
  .from('articles')
  .select(`
    *,
    author:profiles(*),
    category:categories(*),
    tags:article_tags(tag:tags(*))
  `)
  .eq('is_published', true);
```

## Data Models

### User Model

```typescript
interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  role: 'user' | 'author' | 'admin';
  subscription?: 'free' | 'premium' | 'enterprise';
  createdAt: string;
  updatedAt: string;
}
```

### Article Model

```typescript
interface Article {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  featuredImage?: string;
  authorId: string;
  categoryId: string;
  tags: string[];
  isPremium: boolean;
  isPublished: boolean;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
  readTime: number;
  views: number;
  likes: number;
}
```

### Product Model

```typescript
interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  shortDescription: string;
  price: number;
  comparePrice?: number;
  type: 'digital' | 'physical';
  categoryId: string;
  images: string[];
  stock: number;
  status: 'active' | 'inactive' | 'archived';
  featured: boolean;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}
```

### Order Model

```typescript
interface Order {
  id: string;
  userId: string;
  status: 'pending' | 'processing' | 'completed' | 'cancelled';
  items: OrderItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
  paymentIntentId: string;
  shippingAddress?: Address;
  billingAddress?: Address;
  createdAt: string;
  updatedAt: string;
}
```

## Security Architecture

### Authentication Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Supabase
    participant Database

    User->>Frontend: Login Request
    Frontend->>Supabase: Auth Request
    Supabase->>Database: Validate Credentials
    Database-->>Supabase: User Data
    Supabase-->>Frontend: JWT Token
    Frontend-->>User: Login Success
    
    Note over Frontend: Store JWT in memory
    Note over Frontend: Use JWT for API calls
```

### Authorization Levels

1. **Public** - No authentication required
2. **User** - Authenticated user access
3. **Author** - Content creation permissions
4. **Admin** - Full system access

### Data Protection

- **Encryption at Rest** - Database encryption
- **Encryption in Transit** - HTTPS/TLS
- **Input Validation** - Client and server-side validation
- **XSS Protection** - Content sanitization
- **CSRF Protection** - Token-based protection
- **Rate Limiting** - API abuse prevention

## Performance Architecture

### Caching Strategy

```mermaid
graph LR
    User[User Request]
    CDN[Vercel Edge Cache]
    App[React App]
    API[Supabase API]
    DB[(Database)]

    User --> CDN
    CDN --> App
    App --> API
    API --> DB

    CDN -.->|Cache Hit| User
    App -.->|Local Cache| App
```

### Optimization Techniques

1. **Code Splitting** - Route-based lazy loading
2. **Tree Shaking** - Remove unused code
3. **Image Optimization** - WebP format, lazy loading
4. **Bundle Analysis** - Monitor bundle size
5. **Memoization** - React.memo, useMemo, useCallback
6. **Virtual Scrolling** - Large list optimization

### Performance Monitoring

```typescript
// Core Web Vitals tracking
import { performanceMonitor } from '@/utils/performanceMonitoring';

// Track component render time
performanceMonitor.trackComponentRender('ArticleCard', renderTime);

// Track API response time
performanceMonitor.trackApiCall('/api/articles', 'GET', duration, status);

// Track user interactions
performanceMonitor.trackUserInteraction('click', 'article-link');
```

## Scalability Considerations

### Horizontal Scaling

- **Serverless Functions** - Auto-scaling compute
- **CDN Distribution** - Global edge caching
- **Database Scaling** - Supabase auto-scaling
- **Load Balancing** - Automatic traffic distribution

### Vertical Scaling

- **Database Optimization** - Indexes, query optimization
- **Caching Layers** - Multiple cache levels
- **Asset Optimization** - Compressed assets
- **Code Optimization** - Efficient algorithms

### Monitoring and Alerting

```typescript
// Error tracking and monitoring
import { errorTracker } from '@/utils/errorTracking';
import { analytics } from '@/utils/analytics';

// Track errors
errorTracker.captureError(error, context);

// Track user behavior
analytics.track('article_read', { articleId, category });

// Performance monitoring
performanceMonitor.recordMetric('page_load_time', duration);
```

## Deployment Architecture

### CI/CD Pipeline

```mermaid
graph LR
    Code[Code Push]
    Test[Automated Tests]
    Build[Build Process]
    Deploy[Deploy to Vercel]
    Monitor[Monitor & Alert]

    Code --> Test
    Test --> Build
    Build --> Deploy
    Deploy --> Monitor
    Monitor -.-> Code
```

### Environment Strategy

- **Development** - Local development with hot reload
- **Staging** - Production-like environment for testing
- **Production** - Live environment with monitoring

### Infrastructure

- **Hosting** - Vercel serverless platform
- **Database** - Supabase managed PostgreSQL
- **Storage** - Supabase Storage (S3-compatible)
- **CDN** - Vercel Edge Network
- **Monitoring** - Custom analytics + error tracking

## Integration Architecture

### Third-Party Services

```typescript
// Service integrations
const integrations = {
  payments: 'Stripe',
  analytics: 'Google Analytics 4',
  errorTracking: 'Custom Error Tracker',
  email: 'Supabase Auth',
  storage: 'Supabase Storage',
  search: 'PostgreSQL Full-Text Search',
};
```

### API Integrations

- **Stripe API** - Payment processing
- **Google Analytics** - User behavior tracking
- **Supabase API** - Database and auth operations
- **Custom APIs** - Internal service communication

## Future Architecture Considerations

### Microservices Migration

Potential future migration to microservices:

```
Current: Monolithic SPA + Supabase
Future: Microservices + API Gateway

Services:
├── User Service
├── Content Service
├── Commerce Service
├── Analytics Service
└── Notification Service
```

### Advanced Features

- **GraphQL API** - More efficient data fetching
- **Event Sourcing** - Audit trail and replay capability
- **CQRS** - Command Query Responsibility Segregation
- **Message Queues** - Asynchronous processing
- **Machine Learning** - Content recommendations

## Documentation Standards

### Code Documentation

```typescript
/**
 * Retrieves paginated articles with optional filtering
 * @param options - Query options for filtering and pagination
 * @returns Promise resolving to articles and pagination info
 * @throws {Error} When database query fails
 */
async function getArticles(options: GetArticlesOptions): Promise<ArticlesResponse> {
  // Implementation
}
```

### API Documentation

- **OpenAPI/Swagger** - API specification
- **Postman Collections** - API testing
- **Code Examples** - Usage examples
- **Error Codes** - Comprehensive error documentation

This architecture provides a solid foundation for a scalable, maintainable, and performant content management and e-commerce platform.
