// System Monitoring Dashboard for The Chronicle
// Real-time system health, performance, and error monitoring

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Clock, 
  Cpu, 
  Database, 
  Globe, 
  Server,
  Zap,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Download,
  Bell,
  Settings
} from 'lucide-react';
import { LineChart, Line, AreaChart, Area, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  uptime: number;
  lastCheck: Date;
  services: {
    api: ServiceStatus;
    database: ServiceStatus;
    cdn: ServiceStatus;
    auth: ServiceStatus;
    payments: ServiceStatus;
    email: ServiceStatus;
  };
}

interface ServiceStatus {
  name: string;
  status: 'online' | 'degraded' | 'offline';
  responseTime: number;
  uptime: number;
  lastCheck: Date;
  errors: number;
  version?: string;
}

interface PerformanceMetrics {
  timestamp: Date;
  cpu: number;
  memory: number;
  disk: number;
  network: number;
  responseTime: number;
  throughput: number;
  errorRate: number;
}

interface ErrorSummary {
  total: number;
  critical: number;
  warnings: number;
  resolved: number;
  recent: Array<{
    id: string;
    message: string;
    severity: string;
    timestamp: Date;
    count: number;
  }>;
}

interface AlertRule {
  id: string;
  name: string;
  condition: string;
  threshold: number;
  enabled: boolean;
  lastTriggered?: Date;
}

export function SystemMonitoringDashboard() {
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [performanceData, setPerformanceData] = useState<PerformanceMetrics[]>([]);
  const [errorSummary, setErrorSummary] = useState<ErrorSummary | null>(null);
  const [alertRules, setAlertRules] = useState<AlertRule[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  useEffect(() => {
    loadMonitoringData();
    const interval = setInterval(loadMonitoringData, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const loadMonitoringData = useCallback(async () => {
    setIsLoading(true);
    try {
      const [health, performance, errors, alerts] = await Promise.all([
        fetchSystemHealth(),
        fetchPerformanceMetrics(),
        fetchErrorSummary(),
        fetchAlertRules(),
      ]);

      setSystemHealth(health);
      setPerformanceData(performance);
      setErrorSummary(errors);
      setAlertRules(alerts);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to load monitoring data:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const exportMetrics = useCallback(() => {
    const data = {
      timestamp: new Date().toISOString(),
      systemHealth,
      performanceData,
      errorSummary,
      alertRules,
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `system-metrics-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [systemHealth, performanceData, errorSummary, alertRules]);

  if (isLoading && !systemHealth) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">System Monitoring</h2>
          <p className="text-gray-600">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={exportMetrics}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button onClick={loadMonitoringData} disabled={isLoading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* System Status Overview */}
      {systemHealth && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="w-5 h-5 mr-2" />
              System Status
              <StatusBadge status={systemHealth.status} className="ml-2" />
            </CardTitle>
            <CardDescription>
              Uptime: {formatUptime(systemHealth.uptime)}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Object.entries(systemHealth.services).map(([key, service]) => (
                <ServiceCard key={key} service={service} />
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Monitoring Tabs */}
      <Tabs defaultValue="performance" className="space-y-4">
        <TabsList>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="errors">Errors</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
          <TabsTrigger value="logs">Logs</TabsTrigger>
        </TabsList>

        {/* Performance Monitoring */}
        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>System Resources</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" tickFormatter={(value) => new Date(value).toLocaleTimeString()} />
                    <YAxis />
                    <Tooltip labelFormatter={(value) => new Date(value).toLocaleString()} />
                    <Legend />
                    <Area type="monotone" dataKey="cpu" stackId="1" stroke="#8884d8" fill="#8884d8" name="CPU %" />
                    <Area type="monotone" dataKey="memory" stackId="1" stroke="#82ca9d" fill="#82ca9d" name="Memory %" />
                    <Area type="monotone" dataKey="disk" stackId="1" stroke="#ffc658" fill="#ffc658" name="Disk %" />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Response Time & Throughput</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" tickFormatter={(value) => new Date(value).toLocaleTimeString()} />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip labelFormatter={(value) => new Date(value).toLocaleString()} />
                    <Legend />
                    <Line yAxisId="left" type="monotone" dataKey="responseTime" stroke="#8884d8" name="Response Time (ms)" />
                    <Line yAxisId="right" type="monotone" dataKey="throughput" stroke="#82ca9d" name="Throughput (req/s)" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Error Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={200}>
                <AreaChart data={performanceData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="timestamp" tickFormatter={(value) => new Date(value).toLocaleTimeString()} />
                  <YAxis />
                  <Tooltip labelFormatter={(value) => new Date(value).toLocaleString()} />
                  <Area type="monotone" dataKey="errorRate" stroke="#ff7300" fill="#ff7300" name="Error Rate %" />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Error Monitoring */}
        <TabsContent value="errors" className="space-y-4">
          {errorSummary && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Errors</p>
                        <p className="text-2xl font-bold">{errorSummary.total}</p>
                      </div>
                      <AlertTriangle className="w-8 h-8 text-orange-500" />
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Critical</p>
                        <p className="text-2xl font-bold text-red-600">{errorSummary.critical}</p>
                      </div>
                      <XCircle className="w-8 h-8 text-red-500" />
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Warnings</p>
                        <p className="text-2xl font-bold text-yellow-600">{errorSummary.warnings}</p>
                      </div>
                      <AlertTriangle className="w-8 h-8 text-yellow-500" />
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Resolved</p>
                        <p className="text-2xl font-bold text-green-600">{errorSummary.resolved}</p>
                      </div>
                      <CheckCircle className="w-8 h-8 text-green-500" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Recent Errors</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {errorSummary.recent.map((error) => (
                      <div key={error.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex-1">
                          <div className="font-medium">{error.message}</div>
                          <div className="text-sm text-gray-600">
                            {error.timestamp.toLocaleString()} • Count: {error.count}
                          </div>
                        </div>
                        <Badge variant={error.severity === 'critical' ? 'destructive' : error.severity === 'warning' ? 'secondary' : 'default'}>
                          {error.severity}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        {/* Alert Rules */}
        <TabsContent value="alerts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Alert Rules
                <Button size="sm">
                  <Settings className="w-4 h-4 mr-2" />
                  Configure
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {alertRules.map((rule) => (
                  <div key={rule.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="font-medium">{rule.name}</div>
                      <div className="text-sm text-gray-600">
                        {rule.condition} > {rule.threshold}
                        {rule.lastTriggered && (
                          <span className="ml-2">• Last triggered: {rule.lastTriggered.toLocaleString()}</span>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant={rule.enabled ? 'default' : 'secondary'}>
                        {rule.enabled ? 'Enabled' : 'Disabled'}
                      </Badge>
                      <Bell className={`w-4 h-4 ${rule.enabled ? 'text-blue-500' : 'text-gray-400'}`} />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* System Logs */}
        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Logs</CardTitle>
              <CardDescription>Real-time system activity and events</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto">
                <div>[{new Date().toISOString()}] INFO: System monitoring dashboard loaded</div>
                <div>[{new Date().toISOString()}] INFO: Performance metrics updated</div>
                <div>[{new Date().toISOString()}] WARN: High memory usage detected (85%)</div>
                <div>[{new Date().toISOString()}] INFO: Database connection pool optimized</div>
                <div>[{new Date().toISOString()}] ERROR: Failed to connect to external API</div>
                <div>[{new Date().toISOString()}] INFO: Cache cleared successfully</div>
                <div>[{new Date().toISOString()}] INFO: Backup completed</div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Helper Components

interface ServiceCardProps {
  service: ServiceStatus;
}

function ServiceCard({ service }: ServiceCardProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'degraded':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'offline':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getServiceIcon = (name: string) => {
    switch (name.toLowerCase()) {
      case 'api':
        return <Server className="w-4 h-4" />;
      case 'database':
        return <Database className="w-4 h-4" />;
      case 'cdn':
        return <Globe className="w-4 h-4" />;
      default:
        return <Zap className="w-4 h-4" />;
    }
  };

  return (
    <div className="flex items-center space-x-3 p-3 border rounded-lg">
      <div className="flex-shrink-0">
        {getServiceIcon(service.name)}
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center space-x-2">
          <span className="font-medium">{service.name}</span>
          {getStatusIcon(service.status)}
        </div>
        <div className="text-sm text-gray-600">
          {service.responseTime}ms • {formatUptime(service.uptime)}
        </div>
      </div>
    </div>
  );
}

interface StatusBadgeProps {
  status: 'healthy' | 'warning' | 'critical';
  className?: string;
}

function StatusBadge({ status, className = '' }: StatusBadgeProps) {
  const variants = {
    healthy: 'bg-green-100 text-green-800',
    warning: 'bg-yellow-100 text-yellow-800',
    critical: 'bg-red-100 text-red-800',
  };

  return (
    <Badge className={`${variants[status]} ${className}`}>
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </Badge>
  );
}

// Helper Functions

function formatUptime(seconds: number): string {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (days > 0) {
    return `${days}d ${hours}h ${minutes}m`;
  } else if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else {
    return `${minutes}m`;
  }
}

// Mock data fetchers
async function fetchSystemHealth(): Promise<SystemHealth> {
  return {
    status: 'healthy',
    uptime: 2592000, // 30 days
    lastCheck: new Date(),
    services: {
      api: { name: 'API', status: 'online', responseTime: 45, uptime: 2592000, lastCheck: new Date(), errors: 0 },
      database: { name: 'Database', status: 'online', responseTime: 12, uptime: 2592000, lastCheck: new Date(), errors: 0 },
      cdn: { name: 'CDN', status: 'online', responseTime: 23, uptime: 2592000, lastCheck: new Date(), errors: 0 },
      auth: { name: 'Auth', status: 'online', responseTime: 34, uptime: 2592000, lastCheck: new Date(), errors: 0 },
      payments: { name: 'Payments', status: 'degraded', responseTime: 156, uptime: 2591000, lastCheck: new Date(), errors: 2 },
      email: { name: 'Email', status: 'online', responseTime: 67, uptime: 2592000, lastCheck: new Date(), errors: 0 },
    },
  };
}

async function fetchPerformanceMetrics(): Promise<PerformanceMetrics[]> {
  const now = new Date();
  const data: PerformanceMetrics[] = [];
  
  for (let i = 23; i >= 0; i--) {
    const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000);
    data.push({
      timestamp,
      cpu: Math.random() * 80 + 10,
      memory: Math.random() * 70 + 20,
      disk: Math.random() * 60 + 30,
      network: Math.random() * 100,
      responseTime: Math.random() * 200 + 50,
      throughput: Math.random() * 1000 + 500,
      errorRate: Math.random() * 5,
    });
  }
  
  return data;
}

async function fetchErrorSummary(): Promise<ErrorSummary> {
  return {
    total: 156,
    critical: 3,
    warnings: 23,
    resolved: 130,
    recent: [
      { id: '1', message: 'Database connection timeout', severity: 'critical', timestamp: new Date(), count: 5 },
      { id: '2', message: 'High memory usage detected', severity: 'warning', timestamp: new Date(), count: 12 },
      { id: '3', message: 'API rate limit exceeded', severity: 'warning', timestamp: new Date(), count: 8 },
      { id: '4', message: 'Payment gateway error', severity: 'critical', timestamp: new Date(), count: 2 },
    ],
  };
}

async function fetchAlertRules(): Promise<AlertRule[]> {
  return [
    { id: '1', name: 'High CPU Usage', condition: 'CPU', threshold: 80, enabled: true, lastTriggered: new Date() },
    { id: '2', name: 'Memory Usage', condition: 'Memory', threshold: 85, enabled: true },
    { id: '3', name: 'Response Time', condition: 'Response Time', threshold: 1000, enabled: true },
    { id: '4', name: 'Error Rate', condition: 'Error Rate', threshold: 5, enabled: false },
  ];
}

export default SystemMonitoringDashboard;
