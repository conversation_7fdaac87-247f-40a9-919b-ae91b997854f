# SonarCloud Configuration
sonar.projectKey=thechronicle_app
sonar.organization=thechronicle

# Project Information
sonar.projectName=The Chronicle
sonar.projectVersion=1.0.0

# Source Code
sonar.sources=src
sonar.tests=src/test
sonar.test.inclusions=**/*.test.ts,**/*.test.tsx,**/*.spec.ts,**/*.spec.tsx

# Exclusions
sonar.exclusions=**/node_modules/**,**/dist/**,**/build/**,**/coverage/**,**/*.d.ts,**/public/**

# Coverage
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.coverage.exclusions=**/*.test.ts,**/*.test.tsx,**/*.spec.ts,**/*.spec.tsx,**/test/**,**/mocks/**

# Language Settings
sonar.typescript.node=node

# Quality Gate
sonar.qualitygate.wait=true

# Duplication
sonar.cpd.exclusions=**/*.test.ts,**/*.test.tsx

# Issues
sonar.issue.ignore.multicriteria=e1,e2,e3

# Ignore console.log in development files
sonar.issue.ignore.multicriteria.e1.ruleKey=typescript:S2228
sonar.issue.ignore.multicriteria.e1.resourceKey=**/dev/**

# Ignore TODO comments
sonar.issue.ignore.multicriteria.e2.ruleKey=typescript:S1135
sonar.issue.ignore.multicriteria.e2.resourceKey=**/*

# Ignore test file complexity
sonar.issue.ignore.multicriteria.e3.ruleKey=typescript:S3776
sonar.issue.ignore.multicriteria.e3.resourceKey=**/*.test.ts,**/*.test.tsx
