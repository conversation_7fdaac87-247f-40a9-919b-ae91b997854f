
import { <PERSON> } from "react-router-dom";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Clock, Eye, Heart, MessageCircle, Lock } from "lucide-react";
import { Article } from "@/types/blog";
import { cn } from "@/lib/utils";

interface ArticleCardProps {
  article: Article;
  variant?: "default" | "featured" | "compact";
  className?: string;
}

export default function ArticleCard({ 
  article, 
  variant = "default",
  className 
}: ArticleCardProps) {
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(date);
  };

  const cardVariants = {
    default: "group cursor-pointer",
    featured: "group cursor-pointer border-2 border-blue-200",
    compact: "group cursor-pointer"
  };

  const imageVariants = {
    default: "h-48",
    featured: "h-64",
    compact: "h-32"
  };

  return (
    <motion.div
      whileHover={{ y: -4 }}
      transition={{ duration: 0.2 }}
      className={className}
    >
      <Card className={cn(cardVariants[variant], "overflow-hidden border-0 shadow-sm hover:shadow-lg transition-all duration-300 h-full flex flex-col")}>
        <div className="flex flex-col h-full">
          <div className="relative">
            {article.featuredImage && (
              <div className={cn("overflow-hidden", imageVariants[variant])}>
                <img
                  src={article.featuredImage}
                  alt={article.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
            )}

            {article.isPremium && (
              <div className="absolute top-3 right-3">
                <Badge className="bg-yellow-500 text-white">
                  <Lock className="w-3 h-3 mr-1" />
                  Premium
                </Badge>
              </div>
            )}

            <div className="absolute top-3 left-3">
              <Badge variant="secondary" className="bg-white/90 text-gray-800">
                {article.category.name}
              </Badge>
            </div>
          </div>

          <CardContent className="p-4 space-y-3 flex flex-col flex-1">
            <div className="space-y-2 flex-1">
              <h3 className={cn(
                "font-bold leading-tight group-hover:text-blue-600 transition-colors line-clamp-2",
                variant === "featured" ? "text-xl" : "text-lg",
                variant === "compact" ? "text-base" : ""
              )}>
                {article.title}
              </h3>

              {variant !== "compact" && (
                <p className="text-gray-600 text-sm line-clamp-3 flex-1">
                  {article.excerpt}
                </p>
              )}
            </div>

            <div className="mt-auto space-y-3">
              <div className="flex items-center justify-between text-xs text-gray-500">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-1">
                    <Avatar className="w-5 h-5">
                      <AvatarImage src={article.author.avatar} />
                      <AvatarFallback className="text-xs">
                        {article.author.name.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <span>{article.author.name}</span>
                  </div>

                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3" />
                    <span>{article.readTime} min read</span>
                  </div>
                </div>

                <span>{formatDate(article.publishedAt)}</span>
              </div>

              {variant !== "compact" && (
                <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Eye className="w-3 h-3" />
                      <span>{article.views.toLocaleString()}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Heart className="w-3 h-3" />
                      <span>{article.likes}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <MessageCircle className="w-3 h-3" />
                      <span>{article.comments}</span>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-1">
                    {article.tags.slice(0, 2).map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Read More Button - Always Visible */}
              <div className="pt-3">
                <Link to={`/articles/${article.slug}`} className="block">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                  >
                    Read More
                  </motion.button>
                </Link>
              </div>
            </div>
          </CardContent>
        </div>
      </Card>
    </motion.div>
  );
}
