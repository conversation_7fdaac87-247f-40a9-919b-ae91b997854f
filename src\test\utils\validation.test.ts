import { describe, it, expect } from 'vitest';
import { Validator, sanitizers, patterns, validationUtils, schemas } from '../../utils/validation';

describe('Validation Utilities', () => {
  describe('Sanitizers', () => {
    describe('html sanitizer', () => {
      it('removes script tags', () => {
        const input = '<p>Hello</p><script>alert("xss")</script>';
        const result = sanitizers.html(input);
        expect(result).toBe('<p>Hello</p>');
        expect(result).not.toContain('script');
      });

      it('removes iframe tags', () => {
        const input = '<p>Hello</p><iframe src="evil.com"></iframe>';
        const result = sanitizers.html(input);
        expect(result).toBe('<p>Hello</p>');
        expect(result).not.toContain('iframe');
      });

      it('removes javascript: protocol', () => {
        const input = '<a href="javascript:alert(1)">Click</a>';
        const result = sanitizers.html(input);
        expect(result).not.toContain('javascript:');
      });

      it('removes event handlers', () => {
        const input = '<p onclick="alert(1)">Hello</p>';
        const result = sanitizers.html(input);
        expect(result).not.toContain('onclick');
      });

      it('allows safe HTML tags', () => {
        const input = '<p><strong>Bold</strong> and <em>italic</em> text</p>';
        const result = sanitizers.html(input);
        expect(result).toContain('<p>');
        expect(result).toContain('<strong>');
        expect(result).toContain('<em>');
      });
    });

    describe('stripHtml sanitizer', () => {
      it('removes all HTML tags', () => {
        const input = '<p>Hello <strong>world</strong></p>';
        const result = sanitizers.stripHtml(input);
        expect(result).toBe('Hello world');
      });
    });

    describe('text sanitizer', () => {
      it('removes angle brackets', () => {
        const input = 'Hello <world>';
        const result = sanitizers.text(input);
        expect(result).toBe('Hello world');
      });

      it('removes javascript protocol', () => {
        const input = 'javascript:alert(1)';
        const result = sanitizers.text(input);
        expect(result).not.toContain('javascript:');
      });

      it('trims whitespace', () => {
        const input = '  Hello world  ';
        const result = sanitizers.text(input);
        expect(result).toBe('Hello world');
      });
    });

    describe('email sanitizer', () => {
      it('converts to lowercase and trims', () => {
        const input = '  <EMAIL>  ';
        const result = sanitizers.email(input);
        expect(result).toBe('<EMAIL>');
      });
    });

    describe('url sanitizer', () => {
      it('validates and returns valid URLs', () => {
        const input = 'https://example.com/path';
        const result = sanitizers.url(input);
        expect(result).toBe('https://example.com/path');
      });

      it('rejects non-http protocols', () => {
        const input = 'javascript:alert(1)';
        const result = sanitizers.url(input);
        expect(result).toBe('');
      });

      it('rejects invalid URLs', () => {
        const input = 'not-a-url';
        const result = sanitizers.url(input);
        expect(result).toBe('');
      });
    });

    describe('slug sanitizer', () => {
      it('creates valid slugs', () => {
        const input = 'Hello World! 123';
        const result = sanitizers.slug(input);
        expect(result).toBe('hello-world-123');
      });

      it('removes special characters', () => {
        const input = 'Hello@#$%World';
        const result = sanitizers.slug(input);
        expect(result).toBe('helloworld');
      });

      it('handles multiple spaces and dashes', () => {
        const input = 'Hello   ---   World';
        const result = sanitizers.slug(input);
        expect(result).toBe('hello-world');
      });
    });
  });

  describe('Patterns', () => {
    describe('email pattern', () => {
      it('validates correct emails', () => {
        expect(patterns.email.test('<EMAIL>')).toBe(true);
        expect(patterns.email.test('<EMAIL>')).toBe(true);
      });

      it('rejects invalid emails', () => {
        expect(patterns.email.test('invalid-email')).toBe(false);
        expect(patterns.email.test('@example.com')).toBe(false);
        expect(patterns.email.test('test@')).toBe(false);
      });
    });

    describe('password pattern', () => {
      it('validates strong passwords', () => {
        expect(patterns.password.test('StrongPass123!')).toBe(true);
        expect(patterns.password.test('MyP@ssw0rd')).toBe(true);
      });

      it('rejects weak passwords', () => {
        expect(patterns.password.test('weak')).toBe(false);
        expect(patterns.password.test('password123')).toBe(false);
        expect(patterns.password.test('PASSWORD123')).toBe(false);
        expect(patterns.password.test('Pass123')).toBe(false); // No special char
      });
    });

    describe('url pattern', () => {
      it('validates URLs', () => {
        expect(patterns.url.test('https://example.com')).toBe(true);
        expect(patterns.url.test('http://www.example.com/path')).toBe(true);
      });

      it('rejects invalid URLs', () => {
        expect(patterns.url.test('not-a-url')).toBe(false);
        expect(patterns.url.test('ftp://example.com')).toBe(false);
      });
    });
  });

  describe('Validator', () => {
    it('validates required fields', () => {
      const schema = {
        name: { required: true, type: 'string' },
        email: { required: true, type: 'email' },
      };

      const validator = new Validator(schema);
      const result = validator.validate({});

      expect(result.isValid).toBe(false);
      expect(result.errors.name).toContain('name is required');
      expect(result.errors.email).toContain('email is required');
    });

    it('validates field types', () => {
      const schema = {
        age: { type: 'number' },
        active: { type: 'boolean' },
        tags: { type: 'array' },
      };

      const validator = new Validator(schema);
      const result = validator.validate({
        age: 'not-a-number',
        active: 'not-a-boolean',
        tags: 'not-an-array',
      });

      expect(result.isValid).toBe(false);
      expect(result.errors.age).toContain('must be a number');
      expect(result.errors.active).toContain('must be a boolean');
      expect(result.errors.tags).toContain('must be an array');
    });

    it('validates string length', () => {
      const schema = {
        username: { type: 'string', minLength: 3, maxLength: 20 },
      };

      const validator = new Validator(schema);
      
      const shortResult = validator.validate({ username: 'ab' });
      expect(shortResult.isValid).toBe(false);
      expect(shortResult.errors.username).toContain('must be at least 3 characters');

      const longResult = validator.validate({ username: 'a'.repeat(25) });
      expect(longResult.isValid).toBe(false);
      expect(longResult.errors.username).toContain('must be no more than 20 characters');

      const validResult = validator.validate({ username: 'validuser' });
      expect(validResult.isValid).toBe(true);
    });

    it('validates numeric ranges', () => {
      const schema = {
        score: { type: 'number', min: 0, max: 100 },
      };

      const validator = new Validator(schema);
      
      const lowResult = validator.validate({ score: -10 });
      expect(lowResult.isValid).toBe(false);
      expect(lowResult.errors.score).toContain('must be at least 0');

      const highResult = validator.validate({ score: 150 });
      expect(highResult.isValid).toBe(false);
      expect(highResult.errors.score).toContain('must be no more than 100');

      const validResult = validator.validate({ score: 85 });
      expect(validResult.isValid).toBe(true);
    });

    it('validates patterns', () => {
      const schema = {
        email: { type: 'string', pattern: patterns.email },
      };

      const validator = new Validator(schema);
      
      const invalidResult = validator.validate({ email: 'invalid-email' });
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors.email).toContain('format is invalid');

      const validResult = validator.validate({ email: '<EMAIL>' });
      expect(validResult.isValid).toBe(true);
    });

    it('validates allowed values', () => {
      const schema = {
        status: { type: 'string', allowedValues: ['active', 'inactive', 'pending'] },
      };

      const validator = new Validator(schema);
      
      const invalidResult = validator.validate({ status: 'unknown' });
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors.status).toContain('must be one of: active, inactive, pending');

      const validResult = validator.validate({ status: 'active' });
      expect(validResult.isValid).toBe(true);
    });

    it('runs custom validation', () => {
      const schema = {
        password: {
          type: 'string',
          custom: (value: string) => {
            if (value.includes('password')) {
              return 'Password cannot contain the word "password"';
            }
            return true;
          },
        },
      };

      const validator = new Validator(schema);
      
      const invalidResult = validator.validate({ password: 'mypassword123' });
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors.password).toContain('Password cannot contain the word "password"');

      const validResult = validator.validate({ password: 'mysecret123' });
      expect(validResult.isValid).toBe(true);
    });

    it('sanitizes input when enabled', () => {
      const schema = {
        name: { type: 'string', sanitize: true },
        email: { type: 'email', sanitize: true },
      };

      const validator = new Validator(schema);
      const result = validator.validate({
        name: '  <script>alert(1)</script>John  ',
        email: '  <EMAIL>  ',
      });

      expect(result.sanitizedData.name).toBe('John');
      expect(result.sanitizedData.email).toBe('<EMAIL>');
    });

    it('validates nested objects', () => {
      const schema = {
        user: {
          type: 'object',
          nested: {
            name: { required: true, type: 'string' },
            email: { required: true, type: 'email' },
          },
        },
      };

      const validator = new Validator(schema);
      const result = validator.validate({
        user: {
          name: '',
          email: 'invalid-email',
        },
      });

      expect(result.isValid).toBe(false);
      expect(result.errors.user).toContain('user.name: name is required');
      expect(result.errors.user).toContain('user.email: must be a valid email');
    });
  });

  describe('Predefined Schemas', () => {
    describe('userRegistration schema', () => {
      it('validates complete registration data', () => {
        const validData = {
          email: '<EMAIL>',
          password: 'StrongPass123!',
          confirmPassword: 'StrongPass123!',
          firstName: 'John',
          lastName: 'Doe',
          terms: true,
        };

        const result = validationUtils.validate(validData, schemas.userRegistration);
        expect(result.isValid).toBe(true);
      });

      it('rejects invalid registration data', () => {
        const invalidData = {
          email: 'invalid-email',
          password: 'weak',
          confirmPassword: 'different',
          firstName: '',
          lastName: '',
          terms: false,
        };

        const result = validationUtils.validate(invalidData, schemas.userRegistration);
        expect(result.isValid).toBe(false);
        expect(result.errors.email).toBeDefined();
        expect(result.errors.password).toBeDefined();
        expect(result.errors.firstName).toBeDefined();
        expect(result.errors.lastName).toBeDefined();
      });
    });

    describe('articleCreation schema', () => {
      it('validates article data', () => {
        const validData = {
          title: 'My Great Article Title',
          content: 'This is a long article content that meets the minimum length requirement for validation.',
          category: 'technology',
          isPremium: false,
        };

        const result = validationUtils.validate(validData, schemas.articleCreation);
        expect(result.isValid).toBe(true);
      });

      it('rejects short content', () => {
        const invalidData = {
          title: 'Title',
          content: 'Short',
          category: 'tech',
        };

        const result = validationUtils.validate(invalidData, schemas.articleCreation);
        expect(result.isValid).toBe(false);
        expect(result.errors.content).toContain('must be at least 100 characters');
      });
    });
  });

  describe('Validation Utils', () => {
    describe('checkPasswordStrength', () => {
      it('scores strong passwords highly', () => {
        const result = validationUtils.checkPasswordStrength('StrongPass123!');
        expect(result.score).toBe(5);
        expect(result.feedback).toHaveLength(0);
      });

      it('provides feedback for weak passwords', () => {
        const result = validationUtils.checkPasswordStrength('weak');
        expect(result.score).toBeLessThan(5);
        expect(result.feedback.length).toBeGreaterThan(0);
        expect(result.feedback).toContain('Use at least 8 characters');
      });
    });

    describe('generateSecureToken', () => {
      it('generates tokens of specified length', () => {
        const token = validationUtils.generateSecureToken(16);
        expect(token).toHaveLength(16);
      });

      it('generates different tokens each time', () => {
        const token1 = validationUtils.generateSecureToken();
        const token2 = validationUtils.generateSecureToken();
        expect(token1).not.toBe(token2);
      });
    });

    describe('sanitizeHtml', () => {
      it('sanitizes HTML content', () => {
        const input = '<p>Safe</p><script>alert(1)</script>';
        const result = validationUtils.sanitizeHtml(input);
        expect(result).toContain('<p>Safe</p>');
        expect(result).not.toContain('script');
      });
    });
  });
});
