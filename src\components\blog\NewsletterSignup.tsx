import React, { useState } from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/components/ui/use-toast";
import { Mail, CheckCircle, Loader2 } from "lucide-react";

interface NewsletterSignupProps {
  variant?: "default" | "inline" | "modal";
  className?: string;
}

export default function NewsletterSignup({ 
  variant = "default",
  className 
}: NewsletterSignupProps) {
  const [email, setEmail] = useState("");
  const [preferences, setPreferences] = useState({
    daily: true,
    weekly: true,
    breaking: false,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setIsSubscribed(true);
      toast({
        title: "Successfully subscribed!",
        description: "Welcome to The Chronicle newsletter. Check your email for confirmation.",
        duration: 5000,
      });
    } catch (error) {
      toast({
        title: "Subscription failed",
        description: "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isSubscribed) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className={className}
      >
        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-6 text-center">
            <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-green-800 mb-2">
              You're all set!
            </h3>
            <p className="text-green-700">
              Thank you for subscribing to The Chronicle newsletter.
            </p>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  const inlineVariant = variant === "inline";

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className={className}
    >
      <Card className={inlineVariant ? "bg-blue-50 border-blue-200" : "bg-gradient-to-br from-blue-600 to-purple-700 text-white border-0"}>
        <CardContent className="p-6">
          <div className="text-center mb-6">
            <Mail className={`w-12 h-12 mx-auto mb-4 ${inlineVariant ? "text-blue-600" : "text-white"}`} />
            <h3 className={`text-2xl font-bold mb-2 ${inlineVariant ? "text-gray-900" : "text-white"}`}>
              Stay Informed
            </h3>
            <p className={`${inlineVariant ? "text-gray-700" : "text-blue-100"}`}>
              Get the latest news, insights, and exclusive content delivered to your inbox.
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-3">
              <Input
                type="email"
                placeholder="Enter your email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className={`flex-1 ${inlineVariant ? "" : "bg-white/10 border-white/20 text-white placeholder:text-white/70"}`}
              />
              <Button
                type="submit"
                disabled={isLoading || !email}
                className={`${inlineVariant ? "bg-blue-600 hover:bg-blue-700" : "bg-white text-blue-600 hover:bg-gray-100"}`}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Subscribing...
                  </>
                ) : (
                  "Subscribe"
                )}
              </Button>
            </div>

            {!inlineVariant && (
              <div className="space-y-3">
                <p className="text-sm text-blue-100">Email preferences:</p>
                <div className="flex flex-wrap gap-4 text-sm">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="daily"
                      checked={preferences.daily}
                      onCheckedChange={(checked) =>
                        setPreferences(prev => ({ ...prev, daily: checked as boolean }))
                      }
                      className="border-white/30 data-[state=checked]:bg-white data-[state=checked]:text-blue-600"
                    />
                    <label htmlFor="daily" className="text-blue-100">Daily digest</label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="weekly"
                      checked={preferences.weekly}
                      onCheckedChange={(checked) =>
                        setPreferences(prev => ({ ...prev, weekly: checked as boolean }))
                      }
                      className="border-white/30 data-[state=checked]:bg-white data-[state=checked]:text-blue-600"
                    />
                    <label htmlFor="weekly" className="text-blue-100">Weekly roundup</label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="breaking"
                      checked={preferences.breaking}
                      onCheckedChange={(checked) =>
                        setPreferences(prev => ({ ...prev, breaking: checked as boolean }))
                      }
                      className="border-white/30 data-[state=checked]:bg-white data-[state=checked]:text-blue-600"
                    />
                    <label htmlFor="breaking" className="text-blue-100">Breaking news</label>
                  </div>
                </div>
              </div>
            )}

            <p className={`text-xs ${inlineVariant ? "text-gray-600" : "text-blue-200"}`}>
              By subscribing, you agree to our Privacy Policy and Terms of Service.
              You can unsubscribe at any time.
            </p>
          </form>
        </CardContent>
      </Card>
    </motion.div>
  );
}
