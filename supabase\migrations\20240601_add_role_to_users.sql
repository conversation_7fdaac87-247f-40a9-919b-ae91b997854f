-- Add role column to users table
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS role TEXT NOT NULL DEFAULT 'user';

-- Create policy for admin users to update other users' roles
DROP POLICY IF EXISTS "Ad<PERSON> can update user roles" ON public.users;
CREATE POLICY "Admins can update user roles"
ON public.users
FOR UPDATE
USING (auth.uid() IN (SELECT id FROM public.users WHERE role = 'admin'))
WITH CHECK (auth.uid() IN (SELECT id FROM public.users WHERE role = 'admin'));

-- Create a function to set the first user as admin
CREATE OR REPLACE FUNCTION public.set_first_user_as_admin()
RETURNS TRIGGER AS $$
BEGIN
  IF (SELECT COUNT(*) FROM public.users) = 1 THEN
    UPDATE public.users SET role = 'admin' WHERE id = NEW.id;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger to call the function when a new user is added
DROP TRIGGER IF EXISTS on_first_user_created ON public.users;
CREATE TRIGGER on_first_user_created
  AFTER INSERT ON public.users
  FOR EACH ROW EXECUTE FUNCTION public.set_first_user_as_admin();

-- Enable realtime for users table
alter publication supabase_realtime add table public.users;
