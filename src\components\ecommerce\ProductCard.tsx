import React from "react";
import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Product } from "@/types/ecommerce";
import { ShoppingCart, Heart, Star, Download, Package } from "lucide-react";
import { cn } from "@/lib/utils";

interface ProductCardProps {
  product: Product;
  onAddToCart?: (productId: string) => void;
  onToggleWishlist?: (productId: string) => void;
  isInWishlist?: boolean;
  variant?: "default" | "compact" | "featured";
  className?: string;
}

export default function ProductCard({
  product,
  onAddToCart,
  onToggleWishlist,
  isInWishlist = false,
  variant = "default",
  className
}: ProductCardProps) {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onAddToCart?.(product.id);
  };

  const handleToggleWishlist = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onToggleWishlist?.(product.id);
  };

  const isOutOfStock = product.status === 'out-of-stock' || product.stock === 0;
  const hasDiscount = product.comparePrice && product.comparePrice > product.price;
  const discountPercentage = hasDiscount 
    ? Math.round(((product.comparePrice! - product.price) / product.comparePrice!) * 100)
    : 0;

  return (
    <motion.div
      whileHover={{ y: -4 }}
      transition={{ duration: 0.2 }}
      className={className}
    >
      <Card className="group cursor-pointer overflow-hidden border-0 shadow-sm hover:shadow-lg transition-all duration-300">
        <Link to={`/products/${product.slug}`}>
          <div className="relative">
            {/* Product Image */}
            <div className={cn(
              "overflow-hidden bg-gray-100",
              variant === "compact" ? "h-40" : "h-48"
            )}>
              {product.images.length > 0 ? (
                <img
                  src={product.images[0]}
                  alt={product.name}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gray-200">
                  <Package className="w-12 h-12 text-gray-400" />
                </div>
              )}
            </div>

            {/* Badges */}
            <div className="absolute top-3 left-3 flex flex-col gap-2">
              {product.featured && (
                <Badge className="bg-yellow-500 text-white">
                  Featured
                </Badge>
              )}
              {hasDiscount && (
                <Badge className="bg-red-500 text-white">
                  -{discountPercentage}%
                </Badge>
              )}
              {product.type === 'digital' && (
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  <Download className="w-3 h-3 mr-1" />
                  Digital
                </Badge>
              )}
              {isOutOfStock && (
                <Badge variant="destructive">
                  Out of Stock
                </Badge>
              )}
            </div>

            {/* Wishlist Button */}
            <button
              type="button"
              onClick={handleToggleWishlist}
              aria-label={isInWishlist ? "Remove from wishlist" : "Add to wishlist"}
              className="absolute top-3 right-3 p-2 bg-white/80 backdrop-blur-sm rounded-full hover:bg-white transition-colors"
            >
              <Heart 
                className={cn(
                  "w-4 h-4 transition-colors",
                  isInWishlist ? "fill-red-500 text-red-500" : "text-gray-600"
                )}
              />
            </button>
          </div>

          <CardContent className="p-4 space-y-3">
            {/* Product Category */}
            <div className="flex items-center justify-between">
              <Badge variant="outline" className="text-xs">
                {product.category.name}
              </Badge>
              {product.type === 'physical' && (
                <span className="text-xs text-gray-500">
                  {product.stock} in stock
                </span>
              )}
            </div>

            {/* Product Name */}
            <h3 className={cn(
              "font-semibold leading-tight group-hover:text-blue-600 transition-colors line-clamp-2",
              variant === "compact" ? "text-sm" : "text-base"
            )}>
              {product.name}
            </h3>

            {/* Product Description */}
            {variant !== "compact" && product.shortDescription && (
              <p className="text-sm text-gray-600 line-clamp-2">
                {product.shortDescription}
              </p>
            )}

            {/* Price */}
            <div className="flex items-center space-x-2">
              <span className="text-lg font-bold text-gray-900">
                {formatPrice(product.price)}
              </span>
              {hasDiscount && (
                <span className="text-sm text-gray-500 line-through">
                  {formatPrice(product.comparePrice!)}
                </span>
              )}
            </div>

            {/* Tags */}
            {variant !== "compact" && product.tags.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {product.tags.slice(0, 3).map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            )}

            {/* Add to Cart Button */}
            <Button
              onClick={handleAddToCart}
              disabled={isOutOfStock}
              className="w-full"
              variant={isOutOfStock ? "outline" : "default"}
            >
              <ShoppingCart className="w-4 h-4 mr-2" />
              {isOutOfStock ? "Out of Stock" : "Add to Cart"}
            </Button>
          </CardContent>
        </Link>
      </Card>
    </motion.div>
  );
}
