import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  TrendingUp, 
  TrendingDown,
  Activity,
  Users,
  Eye,
  Clock,
  Zap,
  AlertCircle,
  RefreshCw,
  Download
} from 'lucide-react';
import { errorTracker } from '@/utils/errorTracking';
import { performanceMonitor } from '@/utils/performanceMonitoring';
import { analytics } from '@/utils/analytics';

interface MonitoringDashboardProps {
  className?: string;
}

export function MonitoringDashboard({ className }: MonitoringDashboardProps) {
  const [errorStats, setErrorStats] = useState<any>(null);
  const [performanceStats, setPerformanceStats] = useState<any>(null);
  const [analyticsStats, setAnalyticsStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  useEffect(() => {
    loadDashboardData();
    const interval = setInterval(loadDashboardData, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    setIsLoading(true);
    try {
      const [errors, performance, analyticsData] = await Promise.all([
        Promise.resolve(errorTracker.getErrorStats()),
        Promise.resolve(performanceMonitor.getPerformanceSummary()),
        Promise.resolve(analytics.getAnalyticsSummary()),
      ]);

      setErrorStats(errors);
      setPerformanceStats(performance);
      setAnalyticsStats(analyticsData);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const exportData = (type: 'errors' | 'performance' | 'analytics') => {
    let data: any;
    let filename: string;

    switch (type) {
      case 'errors':
        data = errorTracker.getErrors();
        filename = 'error-report.json';
        break;
      case 'performance':
        data = performanceMonitor.getMetrics();
        filename = 'performance-report.json';
        break;
      case 'analytics':
        data = analytics.getEvents();
        filename = 'analytics-report.json';
        break;
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  const getPerformanceRating = (score: number) => {
    if (score >= 90) return { label: 'Excellent', color: 'text-green-600', icon: CheckCircle };
    if (score >= 70) return { label: 'Good', color: 'text-blue-600', icon: CheckCircle };
    if (score >= 50) return { label: 'Needs Improvement', color: 'text-yellow-600', icon: AlertTriangle };
    return { label: 'Poor', color: 'text-red-600', icon: XCircle };
  };

  if (isLoading && !errorStats) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Monitoring Dashboard</h1>
          <p className="text-gray-600">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </p>
        </div>
        <Button onClick={loadDashboardData} disabled={isLoading}>
          <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* System Health */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Health</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">Healthy</div>
            <p className="text-xs text-muted-foreground">
              {errorStats?.critical || 0} critical issues
            </p>
          </CardContent>
        </Card>

        {/* Total Errors */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Errors</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{errorStats?.total || 0}</div>
            <p className="text-xs text-muted-foreground">
              {errorStats?.last24Hours || 0} in last 24h
            </p>
          </CardContent>
        </Card>

        {/* Performance Score */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Performance</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {performanceStats?.webVitals?.score || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Web Vitals Score
            </p>
          </CardContent>
        </Card>

        {/* Active Users */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analyticsStats?.last24Hours?.uniqueUsers || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Last 24 hours
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Monitoring */}
      <Tabs defaultValue="errors" className="space-y-4">
        <TabsList>
          <TabsTrigger value="errors">Error Tracking</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* Error Tracking Tab */}
        <TabsContent value="errors" className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Error Tracking</h2>
            <Button variant="outline" onClick={() => exportData('errors')}>
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Error Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Error Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span>Total Errors</span>
                  <Badge variant="secondary">{errorStats?.total || 0}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>Unresolved</span>
                  <Badge variant="destructive">{errorStats?.unresolved || 0}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>Critical</span>
                  <Badge className="bg-red-500">{errorStats?.critical || 0}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>Last 24h</span>
                  <Badge variant="outline">{errorStats?.last24Hours || 0}</Badge>
                </div>
              </CardContent>
            </Card>

            {/* Top Errors */}
            <Card>
              <CardHeader>
                <CardTitle>Top Errors</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {errorStats?.topErrors?.slice(0, 5).map((error: any, index: number) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          {error.message}
                        </p>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge 
                            className={`${getSeverityColor(error.severity)} text-white text-xs`}
                          >
                            {error.severity}
                          </Badge>
                          <span className="text-xs text-gray-500">
                            {error.count} occurrences
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Error Types Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Error Types</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {Object.entries(errorStats?.byType || {}).map(([type, count]) => (
                  <div key={type} className="text-center">
                    <div className="text-2xl font-bold">{count as number}</div>
                    <div className="text-sm text-gray-600 capitalize">{type}</div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Performance Monitoring</h2>
            <Button variant="outline" onClick={() => exportData('performance')}>
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Web Vitals */}
            <Card>
              <CardHeader>
                <CardTitle>Core Web Vitals</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {Object.entries(performanceStats?.webVitals?.vitals || {}).map(([vital, data]: [string, any]) => {
                  const rating = getPerformanceRating(data.rating === 'good' ? 100 : data.rating === 'needs-improvement' ? 60 : 30);
                  const Icon = rating.icon;
                  
                  return (
                    <div key={vital} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Icon className={`w-4 h-4 ${rating.color}`} />
                        <span className="font-medium">{vital}</span>
                      </div>
                      <div className="text-right">
                        <div className="font-bold">{data.value.toFixed(2)}</div>
                        <div className={`text-xs ${rating.color}`}>{rating.label}</div>
                      </div>
                    </div>
                  );
                })}
              </CardContent>
            </Card>

            {/* Performance Metrics */}
            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span>Avg API Response</span>
                  <span className="font-bold">
                    {performanceStats?.averageApiResponseTime?.toFixed(0) || 0}ms
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Avg Component Render</span>
                  <span className="font-bold">
                    {performanceStats?.averageComponentRenderTime?.toFixed(0) || 0}ms
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Memory Usage</span>
                  <span className="font-bold">
                    {performanceStats?.memoryUsage ? 
                      `${(performanceStats.memoryUsage.value / 1024 / 1024).toFixed(1)}MB` : 
                      'N/A'
                    }
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Bundle Size</span>
                  <span className="font-bold">
                    {performanceStats?.bundleSize?.scripts ? 
                      `${(performanceStats.bundleSize.scripts.value / 1024).toFixed(1)}KB` : 
                      'N/A'
                    }
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Slow Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Slowest Operations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {performanceStats?.topSlowMetrics?.slice(0, 5).map((metric: any, index: number) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm font-medium">{metric.name}</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-bold">{metric.value.toFixed(0)}ms</span>
                      {metric.value > 1000 && (
                        <AlertTriangle className="w-4 h-4 text-yellow-500" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Analytics Overview</h2>
            <Button variant="outline" onClick={() => exportData('analytics')}>
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Usage Statistics */}
            <Card>
              <CardHeader>
                <CardTitle>Usage Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span>Total Events</span>
                  <span className="font-bold">{analyticsStats?.totalEvents || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Page Views</span>
                  <span className="font-bold">{analyticsStats?.totalPageViews || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Total Users</span>
                  <span className="font-bold">{analyticsStats?.totalUsers || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Active Users (24h)</span>
                  <span className="font-bold">{analyticsStats?.last24Hours?.uniqueUsers || 0}</span>
                </div>
              </CardContent>
            </Card>

            {/* Top Events */}
            <Card>
              <CardHeader>
                <CardTitle>Top Events</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analyticsStats?.topEvents?.slice(0, 5).map((event: any, index: number) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm font-medium">{event.name}</span>
                      <Badge variant="secondary">{event.count}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Top Pages */}
          <Card>
            <CardHeader>
              <CardTitle>Popular Pages</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {analyticsStats?.topPages?.slice(0, 10).map((page: any, index: number) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm font-medium">{page.path}</span>
                    <Badge variant="outline">{page.count} views</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default MonitoringDashboard;
