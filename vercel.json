{"version": 2, "name": "the-chronicle", "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "/(.*)", "dest": "/index.html"}], "env": {"VITE_SUPABASE_URL": "@supabase_url", "VITE_SUPABASE_ANON_KEY": "@supabase_anon_key", "VITE_STRIPE_PUBLISHABLE_KEY": "@stripe_publishable_key", "VITE_GOOGLE_ANALYTICS_ID": "@google_analytics_id"}, "build": {"env": {"VITE_SUPABASE_URL": "@supabase_url", "VITE_SUPABASE_ANON_KEY": "@supabase_anon_key", "VITE_STRIPE_PUBLISHABLE_KEY": "@stripe_publishable_key", "VITE_GOOGLE_ANALYTICS_ID": "@google_analytics_id"}}, "functions": {"app/api/**/*.ts": {"runtime": "nodejs18.x"}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}, {"source": "/((?!api).*)", "destination": "/index.html"}], "redirects": [{"source": "/admin", "destination": "/dashboard/admin", "permanent": true}, {"source": "/login", "destination": "/auth/login", "permanent": false}, {"source": "/register", "destination": "/auth/register", "permanent": false}]}