{"buildCommand": "npx vite build", "outputDirectory": "dist", "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}, {"source": "/((?!api).*)", "destination": "/index.html"}], "redirects": [{"source": "/admin", "destination": "/dashboard/admin", "permanent": true}, {"source": "/login", "destination": "/auth/login", "permanent": false}, {"source": "/register", "destination": "/auth/register", "permanent": false}]}