import React, { useState } from "react";
import { Routes, Route, useLocation } from "react-router-dom";
import Sidebar from "../dashboard/layout/Sidebar";
import { Navbar } from "@/components/layout/Navbar";
import { DashboardHome } from "../dashboard/pages/DashboardHome";
import { CreateArticle } from "../dashboard/pages/CreateArticle";
import { MyArticles } from "../dashboard/pages/MyArticles";
import { Favorites } from "../dashboard/pages/Favorites";
import { Comments } from "../dashboard/pages/Comments";
import { Analytics } from "../dashboard/pages/Analytics";
import { Subscription } from "../dashboard/pages/Subscription";
import { Settings } from "../dashboard/pages/Settings";
import { AdminPanel } from "../dashboard/pages/AdminPanel";
import { UsersManagement } from "../dashboard/pages/UsersManagement";
import { useAuth } from "../../../supabase/auth";

const Dashboard = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const { user, isAdmin } = useAuth();
  const location = useLocation();

  // Determine if we're in admin mode
  const isAdminRoute = location.pathname.startsWith('/admin');

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar variant={isAdminRoute ? "admin" : "dashboard"} />

      <div className="flex h-[calc(100vh-64px)]">
        <Sidebar
          isAdmin={isAdminRoute}
          collapsed={sidebarCollapsed}
          onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
        />

        <main className="flex-1 overflow-auto">
          <div className="container mx-auto p-6">
            <Routes>
              {/* User Dashboard Routes */}
              <Route path="/" element={<DashboardHome />} />
              <Route path="/articles" element={<MyArticles />} />
              <Route path="/articles/create" element={<CreateArticle />} />
              <Route path="/favorites" element={<Favorites />} />
              <Route path="/comments" element={<Comments />} />
              <Route path="/analytics" element={<Analytics />} />
              <Route path="/subscription" element={<Subscription />} />
              <Route path="/settings" element={<Settings />} />

              {/* Admin Routes */}
              {isAdmin && (
                <>
                  <Route path="/admin" element={<AdminPanel />} />
                  <Route path="/admin/analytics" element={<div>Admin Analytics</div>} />
                  <Route path="/admin/content/*" element={<div>Content Management</div>} />
                  <Route path="/admin/products/*" element={<div>Product Management</div>} />
                  <Route path="/admin/users" element={<UsersManagement />} />
                  <Route path="/admin/newsletter" element={<div>Newsletter Management</div>} />
                </>
              )}
            </Routes>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Dashboard;
