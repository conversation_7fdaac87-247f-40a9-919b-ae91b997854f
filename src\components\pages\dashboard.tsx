import React, { useState, useEffect } from "react";
import { Routes, Route, useLocation } from "react-router-dom";
import Sidebar from "../dashboard/layout/Sidebar";
import { Navbar } from "@/components/layout/Navbar";
import { DashboardHome } from "../dashboard/pages/DashboardHome";
import { CreateArticle } from "../dashboard/pages/CreateArticle";
import { MyArticles } from "../dashboard/pages/MyArticles";
import { Favorites } from "../dashboard/pages/Favorites";
import { Comments } from "../dashboard/pages/Comments";
import { Analytics } from "../dashboard/pages/Analytics";
import { Subscription } from "../dashboard/pages/Subscription";
import { Settings } from "../dashboard/pages/Settings";
import AdminPanel from "../dashboard/AdminPanel"; // Use the consolidated admin panel
import { UsersManagement } from "../dashboard/pages/UsersManagement";
import { UserDashboard } from "../dashboard/pages/UserDashboard";
import { useAuth } from "../../../supabase/auth";
import { Button } from "@/components/ui/button";
import { Menu, X } from "lucide-react";

// Admin-only components
import { EcommerceDashboard } from "../dashboard/admin/EcommerceDashboard";
import { ProductManagement } from "../dashboard/admin/ProductManagement";
import { OrderManagement } from "../dashboard/admin/OrderManagement";
import { ContactMessages } from "../dashboard/admin/ContactMessages";
import { Monitoring } from "../dashboard/admin/Monitoring";
import { Performance } from "../dashboard/admin/Performance";
import { Privacy } from "../dashboard/admin/Privacy";

const Dashboard = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const { user, isAdmin } = useAuth();
  const location = useLocation();

  // Check if mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setSidebarCollapsed(true);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Close mobile sidebar when route changes
  useEffect(() => {
    if (isMobile) {
      setSidebarOpen(false);
    }
  }, [location.pathname, isMobile]);

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar variant="dashboard" />

      <div className="flex h-[calc(100vh-64px)]">
        {/* Mobile Overlay */}
        {isMobile && sidebarOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Sidebar */}
        <div className={`
          ${isMobile ? 'fixed inset-y-0 left-0 z-50' : 'relative'}
          ${isMobile && !sidebarOpen ? '-translate-x-full' : 'translate-x-0'}
          transition-transform duration-300 ease-in-out
          ${isMobile ? 'w-64' : sidebarCollapsed ? 'w-16' : 'w-[280px]'}
        `}>
          <Sidebar
            isAdmin={isAdmin}
            collapsed={!isMobile && sidebarCollapsed}
            onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
            isMobile={isMobile}
            onMobileClose={() => setSidebarOpen(false)}
          />
        </div>

        {/* Main Content */}
        <main className="flex-1 overflow-auto">
          {/* Mobile Menu Button */}
          {isMobile && (
            <div className="lg:hidden p-4 border-b border-gray-200 bg-white">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="flex items-center"
              >
                {sidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
                <span className="ml-2">Menu</span>
              </Button>
            </div>
          )}

          <div className="container mx-auto p-4 lg:p-6">
            <Routes>
              {/* Default route - redirect based on user role */}
              <Route
                path="/"
                element={isAdmin ? <AdminPanel /> : <UserDashboard />}
              />

              {/* Admin-only routes */}
              {isAdmin ? (
                <>
                  <Route path="/admin" element={<AdminPanel />} />
                  <Route path="/content" element={<MyArticles />} />
                  <Route path="/create-article" element={<CreateArticle />} />
                  <Route path="/ecommerce" element={<EcommerceDashboard />} />
                  <Route path="/products" element={<ProductManagement />} />
                  <Route path="/orders" element={<OrderManagement />} />
                  <Route path="/users" element={<UsersManagement />} />
                  <Route path="/messages" element={<ContactMessages />} />
                  <Route path="/analytics" element={<Analytics />} />
                  <Route path="/monitoring" element={<Monitoring />} />
                  <Route path="/performance" element={<Performance />} />
                  <Route path="/privacy" element={<Privacy />} />
                  <Route path="/settings" element={<Settings />} />
                </>
              ) : (
                <>
                  {/* User-only routes */}
                  <Route path="/favorites" element={<Favorites />} />
                  <Route path="/comments" element={<Comments />} />
                  <Route path="/analytics" element={<Analytics />} />
                  <Route path="/subscription" element={<Subscription />} />
                  <Route path="/settings" element={<Settings />} />
                </>
              )}
            </Routes>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Dashboard;
