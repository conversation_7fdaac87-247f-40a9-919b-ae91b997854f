import React, { useState } from "react";
import { Routes, Route, useLocation } from "react-router-dom";
import Sidebar from "../dashboard/layout/Sidebar";
import { Navbar } from "@/components/layout/Navbar";
import { DashboardHome } from "../dashboard/pages/DashboardHome";
import { CreateArticle } from "../dashboard/pages/CreateArticle";
import { MyArticles } from "../dashboard/pages/MyArticles";
import { Favorites } from "../dashboard/pages/Favorites";
import { Comments } from "../dashboard/pages/Comments";
import { Analytics } from "../dashboard/pages/Analytics";
import { Subscription } from "../dashboard/pages/Subscription";
import { Settings } from "../dashboard/pages/Settings";
import { AdminPanel } from "../dashboard/pages/AdminPanel";
import { UsersManagement } from "../dashboard/pages/UsersManagement";
import { UserDashboard } from "../dashboard/pages/UserDashboard";
import { useAuth } from "../../../supabase/auth";

const Dashboard = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const { user, isAdmin } = useAuth();
  const location = useLocation();

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar variant="dashboard" />

      <div className="flex h-[calc(100vh-64px)]">
        <Sidebar
          isAdmin={isAdmin}
          collapsed={sidebarCollapsed}
          onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
        />

        <main className="flex-1 overflow-auto">
          <div className="container mx-auto p-6">
            <Routes>
              {/* Default route - redirect based on user role */}
              <Route
                path="/"
                element={isAdmin ? <DashboardHome /> : <UserDashboard />}
              />

              {/* Admin-only routes */}
              {isAdmin && (
                <>
                  <Route path="/admin" element={<AdminPanel />} />
                  <Route path="/articles" element={<MyArticles />} />
                  <Route path="/articles/create" element={<CreateArticle />} />
                  <Route path="/analytics" element={<Analytics />} />
                  <Route path="/users" element={<UsersManagement />} />
                </>
              )}

              {/* User routes (limited functionality) */}
              <Route path="/favorites" element={<Favorites />} />
              <Route path="/comments" element={<Comments />} />
              <Route path="/subscription" element={<Subscription />} />
              <Route path="/settings" element={<Settings />} />

              {/* User analytics - only for followed content */}
              {!isAdmin && <Route path="/analytics" element={<Analytics />} />}
            </Routes>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Dashboard;
