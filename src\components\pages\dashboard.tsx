import React, { useState } from "react";
import { Routes, Route, useLocation } from "react-router-dom";
import Sidebar from "../dashboard/layout/Sidebar";
import { Navbar } from "@/components/layout/Navbar";
import { DashboardHome } from "../dashboard/pages/DashboardHome";
import { CreateArticle } from "../dashboard/pages/CreateArticle";
import { AdminPanel } from "../dashboard/pages/AdminPanel";
import { useAuth } from "../../../supabase/auth";

const Dashboard = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const { user, isAdmin } = useAuth();
  const location = useLocation();

  // Determine if we're in admin mode
  const isAdminRoute = location.pathname.startsWith('/admin');

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar variant={isAdminRoute ? "admin" : "dashboard"} />

      <div className="flex h-[calc(100vh-64px)]">
        <Sidebar
          isAdmin={isAdminRoute}
          collapsed={sidebarCollapsed}
          onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
        />

        <main className="flex-1 overflow-auto">
          <div className="container mx-auto p-6">
            <Routes>
              {/* User Dashboard Routes */}
              <Route path="/" element={<DashboardHome />} />
              <Route path="/articles" element={<div>My Articles</div>} />
              <Route path="/articles/create" element={<CreateArticle />} />
              <Route path="/favorites" element={<div>Favorites</div>} />
              <Route path="/comments" element={<div>Comments</div>} />
              <Route path="/analytics" element={<div>Analytics</div>} />
              <Route path="/subscription" element={<div>Subscription</div>} />
              <Route path="/settings" element={<div>Settings</div>} />

              {/* Admin Routes */}
              {isAdmin && (
                <>
                  <Route path="/admin" element={<AdminPanel />} />
                  <Route path="/admin/analytics" element={<div>Admin Analytics</div>} />
                  <Route path="/admin/content/*" element={<div>Content Management</div>} />
                  <Route path="/admin/products/*" element={<div>Product Management</div>} />
                  <Route path="/admin/users" element={<div>User Management</div>} />
                  <Route path="/admin/newsletter" element={<div>Newsletter Management</div>} />
                </>
              )}
            </Routes>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Dashboard;
