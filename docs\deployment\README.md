# Deployment Guide

This guide covers deploying The Chronicle to various platforms and environments.

## Overview

The Chronicle supports multiple deployment strategies:

- **Vercel** (Recommended) - Serverless deployment with automatic CI/CD
- **Docker** - Containerized deployment for any platform
- **Static Hosting** - Build and deploy to any static hosting provider
- **Self-Hosted** - Deploy on your own infrastructure

## Prerequisites

- Node.js 18+
- npm or yarn
- Git
- Supabase project
- Stripe account (for payments)

## Environment Variables

Create environment files for each deployment environment:

### Production (.env.production)

```env
# Application
NODE_ENV=production
VITE_APP_URL=https://thechronicle.com

# Supabase
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_production_anon_key

# Stripe
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_key

# Analytics
VITE_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Error Tracking
VITE_SENTRY_DSN=https://your-sentry-dsn

# Build Information
VITE_BUILD_VERSION=1.0.0
VITE_BUILD_TIME=2024-01-01T00:00:00Z
```

### Staging (.env.staging)

```env
# Application
NODE_ENV=staging
VITE_APP_URL=https://staging.thechronicle.com

# Supabase
VITE_SUPABASE_URL=https://your-staging-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_staging_anon_key

# Stripe
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key

# Analytics
VITE_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Error Tracking
VITE_SENTRY_DSN=https://your-staging-sentry-dsn
```

## Vercel Deployment

### Automatic Deployment (Recommended)

1. **Connect Repository**
   ```bash
   # Push your code to GitHub
   git push origin main
   ```

2. **Import Project in Vercel**
   - Go to [Vercel Dashboard](https://vercel.com/dashboard)
   - Click "New Project"
   - Import your GitHub repository
   - Configure build settings:
     - Framework Preset: Vite
     - Build Command: `npm run build`
     - Output Directory: `dist`

3. **Configure Environment Variables**
   - Add all production environment variables in Vercel dashboard
   - Go to Project Settings → Environment Variables

4. **Deploy**
   - Vercel will automatically deploy on every push to main branch
   - Preview deployments for pull requests

### Manual Deployment

```bash
# Install Vercel CLI
npm install -g vercel

# Login to Vercel
vercel login

# Deploy to production
vercel --prod

# Deploy to preview
vercel
```

### Vercel Configuration

Create `vercel.json` in project root:

```json
{
  "framework": "vite",
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "installCommand": "npm ci",
  "devCommand": "npm run dev",
  "rewrites": [
    {
      "source": "/((?!api/).*)",
      "destination": "/index.html"
    }
  ],
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "SAMEORIGIN"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        }
      ]
    },
    {
      "source": "/assets/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=********, immutable"
        }
      ]
    }
  ]
}
```

## Docker Deployment

### Build Docker Image

```bash
# Build production image
docker build -t thechronicle:latest .

# Build with build arguments
docker build \
  --build-arg NODE_ENV=production \
  --build-arg VITE_BUILD_VERSION=1.0.0 \
  --build-arg VITE_BUILD_TIME=$(date -u +%Y-%m-%dT%H:%M:%SZ) \
  -t thechronicle:1.0.0 .
```

### Run Container

```bash
# Run production container
docker run -d \
  --name thechronicle \
  -p 80:80 \
  thechronicle:latest

# Run with environment file
docker run -d \
  --name thechronicle \
  --env-file .env.production \
  -p 80:80 \
  thechronicle:latest
```

### Docker Compose

Use the provided `docker-compose.yml` for local development:

```bash
# Start development environment
docker-compose up -d

# Start production environment
docker-compose --profile production up -d

# Start with monitoring
docker-compose --profile monitoring up -d
```

### Container Registry

Push to container registry:

```bash
# Tag for registry
docker tag thechronicle:latest ghcr.io/thechronicle/app:latest

# Push to GitHub Container Registry
docker push ghcr.io/thechronicle/app:latest

# Push to Docker Hub
docker tag thechronicle:latest thechronicle/app:latest
docker push thechronicle/app:latest
```

## Static Hosting Deployment

### Build for Static Hosting

```bash
# Install dependencies
npm ci

# Build for production
npm run build

# The dist/ folder contains the built application
```

### Netlify

1. **Automatic Deployment**
   - Connect your GitHub repository
   - Set build command: `npm run build`
   - Set publish directory: `dist`

2. **Manual Deployment**
   ```bash
   # Install Netlify CLI
   npm install -g netlify-cli

   # Login
   netlify login

   # Deploy
   netlify deploy --prod --dir=dist
   ```

3. **Netlify Configuration**
   
   Create `netlify.toml`:
   ```toml
   [build]
     command = "npm run build"
     publish = "dist"

   [[redirects]]
     from = "/*"
     to = "/index.html"
     status = 200

   [[headers]]
     for = "/*"
     [headers.values]
       X-Frame-Options = "SAMEORIGIN"
       X-Content-Type-Options = "nosniff"
       X-XSS-Protection = "1; mode=block"

   [[headers]]
     for = "/assets/*"
     [headers.values]
       Cache-Control = "public, max-age=********, immutable"
   ```

### AWS S3 + CloudFront

1. **Create S3 Bucket**
   ```bash
   aws s3 mb s3://thechronicle-app
   ```

2. **Upload Build Files**
   ```bash
   aws s3 sync dist/ s3://thechronicle-app --delete
   ```

3. **Configure CloudFront**
   - Create CloudFront distribution
   - Set origin to S3 bucket
   - Configure custom error pages for SPA routing

### GitHub Pages

1. **Build and Deploy Action**
   
   Create `.github/workflows/deploy-pages.yml`:
   ```yaml
   name: Deploy to GitHub Pages

   on:
     push:
       branches: [ main ]

   jobs:
     deploy:
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v4
         - uses: actions/setup-node@v4
           with:
             node-version: '18'
             cache: 'npm'
         - run: npm ci
         - run: npm run build
         - uses: peaceiris/actions-gh-pages@v3
           with:
             github_token: ${{ secrets.GITHUB_TOKEN }}
             publish_dir: ./dist
   ```

## Self-Hosted Deployment

### Server Requirements

- **CPU**: 2+ cores
- **RAM**: 4GB+ recommended
- **Storage**: 20GB+ SSD
- **OS**: Ubuntu 20.04+ or similar
- **Node.js**: 18+
- **Nginx**: Latest stable
- **SSL**: Let's Encrypt or custom certificate

### Server Setup

1. **Install Dependencies**
   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y

   # Install Node.js
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs

   # Install Nginx
   sudo apt install nginx -y

   # Install PM2 for process management
   sudo npm install -g pm2
   ```

2. **Deploy Application**
   ```bash
   # Clone repository
   git clone https://github.com/thechronicle/app.git
   cd app

   # Install dependencies
   npm ci

   # Build application
   npm run build

   # Copy build files to web directory
   sudo cp -r dist/* /var/www/html/
   ```

3. **Configure Nginx**
   
   Create `/etc/nginx/sites-available/thechronicle`:
   ```nginx
   server {
       listen 80;
       server_name thechronicle.com www.thechronicle.com;
       root /var/www/html;
       index index.html;

       location / {
           try_files $uri $uri/ /index.html;
       }

       location /assets/ {
           expires 1y;
           add_header Cache-Control "public, immutable";
       }
   }
   ```

   Enable site:
   ```bash
   sudo ln -s /etc/nginx/sites-available/thechronicle /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl reload nginx
   ```

4. **SSL Certificate**
   ```bash
   # Install Certbot
   sudo apt install certbot python3-certbot-nginx -y

   # Get SSL certificate
   sudo certbot --nginx -d thechronicle.com -d www.thechronicle.com
   ```

### Process Management with PM2

If running a Node.js server:

```bash
# Start application with PM2
pm2 start npm --name "thechronicle" -- start

# Save PM2 configuration
pm2 save

# Setup PM2 startup
pm2 startup
```

## Database Migration

### Supabase Setup

1. **Create Supabase Project**
   - Go to [Supabase Dashboard](https://app.supabase.com)
   - Create new project
   - Note the project URL and anon key

2. **Run Migrations**
   ```bash
   # Install Supabase CLI
   npm install -g supabase

   # Login to Supabase
   supabase login

   # Link project
   supabase link --project-ref your-project-ref

   # Run migrations
   supabase db push
   ```

3. **Seed Database**
   ```bash
   # Run seed script
   npm run db:seed
   ```

## Monitoring and Health Checks

### Health Check Endpoint

The application includes a health check endpoint:

```
GET /health
```

Response:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "version": "1.0.0",
  "uptime": 3600
}
```

### Monitoring Setup

1. **Application Monitoring**
   - Configure Sentry for error tracking
   - Set up Google Analytics for user analytics
   - Monitor Core Web Vitals

2. **Infrastructure Monitoring**
   - Use Prometheus + Grafana for metrics
   - Set up log aggregation
   - Configure alerting

### Backup Strategy

1. **Database Backups**
   - Supabase provides automatic backups
   - Configure additional backup retention if needed

2. **File Backups**
   - Backup uploaded media files
   - Store backups in multiple locations

## Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Clear cache and reinstall
   rm -rf node_modules package-lock.json
   npm install

   # Check Node.js version
   node --version  # Should be 18+
   ```

2. **Environment Variables Not Loading**
   - Verify environment file names
   - Check variable names (must start with VITE_)
   - Restart development server

3. **Supabase Connection Issues**
   - Verify project URL and keys
   - Check network connectivity
   - Verify RLS policies

4. **Deployment Failures**
   - Check build logs
   - Verify environment variables
   - Check resource limits

### Performance Optimization

1. **Bundle Size**
   ```bash
   # Analyze bundle size
   npm run build
   npx webpack-bundle-analyzer dist/assets/*.js
   ```

2. **Caching**
   - Configure proper cache headers
   - Use CDN for static assets
   - Implement service worker

3. **Database Optimization**
   - Add database indexes
   - Optimize queries
   - Use connection pooling

## Security Considerations

1. **Environment Variables**
   - Never commit secrets to version control
   - Use secure secret management
   - Rotate keys regularly

2. **HTTPS**
   - Always use HTTPS in production
   - Configure HSTS headers
   - Use secure cookies

3. **Content Security Policy**
   - Configure CSP headers
   - Whitelist trusted domains
   - Monitor CSP violations

4. **Rate Limiting**
   - Implement API rate limiting
   - Use DDoS protection
   - Monitor for abuse

## Support

For deployment support:

- **Documentation**: [https://docs.thechronicle.com](https://docs.thechronicle.com)
- **Discord**: [Join our Discord](https://discord.gg/thechronicle)
- **Email**: <EMAIL>
- **GitHub Issues**: [Report issues](https://github.com/thechronicle/app/issues)
