import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
import {
  Crown,
  CreditCard,
  Calendar,
  Check,
  X,
  Star,
  Zap,
  Shield,
  Download,
  Users,
  Sparkles,
} from 'lucide-react';
import { useAuth } from '../../../../supabase/auth';
import { supabase } from '../../../../supabase/supabase';

interface Subscription {
  id: string;
  plan: 'free' | 'premium' | 'pro';
  status: 'active' | 'cancelled' | 'expired' | 'trialing';
  start_date: string;
  end_date: string | null;
  stripe_subscription_id: string | null;
  stripe_customer_id: string | null;
}

interface PlanFeature {
  name: string;
  included: boolean;
}

interface Plan {
  id: 'free' | 'premium' | 'pro';
  name: string;
  price: number;
  interval: string;
  description: string;
  features: PlanFeature[];
  popular?: boolean;
  color: string;
  icon: React.ReactNode;
}

const plans: Plan[] = [
  {
    id: 'free',
    name: 'Free',
    price: 0,
    interval: 'forever',
    description: 'Perfect for getting started',
    color: 'gray',
    icon: <Users className="h-6 w-6" />,
    features: [
      { name: 'Read free articles', included: true },
      { name: 'Basic commenting', included: true },
      { name: 'Newsletter subscription', included: true },
      { name: 'Premium articles', included: false },
      { name: 'Ad-free experience', included: false },
      { name: 'Early access', included: false },
      { name: 'Member events', included: false },
    ],
  },
  {
    id: 'premium',
    name: 'Premium',
    price: 9.99,
    interval: 'month',
    description: 'Unlock premium content and features',
    color: 'blue',
    icon: <Crown className="h-6 w-6" />,
    popular: true,
    features: [
      { name: 'All free features', included: true },
      { name: 'Premium articles', included: true },
      { name: 'Ad-free experience', included: true },
      { name: 'Priority support', included: true },
      { name: 'Download articles', included: true },
      { name: 'Early access', included: false },
      { name: 'Member events', included: false },
    ],
  },
  {
    id: 'pro',
    name: 'Pro',
    price: 19.99,
    interval: 'month',
    description: 'Everything you need for the ultimate experience',
    color: 'purple',
    icon: <Sparkles className="h-6 w-6" />,
    features: [
      { name: 'All premium features', included: true },
      { name: 'Early access to content', included: true },
      { name: 'Exclusive member events', included: true },
      { name: 'Direct author access', included: true },
      { name: 'Custom reading lists', included: true },
      { name: 'Advanced analytics', included: true },
      { name: 'API access', included: true },
    ],
  },
];

export function Subscription() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpgrading, setIsUpgrading] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      loadSubscription();
    }
  }, [user]);

  const loadSubscription = async () => {
    try {
      setIsLoading(true);
      
      const { data, error } = await supabase
        .from('user_subscriptions')
        .select('*')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      setSubscription(data || null);
    } catch (error) {
      console.error('Error loading subscription:', error);
      toast({
        title: 'Error',
        description: 'Failed to load subscription information',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpgrade = async (planId: string) => {
    try {
      setIsUpgrading(planId);
      
      // In a real app, you would integrate with Stripe here
      // For now, we'll simulate the upgrade
      const plan = plans.find(p => p.id === planId);
      if (!plan) return;

      // Create or update subscription
      const subscriptionData = {
        user_id: user?.id,
        plan: planId,
        status: 'active',
        start_date: new Date().toISOString(),
        end_date: planId === 'free' ? null : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      };

      const { error } = await supabase
        .from('user_subscriptions')
        .upsert(subscriptionData);

      if (error) throw error;

      toast({
        title: 'Success!',
        description: `Successfully upgraded to ${plan.name} plan`,
      });

      loadSubscription();
    } catch (error) {
      console.error('Error upgrading subscription:', error);
      toast({
        title: 'Error',
        description: 'Failed to upgrade subscription',
        variant: 'destructive',
      });
    } finally {
      setIsUpgrading(null);
    }
  };

  const handleCancelSubscription = async () => {
    if (!subscription || !confirm('Are you sure you want to cancel your subscription?')) return;

    try {
      const { error } = await supabase
        .from('user_subscriptions')
        .update({ status: 'cancelled' })
        .eq('id', subscription.id);

      if (error) throw error;

      toast({
        title: 'Subscription Cancelled',
        description: 'Your subscription has been cancelled. You can continue using premium features until the end of your billing period.',
      });

      loadSubscription();
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      toast({
        title: 'Error',
        description: 'Failed to cancel subscription',
        variant: 'destructive',
      });
    }
  };

  const currentPlan = subscription?.plan || 'free';
  const currentPlanData = plans.find(p => p.id === currentPlan);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="h-32 bg-gray-200 rounded mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-96 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <Crown className="h-8 w-8 text-yellow-600" />
            Subscription
          </h1>
          <p className="text-gray-600 mt-1">
            Manage your subscription and billing
          </p>
        </div>
      </div>

      {/* Current Subscription Status */}
      {subscription && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Current Subscription
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className={`p-3 rounded-full bg-${currentPlanData?.color}-100`}>
                  {currentPlanData?.icon}
                </div>
                <div>
                  <h3 className="font-semibold text-lg">{currentPlanData?.name} Plan</h3>
                  <p className="text-gray-600">{currentPlanData?.description}</p>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant={subscription.status === 'active' ? 'default' : 'secondary'}>
                      {subscription.status}
                    </Badge>
                    {subscription.end_date && (
                      <span className="text-sm text-gray-500">
                        {subscription.status === 'cancelled' ? 'Expires' : 'Renews'} on{' '}
                        {new Date(subscription.end_date).toLocaleDateString()}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              
              {subscription.status === 'active' && subscription.plan !== 'free' && (
                <Button variant="outline" onClick={handleCancelSubscription}>
                  Cancel Subscription
                </Button>
              )}
            </div>

            {subscription.end_date && subscription.status === 'active' && (
              <div className="mt-4">
                <div className="flex justify-between text-sm text-gray-600 mb-2">
                  <span>Billing period</span>
                  <span>
                    {Math.ceil((new Date(subscription.end_date).getTime() - Date.now()) / (1000 * 60 * 60 * 24))} days remaining
                  </span>
                </div>
                <Progress 
                  value={
                    ((Date.now() - new Date(subscription.start_date).getTime()) / 
                    (new Date(subscription.end_date).getTime() - new Date(subscription.start_date).getTime())) * 100
                  } 
                  className="h-2" 
                />
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Subscription Plans */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Choose Your Plan</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {plans.map((plan) => (
            <Card 
              key={plan.id} 
              className={`relative ${plan.popular ? 'ring-2 ring-blue-500' : ''} ${
                currentPlan === plan.id ? 'bg-gray-50' : ''
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-blue-500 text-white">Most Popular</Badge>
                </div>
              )}
              
              <CardHeader className="text-center">
                <div className={`mx-auto p-3 rounded-full bg-${plan.color}-100 w-fit mb-4`}>
                  {plan.icon}
                </div>
                <CardTitle className="text-xl">{plan.name}</CardTitle>
                <div className="text-3xl font-bold">
                  ${plan.price}
                  <span className="text-lg font-normal text-gray-600">
                    /{plan.interval}
                  </span>
                </div>
                <p className="text-gray-600">{plan.description}</p>
              </CardHeader>
              
              <CardContent>
                <ul className="space-y-3 mb-6">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-2">
                      {feature.included ? (
                        <Check className="h-4 w-4 text-green-600" />
                      ) : (
                        <X className="h-4 w-4 text-gray-400" />
                      )}
                      <span className={feature.included ? 'text-gray-900' : 'text-gray-400'}>
                        {feature.name}
                      </span>
                    </li>
                  ))}
                </ul>
                
                <Button
                  className="w-full"
                  variant={currentPlan === plan.id ? 'outline' : 'default'}
                  disabled={currentPlan === plan.id || isUpgrading === plan.id}
                  onClick={() => handleUpgrade(plan.id)}
                >
                  {isUpgrading === plan.id ? (
                    'Processing...'
                  ) : currentPlan === plan.id ? (
                    'Current Plan'
                  ) : plan.id === 'free' ? (
                    'Downgrade to Free'
                  ) : (
                    `Upgrade to ${plan.name}`
                  )}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Benefits Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5" />
            Premium Benefits
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="p-3 bg-blue-100 rounded-full w-fit mx-auto mb-3">
                <Shield className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-medium mb-1">Ad-Free Experience</h3>
              <p className="text-sm text-gray-600">Enjoy uninterrupted reading without any advertisements</p>
            </div>
            
            <div className="text-center">
              <div className="p-3 bg-green-100 rounded-full w-fit mx-auto mb-3">
                <Download className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-medium mb-1">Offline Reading</h3>
              <p className="text-sm text-gray-600">Download articles to read offline anytime, anywhere</p>
            </div>
            
            <div className="text-center">
              <div className="p-3 bg-purple-100 rounded-full w-fit mx-auto mb-3">
                <Zap className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="font-medium mb-1">Early Access</h3>
              <p className="text-sm text-gray-600">Get early access to new articles and features</p>
            </div>
            
            <div className="text-center">
              <div className="p-3 bg-yellow-100 rounded-full w-fit mx-auto mb-3">
                <Users className="h-6 w-6 text-yellow-600" />
              </div>
              <h3 className="font-medium mb-1">Exclusive Events</h3>
              <p className="text-sm text-gray-600">Join member-only events and discussions</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* FAQ Section */}
      <Card>
        <CardHeader>
          <CardTitle>Frequently Asked Questions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-1">Can I cancel my subscription anytime?</h4>
              <p className="text-sm text-gray-600">
                Yes, you can cancel your subscription at any time. You'll continue to have access to premium features until the end of your billing period.
              </p>
            </div>
            
            <div>
              <h4 className="font-medium mb-1">What payment methods do you accept?</h4>
              <p className="text-sm text-gray-600">
                We accept all major credit cards, PayPal, and other payment methods through Stripe.
              </p>
            </div>
            
            <div>
              <h4 className="font-medium mb-1">Is there a free trial?</h4>
              <p className="text-sm text-gray-600">
                Yes, new users get a 7-day free trial of our Premium plan. No credit card required.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
