@echo off
REM Thabo Bester - Hostinger Deployment Script (Windows)
REM This script builds the project and prepares it for Hostinger shared hosting

echo.
echo ========================================
echo 🚀 Thabo Bester - Hostinger Deployment
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed. Please install Node.js first.
    pause
    exit /b 1
)

REM Check if npm is installed
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm is not installed. Please install npm first.
    pause
    exit /b 1
)

echo [INFO] Node.js version:
node --version
echo [INFO] npm version:
npm --version
echo.

REM Install dependencies
echo [INFO] Installing dependencies...
call npm install
if errorlevel 1 (
    echo [ERROR] Failed to install dependencies
    pause
    exit /b 1
)
echo [SUCCESS] Dependencies installed successfully
echo.

REM Build the project
echo [INFO] Building project for production...
call npm run build
if errorlevel 1 (
    echo [ERROR] Build failed
    pause
    exit /b 1
)
echo [SUCCESS] Project built successfully
echo.

REM Create deployment directory
set DEPLOY_DIR=hostinger-deployment
echo [INFO] Creating deployment directory: %DEPLOY_DIR%

if exist "%DEPLOY_DIR%" (
    echo [WARNING] Deployment directory exists. Removing old files...
    rmdir /s /q "%DEPLOY_DIR%"
)

mkdir "%DEPLOY_DIR%"

REM Copy built files
echo [INFO] Copying built files...
xcopy "dist\*" "%DEPLOY_DIR%\" /E /I /Y >nul

REM Copy .htaccess file
if exist "public\.htaccess" (
    echo [INFO] Copying .htaccess file...
    copy "public\.htaccess" "%DEPLOY_DIR%\" >nul
) else (
    echo [WARNING] .htaccess file not found. Creating basic one...
    (
        echo RewriteEngine On
        echo RewriteBase /
        echo RewriteRule ^\index\.html$ - [L]
        echo RewriteCond %%{REQUEST_FILENAME} !-f
        echo RewriteCond %%{REQUEST_FILENAME} !-d
        echo RewriteRule . /index.html [L]
    ) > "%DEPLOY_DIR%\.htaccess"
)

REM Create deployment info file
echo [INFO] Creating deployment info...
(
    echo Thabo Bester - Professional Blog ^& Insights
    echo Deployment Information
    echo.
    echo Build Date: %date% %time%
    echo.
    echo Deployment Instructions:
    echo 1. Upload all files from this directory to your Hostinger public_html folder
    echo 2. Ensure .htaccess file is uploaded and has proper permissions ^(644^)
    echo 3. Configure your domain to point to the public_html directory
    echo 4. Test all routes and functionality
    echo.
    echo For detailed instructions, see HOSTINGER_DEPLOYMENT.md
) > "%DEPLOY_DIR%\deployment-info.txt"

REM Create a zip file for easy upload (requires PowerShell)
echo [INFO] Creating deployment zip file...
powershell -command "Compress-Archive -Path '%DEPLOY_DIR%\*' -DestinationPath 'thabo-bester-hostinger.zip' -Force"

REM Display deployment summary
echo.
echo ======================================
echo 🎉 DEPLOYMENT PREPARATION COMPLETE!
echo ======================================
echo.
echo [SUCCESS] Build completed successfully
echo [SUCCESS] Files prepared for Hostinger deployment
echo.
echo 📁 Files created:
echo    • %DEPLOY_DIR%\ (deployment files)
echo    • thabo-bester-hostinger.zip (upload package)
echo.
echo 🚀 Next Steps:
echo    1. Download 'thabo-bester-hostinger.zip'
echo    2. Extract and upload contents to Hostinger public_html
echo    3. Configure domain and SSL
echo    4. Test the website
echo.
echo 📖 For detailed instructions, see HOSTINGER_DEPLOYMENT.md
echo.

REM Check for common issues
echo [INFO] Checking for potential issues...

if not exist "%DEPLOY_DIR%\index.html" (
    echo [ERROR] index.html not found in deployment directory!
)

if not exist "%DEPLOY_DIR%\assets" (
    echo [WARNING] assets directory not found. This might be normal depending on your build configuration.
)

if exist "%DEPLOY_DIR%\.htaccess" (
    echo [SUCCESS] .htaccess file is ready
) else (
    echo [ERROR] .htaccess file is missing!
)

echo.
echo [SUCCESS] Deployment preparation completed successfully! 🎉
echo.
echo Upload the contents of '%DEPLOY_DIR%' or use 'thabo-bester-hostinger.zip' for easy deployment.
echo.
pause
