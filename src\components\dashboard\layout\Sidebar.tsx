import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Link } from "react-router-dom";
import {
  Home,
  LayoutDashboard,
  Calendar,
  Users,
  Settings,
  HelpCircle,
  FolderKanban,
  ShieldCheck,
  FileText,
  ShoppingCart,
  BarChart3,
  ChevronLeft,
  ChevronRight,
  PlusCircle,
  Package,
  MessageSquare,
  Heart,
  TrendingUp,
  CreditCard,
  UserCheck,
  Globe,
  Mail,
} from "lucide-react";

interface NavItem {
  icon: React.ReactNode;
  label: string;
  href?: string;
  isActive?: boolean;
  badge?: string;
  children?: NavItem[];
}

interface SidebarProps {
  items?: NavItem[];
  activeItem?: string;
  onItemClick?: (label: string, href?: string) => void;
  isAdmin?: boolean;
  collapsed?: boolean;
  onToggleCollapse?: () => void;
  isMobile?: boolean;
  onMobileClose?: () => void;
}

const userNavItems: NavItem[] = [
  { icon: <LayoutDashboard size={20} />, label: "Dashboard", href: "/dashboard" },
  { icon: <Heart size={20} />, label: "Favorites", href: "/dashboard/favorites" },
  { icon: <MessageSquare size={20} />, label: "Comments", href: "/dashboard/comments", badge: "3" },
  { icon: <TrendingUp size={20} />, label: "Analytics", href: "/dashboard/analytics" },
  { icon: <CreditCard size={20} />, label: "Subscription", href: "/dashboard/subscription" },
];

const adminNavItems: NavItem[] = [
  { icon: <LayoutDashboard size={20} />, label: "Dashboard", href: "/dashboard" },
  { icon: <ShieldCheck size={20} />, label: "Admin Panel", href: "/dashboard/admin" },
  { icon: <FileText size={20} />, label: "Articles", href: "/dashboard/articles" },
  { icon: <PlusCircle size={20} />, label: "Create Article", href: "/dashboard/articles/create" },
  { icon: <BarChart3 size={20} />, label: "Analytics", href: "/dashboard/analytics" },
  { icon: <Users size={20} />, label: "Users", href: "/dashboard/users" },
];

const defaultBottomItems: NavItem[] = [
  { icon: <Settings size={20} />, label: "Settings", href: "/dashboard/settings" },
  { icon: <HelpCircle size={20} />, label: "Help", href: "/help" },
];

const Sidebar = ({
  items,
  activeItem = "Dashboard",
  onItemClick = () => {},
  isAdmin = false,
  collapsed = false,
  onToggleCollapse = () => {},
  isMobile = false,
  onMobileClose = () => {},
}: SidebarProps) => {
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const navItems = isAdmin ? adminNavItems : userNavItems;

  const toggleExpanded = (label: string) => {
    setExpandedItems(prev =>
      prev.includes(label)
        ? prev.filter(item => item !== label)
        : [...prev, label]
    );
  };

  const renderNavItem = (item: NavItem, isChild = false) => {
    const isActive = activeItem === item.label;
    const isExpanded = expandedItems.includes(item.label);
    const hasChildren = item.children && item.children.length > 0;

    if (collapsed && !isChild) {
      return (
        <TooltipProvider key={item.label}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className={`w-12 h-12 rounded-xl ${
                  isActive
                    ? "bg-blue-50 text-blue-600 hover:bg-blue-100"
                    : "text-gray-700 hover:bg-gray-100"
                }`}
                onClick={() => item.href ? onItemClick(item.label, item.href) : onItemClick(item.label)}
                asChild={!!item.href}
              >
                {item.href ? (
                  <Link to={item.href}>
                    <span className={isActive ? "text-blue-600" : "text-gray-500"}>
                      {item.icon}
                    </span>
                    {item.badge && (
                      <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                        {item.badge}
                      </span>
                    )}
                  </Link>
                ) : (
                  <>
                    <span className={isActive ? "text-blue-600" : "text-gray-500"}>
                      {item.icon}
                    </span>
                    {item.badge && (
                      <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                        {item.badge}
                      </span>
                    )}
                  </>
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">
              <p>{item.label}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }

    return (
      <div key={item.label}>
        <Button
          variant="ghost"
          className={`w-full justify-start gap-3 h-10 rounded-xl text-sm font-medium relative ${
            isActive
              ? "bg-blue-50 text-blue-600 hover:bg-blue-100"
              : "text-gray-700 hover:bg-gray-100"
          } ${isChild ? "ml-6 h-8" : ""}`}
          onClick={() => {
            if (hasChildren && !collapsed) {
              toggleExpanded(item.label);
            } else if (item.href) {
              onItemClick(item.label, item.href);
              if (isMobile) {
                onMobileClose();
              }
            } else {
              onItemClick(item.label);
              if (isMobile) {
                onMobileClose();
              }
            }
          }}
          asChild={!!item.href && !hasChildren}
        >
          {item.href && !hasChildren ? (
            <Link to={item.href}>
              <span className={isActive ? "text-blue-600" : "text-gray-500"}>
                {item.icon}
              </span>
              {item.label}
              {item.badge && (
                <span className="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-0.5">
                  {item.badge}
                </span>
              )}
            </Link>
          ) : (
            <>
              <span className={isActive ? "text-blue-600" : "text-gray-500"}>
                {item.icon}
              </span>
              {item.label}
              {item.badge && (
                <span className="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-0.5">
                  {item.badge}
                </span>
              )}
              {hasChildren && (
                <ChevronRight
                  className={`ml-auto h-4 w-4 transition-transform ${
                    isExpanded ? "rotate-90" : ""
                  }`}
                />
              )}
            </>
          )}
        </Button>

        {hasChildren && isExpanded && !collapsed && (
          <div className="mt-1 space-y-1">
            {item.children?.map(child => renderNavItem(child, true))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`
      ${isMobile ? "w-64" : collapsed ? "w-16" : "w-[280px]"}
      h-full bg-white/80 backdrop-blur-md border-r border-gray-200 flex flex-col transition-all duration-300
      ${isMobile ? "shadow-lg" : ""}
    `}>
      <div className={`${collapsed && !isMobile ? "p-2" : "p-6"} flex items-center justify-between`}>
        {(!collapsed || isMobile) && (
          <div>
            <h2 className="text-xl font-semibold mb-2 text-gray-900">
              {isAdmin ? "Admin" : "Dashboard"}
            </h2>
            <p className="text-sm text-gray-500">
              {isAdmin ? "Admin dashboard" : "Manage your content"}
            </p>
          </div>
        )}
        {!isMobile && (
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggleCollapse}
            className="h-8 w-8 rounded-lg"
          >
            {collapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
          </Button>
        )}
      </div>

      <ScrollArea className={`flex-1 ${collapsed ? "px-2" : "px-4"}`}>
        <div className="space-y-1.5">
          {navItems.map((item) => renderNavItem(item))}
        </div>

        <Separator className="my-4 bg-gray-100" />

        <div className="space-y-1.5">
          {defaultBottomItems.map((item) => renderNavItem(item))}
        </div>
      </ScrollArea>
    </div>
  );
};

export default Sidebar;
