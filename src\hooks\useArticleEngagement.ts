// Fixed Article Engagement Hook
// Uses Supabase functions instead of direct table queries

import { useState, useEffect } from 'react';
import { supabase } from '@/supabase/client';
import { useAuth } from '@/hooks/useAuth';

export interface ArticleEngagement {
  liked: boolean;
  bookmarked: boolean;
  loading: boolean;
}

export function useArticleEngagement(articleId: string) {
  const [engagement, setEngagement] = useState<ArticleEngagement>({
    liked: false,
    bookmarked: false,
    loading: true,
  });

  const { user } = useAuth();

  useEffect(() => {
    if (user && articleId) {
      loadEngagement();
    } else {
      setEngagement({ liked: false, bookmarked: false, loading: false });
    }
  }, [user, articleId]);

  const loadEngagement = async () => {
    if (!user || !articleId) {
      setEngagement({ liked: false, bookmarked: false, loading: false });
      return;
    }

    try {
      // Use the new function instead of direct table query
      const { data, error } = await supabase
        .rpc('get_article_engagement', { p_article_id: articleId });

      if (error) {
        throw error;
      }

      // The function returns an array, get the first result
      const result = data?.[0] || { liked: false, bookmarked: false };

      setEngagement({
        liked: result.liked || false,
        bookmarked: result.bookmarked || false,
        loading: false,
      });
    } catch (error) {
      console.error('Error loading engagement:', error);
      setEngagement({ liked: false, bookmarked: false, loading: false });
    }
  };

  const toggleLike = async () => {
    if (!user || !articleId) return;

    const newLiked = !engagement.liked;
    
    // Optimistic update
    setEngagement(prev => ({ ...prev, liked: newLiked }));

    try {
      const { error } = await supabase
        .rpc('handle_article_engagement', {
          p_article_id: articleId,
          p_liked: newLiked,
          p_bookmarked: null // Don't change bookmark status
        });

      if (error) {
        throw error;
      }

      // Reload to get the actual state
      await loadEngagement();
    } catch (error) {
      console.error('Error toggling like:', error);
      // Revert optimistic update
      setEngagement(prev => ({ ...prev, liked: !newLiked }));
    }
  };

  const toggleBookmark = async () => {
    if (!user || !articleId) return;

    const newBookmarked = !engagement.bookmarked;
    
    // Optimistic update
    setEngagement(prev => ({ ...prev, bookmarked: newBookmarked }));

    try {
      const { error } = await supabase
        .rpc('handle_article_engagement', {
          p_article_id: articleId,
          p_liked: null, // Don't change like status
          p_bookmarked: newBookmarked
        });

      if (error) {
        throw error;
      }

      // Reload to get the actual state
      await loadEngagement();
    } catch (error) {
      console.error('Error toggling bookmark:', error);
      // Revert optimistic update
      setEngagement(prev => ({ ...prev, bookmarked: !newBookmarked }));
    }
  };

  return {
    ...engagement,
    toggleLike,
    toggleBookmark,
    refresh: loadEngagement,
  };
}

export default useArticleEngagement;
