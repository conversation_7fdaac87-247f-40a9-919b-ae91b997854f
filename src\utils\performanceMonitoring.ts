// Performance Monitoring System
// Core Web Vitals and custom metrics tracking

export interface PerformanceMetric {
  name: string;
  value: number;
  unit: 'ms' | 'bytes' | 'count' | 'percentage' | 'score';
  timestamp: string;
  context?: Record<string, any>;
  tags?: string[];
}

export interface WebVital {
  name: 'CLS' | 'FID' | 'FCP' | 'LCP' | 'TTFB' | 'INP';
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  id: string;
  navigationType: string;
}

export interface ResourceTiming {
  name: string;
  duration: number;
  size: number;
  type: 'script' | 'stylesheet' | 'image' | 'font' | 'fetch' | 'other';
  cached: boolean;
}

export interface NavigationTiming {
  domContentLoaded: number;
  loadComplete: number;
  firstPaint: number;
  firstContentfulPaint: number;
  timeToInteractive: number;
  totalBlockingTime: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private webVitals: WebVital[] = [];
  private isEnabled: boolean = true;
  private reportingEndpoint?: string;
  private onMetric?: (metric: PerformanceMetric) => void;
  private observer?: PerformanceObserver;

  constructor(config?: {
    enabled?: boolean;
    reportingEndpoint?: string;
    onMetric?: (metric: PerformanceMetric) => void;
  }) {
    this.isEnabled = config?.enabled ?? true;
    this.reportingEndpoint = config?.reportingEndpoint;
    this.onMetric = config?.onMetric;

    if (this.isEnabled) {
      this.setupPerformanceObserver();
      this.trackWebVitals();
      this.trackNavigationTiming();
      this.trackResourceTiming();
    }
  }

  // Record a custom metric
  recordMetric(
    name: string,
    value: number,
    unit: PerformanceMetric['unit'] = 'ms',
    context?: Record<string, any>,
    tags?: string[]
  ) {
    if (!this.isEnabled) return;

    const metric: PerformanceMetric = {
      name,
      value,
      unit,
      timestamp: new Date().toISOString(),
      context,
      tags,
    };

    this.metrics.push(metric);
    this.processMetric(metric);
  }

  // Measure function execution time
  measureFunction<T>(name: string, fn: () => T, context?: Record<string, any>): T {
    const start = performance.now();
    try {
      const result = fn();
      const duration = performance.now() - start;
      this.recordMetric(`function.${name}`, duration, 'ms', context);
      return result;
    } catch (error) {
      const duration = performance.now() - start;
      this.recordMetric(`function.${name}.error`, duration, 'ms', {
        ...context,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  // Measure async function execution time
  async measureAsyncFunction<T>(
    name: string,
    fn: () => Promise<T>,
    context?: Record<string, any>
  ): Promise<T> {
    const start = performance.now();
    try {
      const result = await fn();
      const duration = performance.now() - start;
      this.recordMetric(`async.${name}`, duration, 'ms', context);
      return result;
    } catch (error) {
      const duration = performance.now() - start;
      this.recordMetric(`async.${name}.error`, duration, 'ms', {
        ...context,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  // Track component render time
  trackComponentRender(componentName: string, renderTime: number, props?: any) {
    this.recordMetric(
      `component.render.${componentName}`,
      renderTime,
      'ms',
      {
        componentName,
        propsCount: props ? Object.keys(props).length : 0,
      },
      ['component', 'render']
    );
  }

  // Track API call performance
  trackApiCall(
    endpoint: string,
    method: string,
    duration: number,
    status: number,
    size?: number
  ) {
    this.recordMetric(
      `api.${method.toLowerCase()}.${endpoint.replace(/[^a-zA-Z0-9]/g, '_')}`,
      duration,
      'ms',
      {
        endpoint,
        method,
        status,
        size,
        success: status >= 200 && status < 300,
      },
      ['api', method.toLowerCase(), status >= 200 && status < 300 ? 'success' : 'error']
    );
  }

  // Track user interactions
  trackUserInteraction(action: string, element: string, duration?: number) {
    this.recordMetric(
      `interaction.${action}`,
      duration || 0,
      'ms',
      {
        action,
        element,
        timestamp: Date.now(),
      },
      ['interaction', action]
    );
  }

  // Track memory usage
  trackMemoryUsage() {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.recordMetric('memory.used', memory.usedJSHeapSize, 'bytes', {
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit,
      }, ['memory']);
    }
  }

  // Track bundle size
  trackBundleSize() {
    if ('getEntriesByType' in performance) {
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      const scripts = resources.filter(r => r.name.includes('.js'));
      const styles = resources.filter(r => r.name.includes('.css'));

      const totalScriptSize = scripts.reduce((sum, script) => sum + (script.transferSize || 0), 0);
      const totalStyleSize = styles.reduce((sum, style) => sum + (style.transferSize || 0), 0);

      this.recordMetric('bundle.scripts', totalScriptSize, 'bytes', {
        count: scripts.length,
      }, ['bundle', 'scripts']);

      this.recordMetric('bundle.styles', totalStyleSize, 'bytes', {
        count: styles.length,
      }, ['bundle', 'styles']);
    }
  }

  // Get performance summary
  getPerformanceSummary() {
    const now = Date.now();
    const last24Hours = new Date(now - 24 * 60 * 60 * 1000).toISOString();
    const recentMetrics = this.metrics.filter(m => m.timestamp > last24Hours);

    return {
      totalMetrics: this.metrics.length,
      recentMetrics: recentMetrics.length,
      webVitals: this.getWebVitalsScore(),
      averageApiResponseTime: this.getAverageMetric(recentMetrics, 'api'),
      averageComponentRenderTime: this.getAverageMetric(recentMetrics, 'component.render'),
      memoryUsage: this.getLatestMetric('memory.used'),
      bundleSize: {
        scripts: this.getLatestMetric('bundle.scripts'),
        styles: this.getLatestMetric('bundle.styles'),
      },
      topSlowMetrics: this.getTopSlowMetrics(recentMetrics),
    };
  }

  // Get Web Vitals score
  getWebVitalsScore() {
    const vitals = this.webVitals.reduce((acc, vital) => {
      acc[vital.name] = {
        value: vital.value,
        rating: vital.rating,
      };
      return acc;
    }, {} as Record<string, { value: number; rating: string }>);

    const scores = Object.values(vitals).map(v => v.rating === 'good' ? 100 : v.rating === 'needs-improvement' ? 50 : 0);
    const averageScore = scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0;

    return {
      vitals,
      score: Math.round(averageScore),
      rating: averageScore >= 80 ? 'good' : averageScore >= 50 ? 'needs-improvement' : 'poor',
    };
  }

  // Get all metrics with filters
  getMetrics(filters?: {
    name?: string;
    tags?: string[];
    since?: string;
    limit?: number;
  }) {
    let metrics = [...this.metrics];

    if (filters?.name) {
      metrics = metrics.filter(m => m.name.includes(filters.name!));
    }

    if (filters?.tags) {
      metrics = metrics.filter(m => 
        filters.tags!.some(tag => m.tags?.includes(tag))
      );
    }

    if (filters?.since) {
      metrics = metrics.filter(m => m.timestamp > filters.since!);
    }

    metrics.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    if (filters?.limit) {
      metrics = metrics.slice(0, filters.limit);
    }

    return metrics;
  }

  // Clear metrics
  clearMetrics() {
    this.metrics = [];
    this.webVitals = [];
  }

  // Private methods
  private setupPerformanceObserver() {
    if ('PerformanceObserver' in window) {
      this.observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.processPerformanceEntry(entry);
        }
      });

      try {
        this.observer.observe({ entryTypes: ['navigation', 'paint', 'largest-contentful-paint', 'first-input', 'layout-shift'] });
      } catch (error) {
        console.warn('Performance observer not fully supported:', error);
      }
    }
  }

  private processPerformanceEntry(entry: PerformanceEntry) {
    switch (entry.entryType) {
      case 'navigation':
        this.processNavigationEntry(entry as PerformanceNavigationTiming);
        break;
      case 'paint':
        this.processPaintEntry(entry as PerformancePaintTiming);
        break;
      case 'largest-contentful-paint':
        this.processLCPEntry(entry);
        break;
      case 'first-input':
        this.processFIDEntry(entry);
        break;
      case 'layout-shift':
        this.processCLSEntry(entry);
        break;
    }
  }

  private processNavigationEntry(entry: PerformanceNavigationTiming) {
    const timing: NavigationTiming = {
      domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
      loadComplete: entry.loadEventEnd - entry.loadEventStart,
      firstPaint: 0, // Will be set by paint entries
      firstContentfulPaint: 0, // Will be set by paint entries
      timeToInteractive: 0, // Calculated separately
      totalBlockingTime: 0, // Calculated separately
    };

    Object.entries(timing).forEach(([name, value]) => {
      if (value > 0) {
        this.recordMetric(`navigation.${name}`, value, 'ms', {}, ['navigation']);
      }
    });
  }

  private processPaintEntry(entry: PerformancePaintTiming) {
    this.recordMetric(
      `paint.${entry.name.replace(/-/g, '_')}`,
      entry.startTime,
      'ms',
      {},
      ['paint']
    );
  }

  private processLCPEntry(entry: any) {
    const vital: WebVital = {
      name: 'LCP',
      value: entry.startTime,
      rating: entry.startTime <= 2500 ? 'good' : entry.startTime <= 4000 ? 'needs-improvement' : 'poor',
      delta: entry.startTime,
      id: entry.id || 'unknown',
      navigationType: 'navigate',
    };

    this.webVitals.push(vital);
    this.recordMetric('webvital.lcp', vital.value, 'ms', { rating: vital.rating }, ['webvital', 'lcp']);
  }

  private processFIDEntry(entry: any) {
    const vital: WebVital = {
      name: 'FID',
      value: entry.processingStart - entry.startTime,
      rating: entry.processingStart - entry.startTime <= 100 ? 'good' : entry.processingStart - entry.startTime <= 300 ? 'needs-improvement' : 'poor',
      delta: entry.processingStart - entry.startTime,
      id: entry.id || 'unknown',
      navigationType: 'navigate',
    };

    this.webVitals.push(vital);
    this.recordMetric('webvital.fid', vital.value, 'ms', { rating: vital.rating }, ['webvital', 'fid']);
  }

  private processCLSEntry(entry: any) {
    if (!entry.hadRecentInput) {
      const vital: WebVital = {
        name: 'CLS',
        value: entry.value,
        rating: entry.value <= 0.1 ? 'good' : entry.value <= 0.25 ? 'needs-improvement' : 'poor',
        delta: entry.value,
        id: entry.id || 'unknown',
        navigationType: 'navigate',
      };

      this.webVitals.push(vital);
      this.recordMetric('webvital.cls', vital.value, 'score', { rating: vital.rating }, ['webvital', 'cls']);
    }
  }

  private trackWebVitals() {
    // TTFB (Time to First Byte)
    if ('getEntriesByType' in performance) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        const ttfb = navigation.responseStart - navigation.requestStart;
        const vital: WebVital = {
          name: 'TTFB',
          value: ttfb,
          rating: ttfb <= 800 ? 'good' : ttfb <= 1800 ? 'needs-improvement' : 'poor',
          delta: ttfb,
          id: 'navigation',
          navigationType: 'navigate',
        };

        this.webVitals.push(vital);
        this.recordMetric('webvital.ttfb', vital.value, 'ms', { rating: vital.rating }, ['webvital', 'ttfb']);
      }
    }
  }

  private trackNavigationTiming() {
    window.addEventListener('load', () => {
      setTimeout(() => {
        if ('getEntriesByType' in performance) {
          const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
          if (navigation) {
            this.recordMetric('navigation.total', navigation.loadEventEnd - navigation.navigationStart, 'ms');
            this.recordMetric('navigation.dns', navigation.domainLookupEnd - navigation.domainLookupStart, 'ms');
            this.recordMetric('navigation.tcp', navigation.connectEnd - navigation.connectStart, 'ms');
            this.recordMetric('navigation.request', navigation.responseEnd - navigation.requestStart, 'ms');
            this.recordMetric('navigation.response', navigation.responseEnd - navigation.responseStart, 'ms');
            this.recordMetric('navigation.dom', navigation.domComplete - navigation.domLoading, 'ms');
          }
        }
      }, 0);
    });
  }

  private trackResourceTiming() {
    if ('PerformanceObserver' in window) {
      const resourceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          const resource = entry as PerformanceResourceTiming;
          this.recordMetric(
            `resource.${this.getResourceType(resource.name)}`,
            resource.duration,
            'ms',
            {
              name: resource.name,
              size: resource.transferSize,
              cached: resource.transferSize === 0,
            },
            ['resource']
          );
        }
      });

      try {
        resourceObserver.observe({ entryTypes: ['resource'] });
      } catch (error) {
        console.warn('Resource observer not supported:', error);
      }
    }
  }

  private getResourceType(name: string): string {
    if (name.includes('.js')) return 'script';
    if (name.includes('.css')) return 'stylesheet';
    if (name.match(/\.(jpg|jpeg|png|gif|webp|svg)$/)) return 'image';
    if (name.match(/\.(woff|woff2|ttf|otf)$/)) return 'font';
    if (name.includes('/api/') || name.includes('fetch')) return 'fetch';
    return 'other';
  }

  private processMetric(metric: PerformanceMetric) {
    // Call metric callback
    if (this.onMetric) {
      this.onMetric(metric);
    }

    // Report to external service
    if (this.reportingEndpoint) {
      this.reportMetricToService(metric);
    }

    // Log to console in development
    if (import.meta.env.DEV && metric.value > 1000) {
      console.warn('Slow performance detected:', metric);
    }
  }

  private async reportMetricToService(metric: PerformanceMetric) {
    try {
      await fetch(this.reportingEndpoint!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(metric),
      });
    } catch (error) {
      console.error('Failed to report metric to service:', error);
    }
  }

  private getAverageMetric(metrics: PerformanceMetric[], namePrefix: string): number {
    const filteredMetrics = metrics.filter(m => m.name.startsWith(namePrefix));
    if (filteredMetrics.length === 0) return 0;
    return filteredMetrics.reduce((sum, m) => sum + m.value, 0) / filteredMetrics.length;
  }

  private getLatestMetric(name: string): PerformanceMetric | null {
    const filtered = this.metrics.filter(m => m.name === name);
    return filtered.length > 0 ? filtered[filtered.length - 1] : null;
  }

  private getTopSlowMetrics(metrics: PerformanceMetric[], limit: number = 10): PerformanceMetric[] {
    return metrics
      .filter(m => m.unit === 'ms')
      .sort((a, b) => b.value - a.value)
      .slice(0, limit);
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor({
  enabled: true,
  reportingEndpoint: import.meta.env.VITE_PERFORMANCE_REPORTING_ENDPOINT,
});

// React performance tracking utilities
export const withPerformanceTracking = <P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
) => {
  const WrappedComponent = (props: P) => {
    const name = componentName || Component.displayName || Component.name || 'Unknown';
    const startTime = performance.now();

    React.useEffect(() => {
      const endTime = performance.now();
      performanceMonitor.trackComponentRender(name, endTime - startTime, props);
    });

    return React.createElement(Component, props);
  };

  WrappedComponent.displayName = `withPerformanceTracking(${componentName || Component.displayName || Component.name})`;
  return WrappedComponent;
};

export default performanceMonitor;
