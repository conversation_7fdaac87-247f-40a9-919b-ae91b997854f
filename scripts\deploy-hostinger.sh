#!/bin/bash

# Thabo Bester - Hostinger Deployment Script
# This script builds the project and prepares it for Hostinger shared hosting

echo "🚀 Starting Hostinger deployment preparation..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

print_status "Node.js version: $(node --version)"
print_status "npm version: $(npm --version)"

# Install dependencies
print_status "Installing dependencies..."
if npm install; then
    print_success "Dependencies installed successfully"
else
    print_error "Failed to install dependencies"
    exit 1
fi

# Build the project
print_status "Building project for production..."
if npm run build; then
    print_success "Project built successfully"
else
    print_error "Build failed"
    exit 1
fi

# Create deployment directory
DEPLOY_DIR="hostinger-deployment"
print_status "Creating deployment directory: $DEPLOY_DIR"

if [ -d "$DEPLOY_DIR" ]; then
    print_warning "Deployment directory exists. Removing old files..."
    rm -rf "$DEPLOY_DIR"
fi

mkdir -p "$DEPLOY_DIR"

# Copy built files
print_status "Copying built files..."
cp -r dist/* "$DEPLOY_DIR/"

# Copy .htaccess file
if [ -f "public/.htaccess" ]; then
    print_status "Copying .htaccess file..."
    cp "public/.htaccess" "$DEPLOY_DIR/"
else
    print_warning ".htaccess file not found. Creating basic one..."
    cat > "$DEPLOY_DIR/.htaccess" << 'EOF'
RewriteEngine On
RewriteBase /
RewriteRule ^index\.html$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]
EOF
fi

# Create deployment info file
print_status "Creating deployment info..."
cat > "$DEPLOY_DIR/deployment-info.txt" << EOF
Thabo Bester - Professional Blog & Insights
Deployment Information

Build Date: $(date)
Node.js Version: $(node --version)
npm Version: $(npm --version)

Deployment Instructions:
1. Upload all files from this directory to your Hostinger public_html folder
2. Ensure .htaccess file is uploaded and has proper permissions (644)
3. Configure your domain to point to the public_html directory
4. Test all routes and functionality

For detailed instructions, see HOSTINGER_DEPLOYMENT.md
EOF

# Create a zip file for easy upload
print_status "Creating deployment zip file..."
cd "$DEPLOY_DIR"
zip -r "../thabo-bester-hostinger.zip" .
cd ..

# Calculate file sizes
DIST_SIZE=$(du -sh dist | cut -f1)
DEPLOY_SIZE=$(du -sh "$DEPLOY_DIR" | cut -f1)
ZIP_SIZE=$(du -sh "thabo-bester-hostinger.zip" | cut -f1)

# Display deployment summary
echo ""
echo "======================================"
echo "🎉 DEPLOYMENT PREPARATION COMPLETE!"
echo "======================================"
echo ""
print_success "Build completed successfully"
print_success "Files prepared for Hostinger deployment"
echo ""
echo "📊 File Sizes:"
echo "   • Original dist: $DIST_SIZE"
echo "   • Deployment folder: $DEPLOY_SIZE"
echo "   • Zip file: $ZIP_SIZE"
echo ""
echo "📁 Files created:"
echo "   • $DEPLOY_DIR/ (deployment files)"
echo "   • thabo-bester-hostinger.zip (upload package)"
echo ""
echo "🚀 Next Steps:"
echo "   1. Download 'thabo-bester-hostinger.zip'"
echo "   2. Extract and upload contents to Hostinger public_html"
echo "   3. Configure domain and SSL"
echo "   4. Test the website"
echo ""
echo "📖 For detailed instructions, see HOSTINGER_DEPLOYMENT.md"
echo ""

# Check for common issues
print_status "Checking for potential issues..."

# Check if index.html exists
if [ ! -f "$DEPLOY_DIR/index.html" ]; then
    print_error "index.html not found in deployment directory!"
fi

# Check if assets directory exists
if [ ! -d "$DEPLOY_DIR/assets" ]; then
    print_warning "assets directory not found. This might be normal depending on your build configuration."
fi

# Check .htaccess permissions
if [ -f "$DEPLOY_DIR/.htaccess" ]; then
    print_success ".htaccess file is ready"
else
    print_error ".htaccess file is missing!"
fi

echo ""
print_success "Deployment preparation completed successfully! 🎉"
echo ""
echo "Upload the contents of '$DEPLOY_DIR' or use 'thabo-bester-hostinger.zip' for easy deployment."
