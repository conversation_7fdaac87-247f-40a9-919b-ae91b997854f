// Test utilities for React Testing Library
import React, { ReactElement } from 'react';
import { render, RenderOptions, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from '../../../supabase/auth';
import { CartProvider } from '../../contexts/CartContext';

// Custom render function with providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialEntries?: string[];
  user?: any;
  queryClient?: QueryClient;
}

const AllTheProviders = ({ 
  children, 
  initialEntries = ['/'],
  queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
    },
  }),
}: {
  children: React.ReactNode;
  initialEntries?: string[];
  queryClient?: QueryClient;
}) => {
  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <AuthProvider>
          <CartProvider>
            {children}
          </CartProvider>
        </AuthProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

const customRender = (
  ui: ReactElement,
  options: CustomRenderOptions = {}
) => {
  const { initialEntries, queryClient, ...renderOptions } = options;
  
  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <AllTheProviders initialEntries={initialEntries} queryClient={queryClient}>
      {children}
    </AllTheProviders>
  );

  return {
    user: userEvent.setup(),
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
  };
};

// Test data factories
export const createMockArticle = (overrides = {}) => ({
  id: '1',
  title: 'Test Article',
  slug: 'test-article',
  excerpt: 'Test excerpt',
  content: 'Test content',
  author: {
    id: '1',
    name: 'Test Author',
    avatar: 'https://example.com/avatar.jpg',
  },
  category: {
    id: '1',
    name: 'Technology',
    slug: 'technology',
  },
  tags: ['test'],
  featuredImage: 'https://example.com/image.jpg',
  isPremium: false,
  isPublished: true,
  publishedAt: new Date('2024-01-01'),
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  readTime: 5,
  views: 100,
  likes: 10,
  comments: 5,
  shares: 2,
  ...overrides,
});

export const createMockProduct = (overrides = {}) => ({
  id: '1',
  name: 'Test Product',
  slug: 'test-product',
  description: 'Test description',
  shortDescription: 'Short description',
  price: 29.99,
  type: 'digital',
  category: {
    id: '1',
    name: 'Digital',
    slug: 'digital',
  },
  images: ['https://example.com/product.jpg'],
  stock: 100,
  status: 'active',
  featured: true,
  tags: ['test'],
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  ...overrides,
});

export const createMockUser = (overrides = {}) => ({
  id: 'test-user-id',
  email: '<EMAIL>',
  user_metadata: {
    firstName: 'Test',
    lastName: 'User',
  },
  created_at: new Date('2024-01-01').toISOString(),
  updated_at: new Date('2024-01-01').toISOString(),
  ...overrides,
});

// Custom queries and assertions
export const queries = {
  // Find elements by test id
  getByTestId: (testId: string) => screen.getByTestId(testId),
  queryByTestId: (testId: string) => screen.queryByTestId(testId),
  findByTestId: (testId: string) => screen.findByTestId(testId),

  // Find elements by role with name
  getByRoleAndName: (role: string, name: string) => 
    screen.getByRole(role, { name }),
  queryByRoleAndName: (role: string, name: string) => 
    screen.queryByRole(role, { name }),
  findByRoleAndName: (role: string, name: string) => 
    screen.findByRole(role, { name }),

  // Find form elements
  getFormField: (label: string) => screen.getByLabelText(label),
  getSubmitButton: () => screen.getByRole('button', { name: /submit|save|create|update/i }),
  getCancelButton: () => screen.getByRole('button', { name: /cancel|close/i }),

  // Find navigation elements
  getNavLink: (name: string) => screen.getByRole('link', { name }),
  getNavButton: (name: string) => screen.getByRole('button', { name }),

  // Find content elements
  getHeading: (name: string) => screen.getByRole('heading', { name }),
  getArticle: () => screen.getByRole('article'),
  getMain: () => screen.getByRole('main'),
};

// Custom assertions
export const assertions = {
  // Check if element is visible
  toBeVisible: (element: HTMLElement) => {
    expect(element).toBeInTheDocument();
    expect(element).toBeVisible();
  },

  // Check if form is valid
  toBeValidForm: async (form: HTMLElement) => {
    expect(form).toBeInTheDocument();
    expect(form.checkValidity()).toBe(true);
  },

  // Check if loading state
  toBeLoading: () => {
    expect(screen.getByText(/loading/i)).toBeInTheDocument();
  },

  // Check if error state
  toHaveError: (message?: string) => {
    const errorElement = screen.getByRole('alert');
    expect(errorElement).toBeInTheDocument();
    if (message) {
      expect(errorElement).toHaveTextContent(message);
    }
  },

  // Check if success state
  toHaveSuccess: (message?: string) => {
    const successElement = screen.getByText(/success/i);
    expect(successElement).toBeInTheDocument();
    if (message) {
      expect(successElement).toHaveTextContent(message);
    }
  },
};

// User interaction helpers
export const interactions = {
  // Fill form field
  fillField: async (user: any, label: string, value: string) => {
    const field = queries.getFormField(label);
    await user.clear(field);
    await user.type(field, value);
  },

  // Submit form
  submitForm: async (user: any) => {
    const submitButton = queries.getSubmitButton();
    await user.click(submitButton);
  },

  // Navigate to page
  navigateTo: async (user: any, linkName: string) => {
    const link = queries.getNavLink(linkName);
    await user.click(link);
  },

  // Upload file
  uploadFile: async (user: any, inputLabel: string, file: File) => {
    const input = queries.getFormField(inputLabel);
    await user.upload(input, file);
  },

  // Select option
  selectOption: async (user: any, selectLabel: string, optionText: string) => {
    const select = queries.getFormField(selectLabel);
    await user.selectOptions(select, optionText);
  },

  // Toggle checkbox
  toggleCheckbox: async (user: any, checkboxLabel: string) => {
    const checkbox = queries.getFormField(checkboxLabel);
    await user.click(checkbox);
  },
};

// Wait utilities
export const waitUtils = {
  // Wait for element to appear
  waitForElement: (selector: string) => 
    waitFor(() => expect(document.querySelector(selector)).toBeInTheDocument()),

  // Wait for element to disappear
  waitForElementToDisappear: (selector: string) => 
    waitFor(() => expect(document.querySelector(selector)).not.toBeInTheDocument()),

  // Wait for loading to finish
  waitForLoadingToFinish: () => 
    waitFor(() => expect(screen.queryByText(/loading/i)).not.toBeInTheDocument()),

  // Wait for API call
  waitForApiCall: (timeout = 5000) => 
    waitFor(() => {}, { timeout }),

  // Wait for navigation
  waitForNavigation: (expectedPath: string) => 
    waitFor(() => expect(window.location.pathname).toBe(expectedPath)),
};

// Mock helpers
export const mockHelpers = {
  // Mock successful API response
  mockApiSuccess: (data: any) => ({
    data,
    error: null,
    status: 200,
    statusText: 'OK',
  }),

  // Mock API error
  mockApiError: (message: string, status = 400) => ({
    data: null,
    error: { message },
    status,
    statusText: 'Error',
  }),

  // Mock file
  createMockFile: (name = 'test.jpg', size = 1024, type = 'image/jpeg') => 
    new File([''], name, { type, lastModified: Date.now() }),

  // Mock event
  createMockEvent: (type: string, properties = {}) => 
    new Event(type, properties),

  // Mock intersection observer entry
  createMockIntersectionEntry: (isIntersecting = true) => ({
    isIntersecting,
    intersectionRatio: isIntersecting ? 1 : 0,
    target: document.createElement('div'),
    boundingClientRect: {} as DOMRectReadOnly,
    intersectionRect: {} as DOMRectReadOnly,
    rootBounds: {} as DOMRectReadOnly,
    time: Date.now(),
  }),
};

// Performance testing utilities
export const performanceUtils = {
  // Measure render time
  measureRenderTime: async (renderFn: () => void) => {
    const start = performance.now();
    renderFn();
    await waitUtils.waitForLoadingToFinish();
    const end = performance.now();
    return end - start;
  },

  // Check for memory leaks
  checkMemoryLeaks: () => {
    // Simple memory leak detection
    const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;
    return {
      getMemoryUsage: () => {
        const currentMemory = (performance as any).memory?.usedJSHeapSize || 0;
        return currentMemory - initialMemory;
      },
    };
  },
};

// Re-export everything from testing library
export * from '@testing-library/react';
export { default as userEvent } from '@testing-library/user-event';

// Export custom render as default
export { customRender as render };

export default {
  render: customRender,
  queries,
  assertions,
  interactions,
  waitUtils,
  mockHelpers,
  performanceUtils,
  createMockArticle,
  createMockProduct,
  createMockUser,
};
