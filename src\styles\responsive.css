/* Comprehensive Responsive Styles for The Chronicle */
/* Mobile-first responsive design with optimized breakpoints */

/* Base Mobile Styles (320px+) */
@media (min-width: 320px) {
  .container {
    @apply px-4 mx-auto;
    max-width: 100%;
  }

  .grid-responsive {
    @apply grid grid-cols-1 gap-4;
  }

  .card-responsive {
    @apply rounded-lg shadow-sm border border-gray-200 bg-white;
  }

  .text-responsive {
    @apply text-sm leading-relaxed;
  }

  .heading-responsive {
    @apply text-lg font-semibold;
  }

  .button-responsive {
    @apply px-3 py-2 text-sm;
  }

  .nav-mobile {
    @apply fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50;
  }

  .nav-mobile-item {
    @apply flex-1 flex flex-col items-center justify-center py-2 text-xs;
  }

  .sidebar-mobile {
    @apply fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform -translate-x-full transition-transform duration-300 ease-in-out;
  }

  .sidebar-mobile.open {
    @apply translate-x-0;
  }

  .overlay-mobile {
    @apply fixed inset-0 bg-black bg-opacity-50 z-40;
  }

  .tabs-mobile {
    @apply overflow-x-auto;
  }

  .tabs-mobile .tabs-list {
    @apply flex min-w-max;
  }

  .tabs-mobile .tabs-trigger {
    @apply whitespace-nowrap px-4 py-2;
  }
}

/* Small Mobile (375px+) */
@media (min-width: 375px) {
  .container {
    @apply px-5;
  }

  .text-responsive {
    @apply text-base;
  }

  .heading-responsive {
    @apply text-xl;
  }

  .button-responsive {
    @apply px-4 py-2 text-base;
  }
}

/* Large Mobile (425px+) */
@media (min-width: 425px) {
  .container {
    @apply px-6;
  }

  .grid-responsive {
    @apply gap-5;
  }

  .heading-responsive {
    @apply text-2xl;
  }
}

/* Tablet Portrait (768px+) */
@media (min-width: 768px) {
  .container {
    @apply px-8;
    max-width: 768px;
  }

  .grid-responsive {
    @apply grid-cols-2 gap-6;
  }

  .nav-mobile {
    @apply hidden;
  }

  .nav-desktop {
    @apply block;
  }

  .sidebar-mobile {
    @apply relative transform-none w-auto shadow-none;
  }

  .tabs-mobile {
    @apply overflow-visible;
  }

  .tabs-mobile .tabs-list {
    @apply grid grid-cols-auto;
  }

  .card-grid-2 {
    @apply grid-cols-2;
  }

  .text-responsive {
    @apply text-base;
  }

  .heading-responsive {
    @apply text-2xl;
  }

  .button-responsive {
    @apply px-5 py-2.5;
  }
}

/* Tablet Landscape (1024px+) */
@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }

  .grid-responsive {
    @apply grid-cols-3 gap-8;
  }

  .card-grid-3 {
    @apply grid-cols-3;
  }

  .card-grid-4 {
    @apply grid-cols-4;
  }

  .text-responsive {
    @apply text-lg;
  }

  .heading-responsive {
    @apply text-3xl;
  }

  .sidebar-desktop {
    @apply w-64 flex-shrink-0;
  }

  .main-content {
    @apply flex-1 min-w-0;
  }
}

/* Desktop (1280px+) */
@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }

  .grid-responsive {
    @apply grid-cols-4 gap-10;
  }

  .heading-responsive {
    @apply text-4xl;
  }

  .max-w-7xl {
    max-width: 80rem;
  }
}

/* Large Desktop (1536px+) */
@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }

  .max-w-7xl {
    max-width: 96rem;
  }
}

/* Component-Specific Responsive Styles */

/* Article Cards */
.article-card {
  @apply card-responsive overflow-hidden transition-all duration-300 hover:shadow-lg;
}

.article-card-image {
  @apply w-full h-48 object-cover;
}

@media (min-width: 768px) {
  .article-card-image {
    @apply h-56;
  }
}

@media (min-width: 1024px) {
  .article-card-image {
    @apply h-64;
  }
}

/* Product Cards */
.product-card {
  @apply card-responsive overflow-hidden transition-all duration-300 hover:shadow-lg;
}

.product-card-image {
  @apply w-full h-40 object-cover;
}

@media (min-width: 768px) {
  .product-card-image {
    @apply h-48;
  }
}

@media (min-width: 1024px) {
  .product-card-image {
    @apply h-56;
  }
}

/* Dashboard Layout */
.dashboard-layout {
  @apply min-h-screen bg-gray-50;
}

.dashboard-sidebar {
  @apply sidebar-mobile lg:sidebar-desktop;
}

.dashboard-main {
  @apply flex-1 lg:main-content;
}

.dashboard-content {
  @apply p-4 lg:p-8;
}

/* Admin Panel */
.admin-panel {
  @apply space-y-6;
}

.admin-tabs {
  @apply tabs-mobile;
}

.admin-tab-content {
  @apply mt-6;
}

/* Forms */
.form-responsive {
  @apply space-y-4;
}

.form-group {
  @apply space-y-2;
}

.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.form-button {
  @apply button-responsive bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200;
}

/* Navigation */
.nav-responsive {
  @apply bg-white shadow-sm border-b border-gray-200;
}

.nav-container {
  @apply container flex items-center justify-between py-4;
}

.nav-logo {
  @apply text-xl font-bold text-gray-900;
}

.nav-menu {
  @apply hidden md:flex space-x-6;
}

.nav-menu-item {
  @apply text-gray-600 hover:text-gray-900 transition-colors duration-200;
}

.nav-mobile-toggle {
  @apply md:hidden p-2 rounded-md hover:bg-gray-100;
}

/* Footer */
.footer-responsive {
  @apply bg-gray-900 text-white py-12;
}

.footer-container {
  @apply container;
}

.footer-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8;
}

.footer-section {
  @apply space-y-4;
}

.footer-title {
  @apply text-lg font-semibold;
}

.footer-link {
  @apply text-gray-300 hover:text-white transition-colors duration-200;
}

/* Utility Classes */
.hide-mobile {
  @apply hidden md:block;
}

.show-mobile {
  @apply block md:hidden;
}

.full-width-mobile {
  @apply w-full md:w-auto;
}

.center-mobile {
  @apply text-center md:text-left;
}

.stack-mobile {
  @apply flex flex-col md:flex-row;
}

.space-mobile {
  @apply space-y-4 md:space-y-0 md:space-x-4;
}

/* Loading States */
.loading-skeleton {
  @apply animate-pulse bg-gray-200 rounded;
}

.loading-text {
  @apply loading-skeleton h-4 w-3/4;
}

.loading-title {
  @apply loading-skeleton h-6 w-1/2;
}

.loading-image {
  @apply loading-skeleton h-48 w-full;
}

/* Animations */
.fade-in {
  @apply opacity-0 animate-fade-in;
}

.slide-up {
  @apply transform translate-y-4 opacity-0 animate-slide-up;
}

.scale-in {
  @apply transform scale-95 opacity-0 animate-scale-in;
}

@keyframes fade-in {
  to {
    opacity: 1;
  }
}

@keyframes slide-up {
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scale-in {
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Print Styles */
@media print {
  .no-print {
    @apply hidden;
  }

  .print-only {
    @apply block;
  }

  .container {
    @apply max-w-none px-0;
  }

  .card-responsive {
    @apply shadow-none border border-gray-300;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .card-responsive {
    @apply border-2 border-black;
  }

  .button-responsive {
    @apply border-2 border-black;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .dark-mode-auto .card-responsive {
    @apply bg-gray-800 border-gray-700 text-white;
  }

  .dark-mode-auto .nav-responsive {
    @apply bg-gray-800 border-gray-700;
  }

  .dark-mode-auto .footer-responsive {
    @apply bg-gray-900;
  }
}
