import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { Link } from 'react-router-dom';
import {
  Heart,
  MessageSquare,
  Bookmark,
  Eye,
  Calendar,
  TrendingUp,
  Bell,
  Settings,
  User,
  Star,
  Clock,
} from 'lucide-react';
import { useAuth } from '../../../../supabase/auth';
import { supabase } from '@/supabase/client';
import { toast } from '@/hooks/use-toast';

interface UserStats {
  favoriteArticles: number;
  totalComments: number;
  bookmarkedArticles: number;
  followedArticles: number;
}

export function UserDashboard() {
  const { user } = useAuth();
  const [stats, setStats] = useState<UserStats>({
    favoriteArticles: 0,
    totalComments: 0,
    bookmarkedArticles: 0,
    followedArticles: 0,
  });
  const [recentActivity, setRecentActivity] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    if (!user) return;

    try {
      setIsLoading(true);

      // Get user's comments
      const { data: comments, error: commentsError } = await supabase
        .from('comments')
        .select('id')
        .eq('user_id', user.id);

      if (commentsError) throw commentsError;

      // Get user's bookmarks
      const { data: bookmarks, error: bookmarksError } = await supabase
        .from('article_engagement')
        .select('id')
        .eq('user_id', user.id)
        .eq('bookmarked', true);

      if (bookmarksError) throw bookmarksError;

      // Get user's likes (favorites)
      const { data: likes, error: likesError } = await supabase
        .from('article_engagement')
        .select('id')
        .eq('user_id', user.id)
        .eq('liked', true);

      if (likesError) throw likesError;

      setStats({
        favoriteArticles: likes?.length || 0,
        totalComments: comments?.length || 0,
        bookmarkedArticles: bookmarks?.length || 0,
        followedArticles: 0, // This would need a separate follows table
      });
    } catch (error) {
      console.error('Error loading user data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load dashboard data',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const quickActions = [
    {
      title: 'Browse Articles',
      description: 'Discover new content',
      icon: TrendingUp,
      href: '/articles',
      color: 'bg-blue-600 hover:bg-blue-700',
    },
    {
      title: 'My Favorites',
      description: 'View saved articles',
      icon: Heart,
      href: '/dashboard/favorites',
      color: 'bg-red-600 hover:bg-red-700',
    },
    {
      title: 'My Comments',
      description: 'Manage your comments',
      icon: MessageSquare,
      href: '/dashboard/comments',
      color: 'bg-green-600 hover:bg-green-700',
    },
    {
      title: 'Settings',
      description: 'Update your profile',
      icon: Settings,
      href: '/dashboard/settings',
      color: 'bg-gray-600 hover:bg-gray-700',
    },
  ];

  const statCards = [
    {
      title: 'Favorite Articles',
      value: stats.favoriteArticles,
      icon: Heart,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
    },
    {
      title: 'My Comments',
      value: stats.totalComments,
      icon: MessageSquare,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Bookmarked',
      value: stats.bookmarkedArticles,
      icon: Bookmark,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Following',
      value: stats.followedArticles,
      icon: Eye,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
  ];

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome back, {user?.email?.split('@')[0]}!
          </h1>
          <p className="text-gray-600 mt-1">
            Here's your reading activity and saved content.
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Avatar className="h-12 w-12">
            <AvatarImage
              src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${user?.email}`}
              alt={user?.email || ''}
            />
            <AvatarFallback>
              {user?.email?.[0].toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <Badge variant="secondary" className="bg-blue-100 text-blue-700">
            Reader
          </Badge>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-full ${stat.bgColor}`}>
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5 text-yellow-500" />
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action, index) => (
              <Link key={index} to={action.href}>
                <Card className="hover:shadow-md transition-all duration-200 cursor-pointer group">
                  <CardContent className="p-4">
                    <div className="flex flex-col items-center text-center space-y-3">
                      <div className={`p-3 rounded-full ${action.color} text-white group-hover:scale-110 transition-transform`}>
                        <action.icon className="h-6 w-6" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{action.title}</h3>
                        <p className="text-sm text-gray-600">{action.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-blue-500" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <Heart className="h-4 w-4 text-red-500" />
              <span className="text-sm text-gray-600">
                You liked <span className="font-medium">"The Future of AI"</span>
              </span>
              <span className="text-xs text-gray-400 ml-auto">2 hours ago</span>
            </div>
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <MessageSquare className="h-4 w-4 text-green-500" />
              <span className="text-sm text-gray-600">
                You commented on <span className="font-medium">"Tech Trends 2024"</span>
              </span>
              <span className="text-xs text-gray-400 ml-auto">1 day ago</span>
            </div>
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <Bookmark className="h-4 w-4 text-blue-500" />
              <span className="text-sm text-gray-600">
                You bookmarked <span className="font-medium">"Sustainable Fashion"</span>
              </span>
              <span className="text-xs text-gray-400 ml-auto">3 days ago</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
