// Analytics System
// Comprehensive user behavior and business metrics tracking

export interface AnalyticsEvent {
  name: string;
  properties?: Record<string, any>;
  timestamp?: string;
  userId?: string;
  sessionId?: string;
  category?: string;
  value?: number;
}

export interface UserProperties {
  userId: string;
  email?: string;
  name?: string;
  subscriptionTier?: 'free' | 'premium' | 'enterprise';
  signupDate?: string;
  lastActiveDate?: string;
  totalSessions?: number;
  totalPageViews?: number;
  customProperties?: Record<string, any>;
}

export interface PageView {
  path: string;
  title: string;
  referrer?: string;
  timestamp: string;
  userId?: string;
  sessionId: string;
  loadTime?: number;
  scrollDepth?: number;
  timeOnPage?: number;
}

export interface ConversionEvent {
  eventName: string;
  funnelStep: string;
  value?: number;
  currency?: string;
  properties?: Record<string, any>;
}

class Analytics {
  private events: AnalyticsEvent[] = [];
  private pageViews: PageView[] = [];
  private userProperties: Map<string, UserProperties> = new Map();
  private sessionId: string;
  private userId?: string;
  private isEnabled: boolean = true;
  private debugMode: boolean = false;
  private providers: AnalyticsProvider[] = [];

  constructor(config?: {
    enabled?: boolean;
    debugMode?: boolean;
    providers?: AnalyticsProvider[];
  }) {
    this.isEnabled = config?.enabled ?? true;
    this.debugMode = config?.debugMode ?? import.meta.env.DEV;
    this.providers = config?.providers || [];
    this.sessionId = this.generateSessionId();

    if (this.isEnabled) {
      this.setupPageTracking();
      this.setupScrollTracking();
      this.setupClickTracking();
      this.setupFormTracking();
    }
  }

  // Set user identity
  identify(userId: string, properties?: Partial<UserProperties>) {
    this.userId = userId;
    
    const userProps: UserProperties = {
      userId,
      lastActiveDate: new Date().toISOString(),
      ...properties,
    };

    this.userProperties.set(userId, userProps);
    
    // Send to providers
    this.providers.forEach(provider => {
      if (provider.identify) {
        provider.identify(userId, userProps);
      }
    });

    this.log('User identified:', userProps);
  }

  // Track custom event
  track(eventName: string, properties?: Record<string, any>, category?: string) {
    if (!this.isEnabled) return;

    const event: AnalyticsEvent = {
      name: eventName,
      properties: {
        ...properties,
        sessionId: this.sessionId,
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
      userId: this.userId,
      sessionId: this.sessionId,
      category,
    };

    this.events.push(event);
    
    // Send to providers
    this.providers.forEach(provider => {
      if (provider.track) {
        provider.track(event);
      }
    });

    this.log('Event tracked:', event);
  }

  // Track page view
  trackPageView(path?: string, title?: string, properties?: Record<string, any>) {
    if (!this.isEnabled) return;

    const pageView: PageView = {
      path: path || window.location.pathname,
      title: title || document.title,
      referrer: document.referrer,
      timestamp: new Date().toISOString(),
      userId: this.userId,
      sessionId: this.sessionId,
      loadTime: performance.now(),
    };

    this.pageViews.push(pageView);

    // Track as event
    this.track('page_view', {
      page_path: pageView.path,
      page_title: pageView.title,
      page_referrer: pageView.referrer,
      ...properties,
    }, 'navigation');

    this.log('Page view tracked:', pageView);
  }

  // Track conversion events
  trackConversion(eventName: string, funnelStep: string, value?: number, properties?: Record<string, any>) {
    const conversionEvent: ConversionEvent = {
      eventName,
      funnelStep,
      value,
      currency: 'USD',
      properties,
    };

    this.track('conversion', {
      conversion_event: eventName,
      funnel_step: funnelStep,
      conversion_value: value,
      ...properties,
    }, 'conversion');

    this.log('Conversion tracked:', conversionEvent);
  }

  // Track ecommerce events
  trackPurchase(transactionId: string, items: any[], revenue: number, properties?: Record<string, any>) {
    this.track('purchase', {
      transaction_id: transactionId,
      items,
      revenue,
      currency: 'USD',
      ...properties,
    }, 'ecommerce');
  }

  trackAddToCart(item: any, properties?: Record<string, any>) {
    this.track('add_to_cart', {
      item_id: item.id,
      item_name: item.name,
      item_category: item.category,
      item_price: item.price,
      ...properties,
    }, 'ecommerce');
  }

  trackRemoveFromCart(item: any, properties?: Record<string, any>) {
    this.track('remove_from_cart', {
      item_id: item.id,
      item_name: item.name,
      item_category: item.category,
      item_price: item.price,
      ...properties,
    }, 'ecommerce');
  }

  trackViewItem(item: any, properties?: Record<string, any>) {
    this.track('view_item', {
      item_id: item.id,
      item_name: item.name,
      item_category: item.category,
      item_price: item.price,
      ...properties,
    }, 'ecommerce');
  }

  // Track user engagement
  trackEngagement(action: string, element: string, value?: number, properties?: Record<string, any>) {
    this.track('engagement', {
      engagement_action: action,
      engagement_element: element,
      engagement_value: value,
      ...properties,
    }, 'engagement');
  }

  trackTimeOnPage(path: string, timeSpent: number) {
    this.track('time_on_page', {
      page_path: path,
      time_spent: timeSpent,
    }, 'engagement');
  }

  trackScrollDepth(path: string, depth: number) {
    this.track('scroll_depth', {
      page_path: path,
      scroll_depth: depth,
    }, 'engagement');
  }

  // Track search
  trackSearch(query: string, results: number, properties?: Record<string, any>) {
    this.track('search', {
      search_query: query,
      search_results: results,
      ...properties,
    }, 'search');
  }

  // Track form interactions
  trackFormStart(formName: string, properties?: Record<string, any>) {
    this.track('form_start', {
      form_name: formName,
      ...properties,
    }, 'form');
  }

  trackFormSubmit(formName: string, success: boolean, properties?: Record<string, any>) {
    this.track('form_submit', {
      form_name: formName,
      form_success: success,
      ...properties,
    }, 'form');
  }

  trackFormError(formName: string, field: string, error: string, properties?: Record<string, any>) {
    this.track('form_error', {
      form_name: formName,
      form_field: field,
      form_error: error,
      ...properties,
    }, 'form');
  }

  // Track authentication events
  trackSignUp(method: string, properties?: Record<string, any>) {
    this.track('sign_up', {
      signup_method: method,
      ...properties,
    }, 'auth');
  }

  trackSignIn(method: string, properties?: Record<string, any>) {
    this.track('sign_in', {
      signin_method: method,
      ...properties,
    }, 'auth');
  }

  trackSignOut(properties?: Record<string, any>) {
    this.track('sign_out', properties, 'auth');
  }

  // Get analytics data
  getEvents(filters?: {
    category?: string;
    userId?: string;
    since?: string;
    limit?: number;
  }) {
    let events = [...this.events];

    if (filters?.category) {
      events = events.filter(e => e.category === filters.category);
    }
    if (filters?.userId) {
      events = events.filter(e => e.userId === filters.userId);
    }
    if (filters?.since) {
      events = events.filter(e => e.timestamp! > filters.since!);
    }

    events.sort((a, b) => new Date(b.timestamp!).getTime() - new Date(a.timestamp!).getTime());

    if (filters?.limit) {
      events = events.slice(0, filters.limit);
    }

    return events;
  }

  getPageViews(filters?: {
    path?: string;
    userId?: string;
    since?: string;
    limit?: number;
  }) {
    let pageViews = [...this.pageViews];

    if (filters?.path) {
      pageViews = pageViews.filter(pv => pv.path === filters.path);
    }
    if (filters?.userId) {
      pageViews = pageViews.filter(pv => pv.userId === filters.userId);
    }
    if (filters?.since) {
      pageViews = pageViews.filter(pv => pv.timestamp > filters.since!);
    }

    pageViews.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    if (filters?.limit) {
      pageViews = pageViews.slice(0, filters.limit);
    }

    return pageViews;
  }

  // Get analytics summary
  getAnalyticsSummary() {
    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString();
    const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();

    const recentEvents = this.events.filter(e => e.timestamp! > last24Hours);
    const recentPageViews = this.pageViews.filter(pv => pv.timestamp > last24Hours);
    const weeklyEvents = this.events.filter(e => e.timestamp! > last7Days);

    return {
      totalEvents: this.events.length,
      totalPageViews: this.pageViews.length,
      totalUsers: this.userProperties.size,
      last24Hours: {
        events: recentEvents.length,
        pageViews: recentPageViews.length,
        uniqueUsers: new Set(recentEvents.map(e => e.userId).filter(Boolean)).size,
      },
      last7Days: {
        events: weeklyEvents.length,
        uniqueUsers: new Set(weeklyEvents.map(e => e.userId).filter(Boolean)).size,
      },
      topEvents: this.getTopEvents(recentEvents),
      topPages: this.getTopPages(recentPageViews),
      conversionFunnel: this.getConversionFunnel(),
    };
  }

  // Clear analytics data
  clearData() {
    this.events = [];
    this.pageViews = [];
    this.userProperties.clear();
  }

  // Add analytics provider
  addProvider(provider: AnalyticsProvider) {
    this.providers.push(provider);
  }

  // Private methods
  private setupPageTracking() {
    // Track initial page view
    this.trackPageView();

    // Track page changes (for SPA)
    let currentPath = window.location.pathname;
    const observer = new MutationObserver(() => {
      if (window.location.pathname !== currentPath) {
        currentPath = window.location.pathname;
        this.trackPageView();
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });
  }

  private setupScrollTracking() {
    let maxScrollDepth = 0;
    let scrollTimeout: NodeJS.Timeout;

    window.addEventListener('scroll', () => {
      const scrollDepth = Math.round(
        (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
      );

      if (scrollDepth > maxScrollDepth) {
        maxScrollDepth = scrollDepth;
      }

      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        if (maxScrollDepth >= 25 && maxScrollDepth % 25 === 0) {
          this.trackScrollDepth(window.location.pathname, maxScrollDepth);
        }
      }, 500);
    });
  }

  private setupClickTracking() {
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      const tagName = target.tagName.toLowerCase();
      
      if (tagName === 'a' || tagName === 'button' || target.getAttribute('role') === 'button') {
        this.trackEngagement('click', tagName, 1, {
          element_text: target.textContent?.trim(),
          element_id: target.id,
          element_class: target.className,
        });
      }
    });
  }

  private setupFormTracking() {
    document.addEventListener('submit', (event) => {
      const form = event.target as HTMLFormElement;
      const formName = form.name || form.id || 'unnamed_form';
      
      this.trackFormSubmit(formName, true, {
        form_action: form.action,
        form_method: form.method,
      });
    });

    document.addEventListener('focusin', (event) => {
      const target = event.target as HTMLElement;
      if (target.tagName.toLowerCase() === 'input' || target.tagName.toLowerCase() === 'textarea') {
        const form = target.closest('form');
        if (form) {
          const formName = form.name || form.id || 'unnamed_form';
          this.trackFormStart(formName);
        }
      }
    });
  }

  private generateSessionId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private getTopEvents(events: AnalyticsEvent[], limit: number = 10) {
    const eventCounts = events.reduce((acc, event) => {
      acc[event.name] = (acc[event.name] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(eventCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, limit)
      .map(([name, count]) => ({ name, count }));
  }

  private getTopPages(pageViews: PageView[], limit: number = 10) {
    const pageCounts = pageViews.reduce((acc, pv) => {
      acc[pv.path] = (acc[pv.path] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(pageCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, limit)
      .map(([path, count]) => ({ path, count }));
  }

  private getConversionFunnel() {
    const conversionEvents = this.events.filter(e => e.category === 'conversion');
    const funnelSteps = conversionEvents.reduce((acc, event) => {
      const step = event.properties?.funnel_step;
      if (step) {
        acc[step] = (acc[step] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(funnelSteps)
      .map(([step, count]) => ({ step, count }))
      .sort((a, b) => b.count - a.count);
  }

  private log(message: string, data?: any) {
    if (this.debugMode) {
      console.log(`[Analytics] ${message}`, data);
    }
  }
}

// Analytics provider interface
export interface AnalyticsProvider {
  name: string;
  identify?: (userId: string, properties: UserProperties) => void;
  track?: (event: AnalyticsEvent) => void;
  pageView?: (pageView: PageView) => void;
}

// Google Analytics 4 provider
export class GoogleAnalyticsProvider implements AnalyticsProvider {
  name = 'Google Analytics';
  
  constructor(private measurementId: string) {
    this.initializeGA4();
  }

  private initializeGA4() {
    // Load GA4 script
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${this.measurementId}`;
    document.head.appendChild(script);

    // Initialize gtag
    (window as any).dataLayer = (window as any).dataLayer || [];
    (window as any).gtag = function() {
      (window as any).dataLayer.push(arguments);
    };
    (window as any).gtag('js', new Date());
    (window as any).gtag('config', this.measurementId);
  }

  identify(userId: string, properties: UserProperties) {
    (window as any).gtag('config', this.measurementId, {
      user_id: userId,
      custom_map: properties.customProperties,
    });
  }

  track(event: AnalyticsEvent) {
    (window as any).gtag('event', event.name, {
      event_category: event.category,
      event_label: event.properties?.label,
      value: event.value,
      ...event.properties,
    });
  }

  pageView(pageView: PageView) {
    (window as any).gtag('config', this.measurementId, {
      page_path: pageView.path,
      page_title: pageView.title,
    });
  }
}

// Global analytics instance
export const analytics = new Analytics({
  enabled: true,
  debugMode: import.meta.env.DEV,
  providers: [
    new GoogleAnalyticsProvider(import.meta.env.VITE_GA_MEASUREMENT_ID || 'G-XXXXXXXXXX'),
  ],
});

export default analytics;
