# Hostinger Shared Hosting Deployment Guide

## Prerequisites

1. **Hostinger Shared Hosting Account** with:
   - PHP 8.0+ support
   - Node.js support (if available)
   - File Manager access
   - Database access (MySQL/PostgreSQL)

2. **Domain Configuration**:
   - Point your domain to Hostinger nameservers
   - Configure DNS settings in Hostinger control panel

## Deployment Steps

### Step 1: Build the Project Locally

```bash
# Install dependencies
npm install

# Build for production
npm run build

# This creates a 'dist' folder with static files
```

### Step 2: Upload Files to Hostinger

1. **Access File Manager** in Hostinger control panel
2. **Navigate to public_html** (or your domain folder)
3. **Upload all files** from the `dist` folder to public_html
4. **Upload additional files**:
   - `.htaccess` (for URL rewriting)
   - `_redirects` (for Netlify-style redirects)

### Step 3: Configure .htaccess

Create `.htaccess` file in public_html:

```apache
# Enable URL Rewriting
RewriteEngine On

# Handle Angular/React/Vue Router
RewriteBase /
RewriteRule ^index\.html$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]

# Security Headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Cache Control
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
```

### Step 4: Environment Variables

Since Hostinger shared hosting doesn't support environment variables like Vercel, you'll need to:

1. **Create a config file** in your project:

```javascript
// src/config/hostinger.js
export const config = {
  VITE_SUPABASE_URL: 'your-supabase-url',
  VITE_SUPABASE_ANON_KEY: 'your-supabase-anon-key',
  VITE_STRIPE_PUBLISHABLE_KEY: 'your-stripe-publishable-key',
  VITE_GOOGLE_ANALYTICS_ID: 'your-ga-id'
};
```

2. **Update your build process** to use this config
3. **Rebuild and redeploy**

### Step 5: Database Configuration

If using a database on Hostinger:

1. **Create MySQL Database** in Hostinger control panel
2. **Update Supabase connection** or migrate to MySQL
3. **Import database schema** using phpMyAdmin

### Step 6: SSL Certificate

1. **Enable SSL** in Hostinger control panel
2. **Force HTTPS** by adding to .htaccess:

```apache
# Force HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

## File Structure on Hostinger

```
public_html/
├── index.html
├── assets/
│   ├── index-[hash].js
│   ├── index-[hash].css
│   └── images/
├── favicon.svg
├── .htaccess
└── _redirects
```

## Performance Optimization for Shared Hosting

### 1. Minimize Bundle Size

```bash
# Analyze bundle size
npm run build -- --analyze

# Use dynamic imports for code splitting
const LazyComponent = lazy(() => import('./Component'));
```

### 2. Optimize Images

- Use WebP format when possible
- Compress images before upload
- Use appropriate image sizes

### 3. Enable Caching

Add to .htaccess:

```apache
# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresDefault "access plus 1 month"
    ExpiresByType text/html "access plus 1 hour"
    ExpiresByType application/json "access plus 1 hour"
</IfModule>
```

## Troubleshooting

### Common Issues:

1. **404 Errors on Refresh**
   - Ensure .htaccess is properly configured
   - Check RewriteEngine is enabled

2. **Assets Not Loading**
   - Verify file paths are correct
   - Check file permissions (755 for directories, 644 for files)

3. **Slow Loading**
   - Enable compression in .htaccess
   - Optimize images and assets
   - Use CDN for static assets

### Testing Deployment:

1. **Test all routes** by navigating directly to URLs
2. **Check browser console** for errors
3. **Verify API calls** are working
4. **Test on mobile devices**

## Maintenance

### Regular Updates:

1. **Monitor performance** using browser dev tools
2. **Update dependencies** regularly
3. **Backup files** before major changes
4. **Monitor error logs** in Hostinger control panel

### Scaling Considerations:

- Consider upgrading to VPS if traffic increases
- Use CDN for better global performance
- Implement proper monitoring and analytics

## Support

For Hostinger-specific issues:
- Contact Hostinger support
- Check Hostinger knowledge base
- Use Hostinger community forums

For application issues:
- Check browser console for errors
- Review application logs
- Test locally first before deploying
