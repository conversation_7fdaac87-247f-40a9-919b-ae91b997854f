import React, { useEffect } from 'react';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'product' | 'profile';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  section?: string;
  tags?: string[];
  price?: {
    amount: number;
    currency: string;
  };
  availability?: 'in_stock' | 'out_of_stock' | 'preorder';
  brand?: string;
  category?: string;
  noIndex?: boolean;
  canonical?: string;
  alternateLanguages?: Array<{
    hrefLang: string;
    href: string;
  }>;
}

const defaultSEO = {
  title: 'The Chronicle - Premium News & Ecommerce Platform',
  description: 'Discover the latest news, insights, and premium products at The Chronicle. Your trusted source for quality journalism and curated shopping experiences.',
  keywords: [
    'news',
    'journalism',
    'ecommerce',
    'premium products',
    'technology',
    'business',
    'lifestyle',
    'shopping',
    'articles',
    'blog',
    'marketplace',
    'digital products',
    'subscription',
    'newsletter'
  ],
  image: '/og-image.jpg',
  url: 'https://thechronicle.com',
  type: 'website' as const,
  author: 'The Chronicle Editorial Team',
};

export function SEO({
  title,
  description,
  keywords = [],
  image,
  url,
  type = 'website',
  author,
  publishedTime,
  modifiedTime,
  section,
  tags = [],
  price,
  availability,
  brand,
  category,
  noIndex = false,
  canonical,
  alternateLanguages = [],
}: SEOProps) {
  const siteTitle = 'The Chronicle';
  const fullTitle = title ? `${title} | ${siteTitle}` : defaultSEO.title;
  const seoDescription = description || defaultSEO.description;
  const seoKeywords = [...defaultSEO.keywords, ...keywords].join(', ');
  const seoImage = image || defaultSEO.image;
  const seoUrl = url || defaultSEO.url;
  const seoAuthor = author || defaultSEO.author;

  // Generate structured data
  const generateStructuredData = () => {
    const baseData = {
      '@context': 'https://schema.org',
      '@type': type === 'website' ? 'WebSite' : type === 'article' ? 'Article' : type === 'product' ? 'Product' : 'WebPage',
      name: fullTitle,
      description: seoDescription,
      url: seoUrl,
      image: seoImage,
    };

    if (type === 'website') {
      return {
        ...baseData,
        '@type': 'WebSite',
        potentialAction: {
          '@type': 'SearchAction',
          target: {
            '@type': 'EntryPoint',
            urlTemplate: `${seoUrl}/search?q={search_term_string}`,
          },
          'query-input': 'required name=search_term_string',
        },
        publisher: {
          '@type': 'Organization',
          name: siteTitle,
          logo: {
            '@type': 'ImageObject',
            url: `${seoUrl}/logo.png`,
          },
        },
      };
    }

    if (type === 'article') {
      return {
        ...baseData,
        '@type': 'Article',
        headline: title,
        author: {
          '@type': 'Person',
          name: seoAuthor,
        },
        publisher: {
          '@type': 'Organization',
          name: siteTitle,
          logo: {
            '@type': 'ImageObject',
            url: `${seoUrl}/logo.png`,
          },
        },
        datePublished: publishedTime,
        dateModified: modifiedTime || publishedTime,
        articleSection: section,
        keywords: tags.join(', '),
        mainEntityOfPage: {
          '@type': 'WebPage',
          '@id': seoUrl,
        },
      };
    }

    if (type === 'product') {
      return {
        ...baseData,
        '@type': 'Product',
        brand: {
          '@type': 'Brand',
          name: brand || siteTitle,
        },
        category: category,
        offers: price ? {
          '@type': 'Offer',
          price: price.amount,
          priceCurrency: price.currency,
          availability: `https://schema.org/${availability === 'in_stock' ? 'InStock' : availability === 'out_of_stock' ? 'OutOfStock' : 'PreOrder'}`,
          seller: {
            '@type': 'Organization',
            name: siteTitle,
          },
        } : undefined,
      };
    }

    return baseData;
  };

  useEffect(() => {
    // Update document title
    document.title = fullTitle;

    // Helper function to update or create meta tags
    const updateMetaTag = (name: string, content: string, isProperty = false) => {
      const selector = isProperty ? `meta[property="${name}"]` : `meta[name="${name}"]`;
      let meta = document.querySelector(selector) as HTMLMetaElement;
      if (!meta) {
        meta = document.createElement('meta');
        if (isProperty) {
          meta.setAttribute('property', name);
        } else {
          meta.name = name;
        }
        document.head.appendChild(meta);
      }
      meta.content = content;
    };

    // Helper function to update or create link tags
    const updateLinkTag = (rel: string, href: string) => {
      let link = document.querySelector(`link[rel="${rel}"]`) as HTMLLinkElement;
      if (!link) {
        link = document.createElement('link');
        link.rel = rel;
        document.head.appendChild(link);
      }
      link.href = href;
    };

    // Basic meta tags
    updateMetaTag('description', seoDescription);
    updateMetaTag('keywords', seoKeywords);
    updateMetaTag('author', seoAuthor);
    updateMetaTag('robots', noIndex ? 'noindex,nofollow' : 'index,follow');
    updateMetaTag('googlebot', noIndex ? 'noindex,nofollow' : 'index,follow');

    // Open Graph tags
    updateMetaTag('og:type', type, true);
    updateMetaTag('og:title', fullTitle, true);
    updateMetaTag('og:description', seoDescription, true);
    updateMetaTag('og:image', seoImage, true);
    updateMetaTag('og:url', seoUrl, true);
    updateMetaTag('og:site_name', siteTitle, true);

    // Twitter Card tags
    updateMetaTag('twitter:card', 'summary_large_image');
    updateMetaTag('twitter:title', fullTitle);
    updateMetaTag('twitter:description', seoDescription);
    updateMetaTag('twitter:image', seoImage);
    updateMetaTag('twitter:site', '@thechronicle');
    updateMetaTag('twitter:creator', '@thechronicle');

    // Article specific tags
    if (type === 'article') {
      updateMetaTag('article:author', seoAuthor, true);
      if (publishedTime) updateMetaTag('article:published_time', publishedTime, true);
      if (modifiedTime) updateMetaTag('article:modified_time', modifiedTime, true);
      if (section) updateMetaTag('article:section', section, true);
    }

    // Product specific tags
    if (type === 'product' && price) {
      updateMetaTag('product:price:amount', price.amount.toString(), true);
      updateMetaTag('product:price:currency', price.currency, true);
      updateMetaTag('product:availability', availability || 'in_stock', true);
      if (brand) updateMetaTag('product:brand', brand, true);
      if (category) updateMetaTag('product:category', category, true);
    }

    // Canonical URL
    updateLinkTag('canonical', canonical || seoUrl);

    // Structured data
    const removeExistingStructuredData = () => {
      const existingScripts = document.querySelectorAll('script[type="application/ld+json"]');
      existingScripts.forEach(script => script.remove());
    };

    const addStructuredData = (data: any) => {
      const script = document.createElement('script');
      script.type = 'application/ld+json';
      script.textContent = JSON.stringify(data);
      document.head.appendChild(script);
    };

    removeExistingStructuredData();
    addStructuredData(generateStructuredData());

    // Organization structured data
    addStructuredData({
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: siteTitle,
      url: seoUrl,
      logo: `${seoUrl}/logo.png`,
      sameAs: [
        'https://twitter.com/thechronicle',
        'https://facebook.com/thechronicle',
        'https://linkedin.com/company/thechronicle',
        'https://instagram.com/thechronicle',
      ],
      contactPoint: {
        '@type': 'ContactPoint',
        telephone: '******-0123',
        contactType: 'customer service',
        availableLanguage: 'English',
      },
    });

    // Breadcrumb structured data for non-homepage
    if (type !== 'website') {
      addStructuredData({
        '@context': 'https://schema.org',
        '@type': 'BreadcrumbList',
        itemListElement: [
          {
            '@type': 'ListItem',
            position: 1,
            name: 'Home',
            item: seoUrl,
          },
          {
            '@type': 'ListItem',
            position: 2,
            name: title || 'Page',
            item: seoUrl,
          },
        ],
      });
    }
  }, [fullTitle, seoDescription, seoKeywords, seoAuthor, seoImage, seoUrl, type, publishedTime, modifiedTime, section, tags, price, availability, brand, category, noIndex, canonical]);

  return null; // This component doesn't render anything visible
}

// Utility function to generate SEO-friendly URLs
export function generateSEOUrl(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim()
    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
}

// Utility function to truncate text for meta descriptions
export function truncateDescription(text: string, maxLength: number = 160): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3).trim() + '...';
}
