import { Suspense } from "react";
import { Navigate, Route, Routes, useRoutes } from "react-router-dom";
import LoginForm from "./components/auth/LoginForm";
import SignUpForm from "./components/auth/SignUpForm";
import { AuthCallback } from "./components/auth/SocialAuth";
import Dashboard from "./components/pages/dashboard";
import Success from "./components/pages/success";
import Home from "./components/pages/home";
import SeedPage from "./components/pages/seed";
import { Articles } from "./components/pages/Articles";
import { ArticleView } from "./components/pages/ArticleView";
import { Products } from "./components/pages/Products";
import { ProductView } from "./components/pages/ProductView";
import { CheckoutForm } from "./components/payment/CheckoutForm";
import { SuccessPage } from "./components/payment/SuccessPage";
import { About } from "./components/pages/About";
import { Contact } from "./components/pages/Contact";
import { Cart } from "./components/pages/Cart";
import TermsOfService from "./components/pages/TermsOfService";
import PrivacyPolicy from "./components/pages/PrivacyPolicy";
import CookiePolicy from "./components/pages/CookiePolicy";
import { AuthProvider, useAuth } from "../supabase/auth";
import { CartProvider } from "./contexts/CartContext";
import { Toaster } from "./components/ui/toaster";
import { LoadingScreen } from "./components/ui/loading-spinner";
import ErrorBoundary from "./components/ErrorBoundary";

// Tempo routes - fallback to empty array if not available
const routes: any = [];

function PrivateRoute({
  children,
  requireAdmin = false,
}: {
  children: React.ReactNode;
  requireAdmin?: boolean;
}) {
  const { user, loading, isAdmin } = useAuth();

  if (loading) {
    return <LoadingScreen text="Authenticating..." />;
  }

  if (!user) {
    return <Navigate to="/login" />;
  }

  if (requireAdmin && !isAdmin) {
    return <Navigate to="/dashboard" />;
  }

  return <>{children}</>;
}

function AppRoutes() {
  return (
    <>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/articles" element={<Articles />} />
        <Route path="/articles/:slug" element={<ArticleView />} />
        <Route path="/products" element={<Products />} />
        <Route path="/products/:slug" element={<ProductView />} />
        <Route path="/checkout" element={<CheckoutForm />} />
        <Route path="/success" element={<SuccessPage />} />
        <Route path="/about" element={<About />} />
        <Route path="/contact" element={<Contact />} />
        <Route path="/cart" element={<Cart />} />
        <Route path="/terms-of-service" element={<TermsOfService />} />
        <Route path="/privacy-policy" element={<PrivacyPolicy />} />
        <Route path="/cookie-policy" element={<CookiePolicy />} />
        <Route path="/login" element={<LoginForm />} />
        <Route path="/signup" element={<SignUpForm />} />
        <Route path="/auth/callback" element={<AuthCallback />} />
        <Route path="/seed" element={<SeedPage />} />
        <Route
          path="/dashboard/*"
          element={
            <PrivateRoute>
              <Dashboard />
            </PrivateRoute>
          }
        />
        <Route path="/success" element={<Success />} />
        <Route path="*" element={<Navigate to="/" />} />
      </Routes>
      {import.meta.env.VITE_TEMPO === "true" && useRoutes(routes)}
    </>
  );
}

function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <CartProvider>
          <Suspense fallback={<LoadingScreen text="Loading application..." />}>
            <AppRoutes />
          </Suspense>
          <Toaster />
        </CartProvider>
      </AuthProvider>
    </ErrorBoundary>
  );
}

export default App;
