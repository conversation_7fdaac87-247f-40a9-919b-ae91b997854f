import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

interface SEOData {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  type?: 'website' | 'article' | 'product';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  noIndex?: boolean;
  canonical?: string;
}

const defaultSEO = {
  title: 'The Chronicle - Premium News & Ecommerce Platform',
  description: 'Discover the latest news, insights, and premium products at The Chronicle. Your trusted source for quality journalism and curated shopping experiences.',
  keywords: ['news', 'journalism', 'ecommerce', 'premium products', 'technology', 'business'],
  image: '/og-image.jpg',
  type: 'website' as const,
};

export function useSEO(seoData: SEOData = {}) {
  const location = useLocation();
  const baseUrl = 'https://thechronicle.com';

  useEffect(() => {
    // Update document title
    const title = seoData.title 
      ? `${seoData.title} | The Chronicle`
      : defaultSEO.title;
    document.title = title;

    // Update meta description
    const description = seoData.description || defaultSEO.description;
    updateMetaTag('description', description);

    // Update meta keywords
    const keywords = [...(defaultSEO.keywords || []), ...(seoData.keywords || [])];
    updateMetaTag('keywords', keywords.join(', '));

    // Update Open Graph tags
    updateMetaProperty('og:title', title);
    updateMetaProperty('og:description', description);
    updateMetaProperty('og:image', seoData.image || defaultSEO.image);
    updateMetaProperty('og:url', `${baseUrl}${location.pathname}`);
    updateMetaProperty('og:type', seoData.type || defaultSEO.type);

    // Update Twitter Card tags
    updateMetaName('twitter:title', title);
    updateMetaName('twitter:description', description);
    updateMetaName('twitter:image', seoData.image || defaultSEO.image);
    updateMetaName('twitter:card', 'summary_large_image');

    // Update canonical URL
    updateCanonicalUrl(seoData.canonical || `${baseUrl}${location.pathname}`);

    // Update robots meta
    updateMetaName('robots', seoData.noIndex ? 'noindex,nofollow' : 'index,follow');

    // Article specific meta tags
    if (seoData.type === 'article') {
      if (seoData.author) {
        updateMetaName('author', seoData.author);
        updateMetaProperty('article:author', seoData.author);
      }
      if (seoData.publishedTime) {
        updateMetaProperty('article:published_time', seoData.publishedTime);
      }
      if (seoData.modifiedTime) {
        updateMetaProperty('article:modified_time', seoData.modifiedTime);
      }
    }

    // Add structured data
    addStructuredData(seoData, title, description);

  }, [seoData, location.pathname]);

  // Helper function to update meta tags
  function updateMetaTag(name: string, content: string) {
    let meta = document.querySelector(`meta[name="${name}"]`) as HTMLMetaElement;
    if (!meta) {
      meta = document.createElement('meta');
      meta.name = name;
      document.head.appendChild(meta);
    }
    meta.content = content;
  }

  // Helper function to update meta property tags
  function updateMetaProperty(property: string, content: string) {
    let meta = document.querySelector(`meta[property="${property}"]`) as HTMLMetaElement;
    if (!meta) {
      meta = document.createElement('meta');
      meta.setAttribute('property', property);
      document.head.appendChild(meta);
    }
    meta.content = content;
  }

  // Helper function to update meta name tags
  function updateMetaName(name: string, content: string) {
    let meta = document.querySelector(`meta[name="${name}"]`) as HTMLMetaElement;
    if (!meta) {
      meta = document.createElement('meta');
      meta.name = name;
      document.head.appendChild(meta);
    }
    meta.content = content;
  }

  // Helper function to update canonical URL
  function updateCanonicalUrl(url: string) {
    let link = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
    if (!link) {
      link = document.createElement('link');
      link.rel = 'canonical';
      document.head.appendChild(link);
    }
    link.href = url;
  }

  // Helper function to add structured data
  function addStructuredData(seoData: SEOData, title: string, description: string) {
    // Remove existing structured data
    const existingScript = document.querySelector('script[data-seo="structured-data"]');
    if (existingScript) {
      existingScript.remove();
    }

    const structuredData = generateStructuredData(seoData, title, description);
    
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.setAttribute('data-seo', 'structured-data');
    script.textContent = JSON.stringify(structuredData);
    document.head.appendChild(script);
  }

  // Generate structured data based on content type
  function generateStructuredData(seoData: SEOData, title: string, description: string) {
    const baseData = {
      '@context': 'https://schema.org',
      name: title,
      description: description,
      url: `${baseUrl}${location.pathname}`,
      image: seoData.image || defaultSEO.image,
    };

    switch (seoData.type) {
      case 'article':
        return {
          ...baseData,
          '@type': 'Article',
          headline: seoData.title,
          author: {
            '@type': 'Person',
            name: seoData.author || 'The Chronicle Editorial Team',
          },
          publisher: {
            '@type': 'Organization',
            name: 'The Chronicle',
            logo: {
              '@type': 'ImageObject',
              url: `${baseUrl}/logo.png`,
            },
          },
          datePublished: seoData.publishedTime,
          dateModified: seoData.modifiedTime || seoData.publishedTime,
          mainEntityOfPage: {
            '@type': 'WebPage',
            '@id': `${baseUrl}${location.pathname}`,
          },
        };

      case 'product':
        return {
          ...baseData,
          '@type': 'Product',
          brand: {
            '@type': 'Brand',
            name: 'The Chronicle',
          },
        };

      default:
        return {
          ...baseData,
          '@type': 'WebPage',
        };
    }
  }

  return {
    updateSEO: (newSeoData: SEOData) => {
      // This would trigger a re-render with new SEO data
      // Implementation depends on how you want to handle dynamic updates
    },
  };
}

// Hook for tracking page views (for analytics)
export function usePageTracking() {
  const location = useLocation();

  useEffect(() => {
    // Google Analytics 4
    if (typeof gtag !== 'undefined') {
      gtag('config', 'GA_MEASUREMENT_ID', {
        page_path: location.pathname + location.search,
      });
    }

    // Google Tag Manager
    if (typeof dataLayer !== 'undefined') {
      dataLayer.push({
        event: 'page_view',
        page_path: location.pathname + location.search,
        page_title: document.title,
      });
    }

    // Custom analytics
    if (typeof window !== 'undefined' && (window as any).analytics) {
      (window as any).analytics.page(location.pathname + location.search);
    }

  }, [location]);
}

// Hook for performance monitoring
export function usePerformanceTracking() {
  useEffect(() => {
    // Core Web Vitals tracking
    if ('web-vitals' in window) {
      import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
        getCLS(console.log);
        getFID(console.log);
        getFCP(console.log);
        getLCP(console.log);
        getTTFB(console.log);
      });
    }

    // Performance observer for custom metrics
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          // Log performance metrics
          console.log('Performance:', entry.name, entry.duration);
        }
      });

      observer.observe({ entryTypes: ['navigation', 'paint', 'largest-contentful-paint'] });

      return () => observer.disconnect();
    }
  }, []);
}

// Declare global types for analytics
declare global {
  function gtag(...args: any[]): void;
  const dataLayer: any[];
}
