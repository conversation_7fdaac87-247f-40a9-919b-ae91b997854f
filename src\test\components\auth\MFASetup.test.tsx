import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '../../utils/test-utils';
import userEvent from '@testing-library/user-event';
import MFASetup from '../../../components/auth/MFASetup';

// Mock Supabase auth
const mockEnroll = vi.fn();
const mockVerify = vi.fn();
const mockUpdateUser = vi.fn();

vi.mock('../../../../supabase/client', () => ({
  supabase: {
    auth: {
      mfa: {
        enroll: mockEnroll,
        verify: mockVerify,
      },
      updateUser: mockUpdateUser,
    },
  },
}));

vi.mock('../../../../supabase/auth', () => ({
  useAuth: () => ({
    user: {
      id: 'test-user-id',
      email: '<EMAIL>',
    },
  }),
}));

describe('MFASetup', () => {
  const mockOnComplete = vi.fn();
  const mockOnSkip = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockEnroll.mockResolvedValue({
      data: {
        totp: {
          secret: 'test-secret',
          qr_code: 'test-qr-code',
        },
      },
      error: null,
    });
  });

  it('renders setup step initially', async () => {
    render(<MFASetup onComplete={mockOnComplete} onSkip={mockOnSkip} />);

    expect(screen.getByText('Set Up Two-Factor Authentication')).toBeInTheDocument();
    expect(screen.getByText('Add an extra layer of security to your account')).toBeInTheDocument();
    expect(screen.getByText('1. Install an authenticator app')).toBeInTheDocument();
    expect(screen.getByText('2. Scan the QR code')).toBeInTheDocument();
  });

  it('generates MFA secret on mount', async () => {
    render(<MFASetup onComplete={mockOnComplete} />);

    await waitFor(() => {
      expect(mockEnroll).toHaveBeenCalledWith({
        factorType: 'totp',
        friendlyName: 'The Chronicle App',
      });
    });
  });

  it('displays manual entry key when secret is available', async () => {
    render(<MFASetup onComplete={mockOnComplete} />);

    await waitFor(() => {
      expect(screen.getByText('Manual entry key:')).toBeInTheDocument();
      expect(screen.getByText('test-secret')).toBeInTheDocument();
    });
  });

  it('shows error when MFA generation fails', async () => {
    mockEnroll.mockResolvedValue({
      data: null,
      error: { message: 'Failed to generate secret' },
    });

    render(<MFASetup onComplete={mockOnComplete} />);

    await waitFor(() => {
      expect(screen.getByText('Failed to generate secret')).toBeInTheDocument();
    });
  });

  it('allows skipping MFA setup', async () => {
    const user = userEvent.setup();
    render(<MFASetup onComplete={mockOnComplete} onSkip={mockOnSkip} />);

    const skipButton = screen.getByRole('button', { name: 'Skip' });
    await user.click(skipButton);

    expect(mockOnSkip).toHaveBeenCalled();
  });

  it('proceeds to verification step', async () => {
    const user = userEvent.setup();
    render(<MFASetup onComplete={mockOnComplete} />);

    await waitFor(() => {
      expect(screen.getByRole('button', { name: 'Continue' })).toBeEnabled();
    });

    const continueButton = screen.getByRole('button', { name: 'Continue' });
    await user.click(continueButton);

    expect(screen.getByText('Verify Your Setup')).toBeInTheDocument();
    expect(screen.getByText('Enter the 6-digit code from your authenticator app')).toBeInTheDocument();
  });

  it('validates verification code input', async () => {
    const user = userEvent.setup();
    render(<MFASetup onComplete={mockOnComplete} />);

    // Go to verification step
    await waitFor(() => {
      expect(screen.getByRole('button', { name: 'Continue' })).toBeEnabled();
    });
    await user.click(screen.getByRole('button', { name: 'Continue' }));

    const codeInput = screen.getByPlaceholderText('000000');
    const verifyButton = screen.getByRole('button', { name: 'Verify' });

    // Verify button should be disabled initially
    expect(verifyButton).toBeDisabled();

    // Type partial code
    await user.type(codeInput, '123');
    expect(verifyButton).toBeDisabled();

    // Type complete code
    await user.type(codeInput, '456');
    expect(verifyButton).toBeEnabled();
    expect(codeInput).toHaveValue('123456');
  });

  it('handles verification code submission', async () => {
    const user = userEvent.setup();
    mockVerify.mockResolvedValue({
      data: { verified: true },
      error: null,
    });

    render(<MFASetup onComplete={mockOnComplete} />);

    // Go to verification step
    await waitFor(() => {
      expect(screen.getByRole('button', { name: 'Continue' })).toBeEnabled();
    });
    await user.click(screen.getByRole('button', { name: 'Continue' }));

    // Enter verification code
    const codeInput = screen.getByPlaceholderText('000000');
    await user.type(codeInput, '123456');

    // Submit verification
    const verifyButton = screen.getByRole('button', { name: 'Verify' });
    await user.click(verifyButton);

    await waitFor(() => {
      expect(mockVerify).toHaveBeenCalled();
      expect(mockUpdateUser).toHaveBeenCalledWith({
        data: { mfa_enabled: true },
      });
    });
  });

  it('shows error for invalid verification code', async () => {
    const user = userEvent.setup();
    mockVerify.mockResolvedValue({
      data: null,
      error: { message: 'Invalid verification code' },
    });

    render(<MFASetup onComplete={mockOnComplete} />);

    // Go to verification step and enter code
    await waitFor(() => {
      expect(screen.getByRole('button', { name: 'Continue' })).toBeEnabled();
    });
    await user.click(screen.getByRole('button', { name: 'Continue' }));

    const codeInput = screen.getByPlaceholderText('000000');
    await user.type(codeInput, '123456');

    const verifyButton = screen.getByRole('button', { name: 'Verify' });
    await user.click(verifyButton);

    await waitFor(() => {
      expect(screen.getByText('Invalid verification code')).toBeInTheDocument();
    });
  });

  it('shows completion step with backup codes', async () => {
    const user = userEvent.setup();
    mockVerify.mockResolvedValue({
      data: { verified: true },
      error: null,
    });

    render(<MFASetup onComplete={mockOnComplete} />);

    // Complete setup flow
    await waitFor(() => {
      expect(screen.getByRole('button', { name: 'Continue' })).toBeEnabled();
    });
    await user.click(screen.getByRole('button', { name: 'Continue' }));

    const codeInput = screen.getByPlaceholderText('000000');
    await user.type(codeInput, '123456');

    const verifyButton = screen.getByRole('button', { name: 'Verify' });
    await user.click(verifyButton);

    await waitFor(() => {
      expect(screen.getByText('MFA Setup Complete!')).toBeInTheDocument();
      expect(screen.getByText('Backup Codes:')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Download Codes' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Complete Setup' })).toBeInTheDocument();
    });
  });

  it('allows downloading backup codes', async () => {
    const user = userEvent.setup();
    mockVerify.mockResolvedValue({
      data: { verified: true },
      error: null,
    });

    // Mock URL.createObjectURL
    const mockCreateObjectURL = vi.fn(() => 'mock-url');
    const mockRevokeObjectURL = vi.fn();
    Object.defineProperty(URL, 'createObjectURL', { value: mockCreateObjectURL });
    Object.defineProperty(URL, 'revokeObjectURL', { value: mockRevokeObjectURL });

    // Mock document.createElement and click
    const mockClick = vi.fn();
    const mockAppendChild = vi.fn();
    const mockRemoveChild = vi.fn();
    const mockAnchor = {
      href: '',
      download: '',
      click: mockClick,
    };
    vi.spyOn(document, 'createElement').mockReturnValue(mockAnchor as any);
    vi.spyOn(document.body, 'appendChild').mockImplementation(mockAppendChild);
    vi.spyOn(document.body, 'removeChild').mockImplementation(mockRemoveChild);

    render(<MFASetup onComplete={mockOnComplete} />);

    // Complete setup flow to get to backup codes
    await waitFor(() => {
      expect(screen.getByRole('button', { name: 'Continue' })).toBeEnabled();
    });
    await user.click(screen.getByRole('button', { name: 'Continue' }));

    const codeInput = screen.getByPlaceholderText('000000');
    await user.type(codeInput, '123456');

    const verifyButton = screen.getByRole('button', { name: 'Verify' });
    await user.click(verifyButton);

    await waitFor(() => {
      expect(screen.getByText('MFA Setup Complete!')).toBeInTheDocument();
    });

    // Download backup codes
    const downloadButton = screen.getByRole('button', { name: 'Download Codes' });
    await user.click(downloadButton);

    expect(mockCreateObjectURL).toHaveBeenCalled();
    expect(mockClick).toHaveBeenCalled();
    expect(mockRevokeObjectURL).toHaveBeenCalled();
  });

  it('calls onComplete when setup is finished', async () => {
    const user = userEvent.setup();
    mockVerify.mockResolvedValue({
      data: { verified: true },
      error: null,
    });

    render(<MFASetup onComplete={mockOnComplete} />);

    // Complete entire flow
    await waitFor(() => {
      expect(screen.getByRole('button', { name: 'Continue' })).toBeEnabled();
    });
    await user.click(screen.getByRole('button', { name: 'Continue' }));

    const codeInput = screen.getByPlaceholderText('000000');
    await user.type(codeInput, '123456');

    const verifyButton = screen.getByRole('button', { name: 'Verify' });
    await user.click(verifyButton);

    await waitFor(() => {
      expect(screen.getByText('MFA Setup Complete!')).toBeInTheDocument();
    });

    const completeButton = screen.getByRole('button', { name: 'Complete Setup' });
    await user.click(completeButton);

    expect(mockOnComplete).toHaveBeenCalled();
  });

  it('allows going back from verification step', async () => {
    const user = userEvent.setup();
    render(<MFASetup onComplete={mockOnComplete} />);

    // Go to verification step
    await waitFor(() => {
      expect(screen.getByRole('button', { name: 'Continue' })).toBeEnabled();
    });
    await user.click(screen.getByRole('button', { name: 'Continue' }));

    expect(screen.getByText('Verify Your Setup')).toBeInTheDocument();

    // Go back
    const backButton = screen.getByRole('button', { name: 'Back' });
    await user.click(backButton);

    expect(screen.getByText('Set Up Two-Factor Authentication')).toBeInTheDocument();
  });
});
