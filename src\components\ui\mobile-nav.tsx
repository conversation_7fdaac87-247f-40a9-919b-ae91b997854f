import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Sheet, SheetContent, Sheet<PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from '@/components/ui/sheet';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Menu,
  X,
  Home,
  FileText,
  User,
  Search,
  Bell,
  Settings,
  LogOut,
  Heart,
  ShoppingBag,
  Bookmark,
  TrendingUp,
  Calendar,
  Mail,
  HelpCircle,
} from 'lucide-react';
import { useAuth } from '../../../supabase/auth';
import { useCart } from '@/contexts/CartContext';
import { GlobalSearch } from '@/components/ui/global-search';

// Temporary fallback hook
function useNotificationCount() {
  const { user } = useAuth();
  return user ? 2 : 0; // Mock notification count
}

interface MobileNavProps {
  className?: string;
}

interface NavItem {
  label: string;
  href: string;
  icon: React.ReactNode;
  badge?: string;
}

const publicNavItems: NavItem[] = [
  { label: 'Home', href: '/', icon: <Home className="h-5 w-5" /> },
  { label: 'Articles', href: '/articles', icon: <FileText className="h-5 w-5" /> },
  { label: 'Products', href: '/products', icon: <ShoppingBag className="h-5 w-5" /> },
  { label: 'About', href: '/about', icon: <User className="h-5 w-5" /> },
  { label: 'Contact', href: '/contact', icon: <Mail className="h-5 w-5" /> },
];

const userNavItems: NavItem[] = [
  { label: 'Dashboard', href: '/dashboard', icon: <TrendingUp className="h-5 w-5" /> },
  { label: 'My Articles', href: '/dashboard/articles', icon: <FileText className="h-5 w-5" /> },
  { label: 'Favorites', href: '/dashboard/favorites', icon: <Heart className="h-5 w-5" /> },
  { label: 'Bookmarks', href: '/dashboard/bookmarks', icon: <Bookmark className="h-5 w-5" /> },
  { label: 'Settings', href: '/dashboard/settings', icon: <Settings className="h-5 w-5" /> },
];

export function MobileNav({ className }: MobileNavProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const { user, signOut, isAdmin } = useAuth();
  const { getItemCount } = useCart();
  const location = useLocation();
  const cartItemCount = getItemCount();
  const notificationCount = useNotificationCount();

  const handleSignOut = async () => {
    try {
      await signOut();
      setIsOpen(false);
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const isActive = (href: string) => {
    return location.pathname === href;
  };

  return (
    <div className={`md:hidden ${className}`}>
      {/* Quick Actions */}
      <div className="flex items-center space-x-2">
        {/* Search */}
        <div className="relative">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsSearchOpen(true)}
          >
            <Search className="h-5 w-5" />
          </Button>
          <GlobalSearch
            isOpen={isSearchOpen}
            onClose={() => setIsSearchOpen(false)}
          />
        </div>

        {/* Notifications */}
        {user && (
          <Button variant="ghost" size="icon" className="relative">
            <Bell className="h-5 w-5" />
            {notificationCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                {notificationCount}
              </span>
            )}
          </Button>
        )}

        {/* Cart */}
        <Link to="/cart">
          <Button variant="ghost" size="icon" className="relative">
            <ShoppingBag className="h-5 w-5" />
            {cartItemCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                {cartItemCount}
              </span>
            )}
          </Button>
        </Link>

        {/* Hamburger Menu - Now on the right */}
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetTrigger asChild>
            <Button variant="ghost" size="icon" className="relative">
              <Menu className="h-6 w-6" />
              <span className="sr-only">Toggle menu</span>
            </Button>
          </SheetTrigger>
        <SheetContent side="left" className="w-80 p-0">
          <SheetHeader className="sr-only">
            <SheetTitle>Navigation Menu</SheetTitle>
          </SheetHeader>
          <div className="flex flex-col h-full">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">TC</span>
                </div>
                <span className="font-semibold text-lg">The Chronicle</span>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsOpen(false)}
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* User Info */}
            {user && (
              <div className="p-6 border-b bg-gray-50">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-12 w-12">
                    <AvatarImage
                      src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${user.email}`}
                      alt={user.email || ''}
                    />
                    <AvatarFallback>
                      {user.email?.[0].toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {user.email}
                    </p>
                    <div className="flex items-center space-x-2">
                      {isAdmin && (
                        <Badge variant="secondary" className="text-xs">
                          Admin
                        </Badge>
                      )}
                      <Badge variant="outline" className="text-xs">
                        Premium
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Navigation */}
            <div className="flex-1 overflow-y-auto">
              <nav className="p-4 space-y-2">
                {/* Public Navigation */}
                <div className="space-y-1">
                  <h3 className="px-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Navigation
                  </h3>
                  {publicNavItems.map((item) => (
                    <Link
                      key={item.href}
                      to={item.href}
                      onClick={() => setIsOpen(false)}
                      className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                        isActive(item.href)
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      {item.icon}
                      <span className="ml-3">{item.label}</span>
                      {item.badge && (
                        <Badge variant="secondary" className="ml-auto text-xs">
                          {item.badge}
                        </Badge>
                      )}
                    </Link>
                  ))}
                </div>

                {/* User Navigation */}
                {user && (
                  <>
                    <Separator className="my-4" />
                    <div className="space-y-1">
                      <h3 className="px-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Account
                      </h3>
                      {userNavItems.map((item) => (
                        <Link
                          key={item.href}
                          to={item.href}
                          onClick={() => setIsOpen(false)}
                          className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                            isActive(item.href)
                              ? 'bg-blue-100 text-blue-700'
                              : 'text-gray-700 hover:bg-gray-100'
                          }`}
                        >
                          {item.icon}
                          <span className="ml-3">{item.label}</span>
                          {item.badge && (
                            <Badge variant="secondary" className="ml-auto text-xs">
                              {item.badge}
                            </Badge>
                          )}
                        </Link>
                      ))}
                    </div>
                  </>
                )}

                {/* Admin Navigation */}
                {isAdmin && (
                  <>
                    <Separator className="my-4" />
                    <div className="space-y-1">
                      <h3 className="px-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Admin
                      </h3>
                      <Link
                        to="/admin"
                        onClick={() => setIsOpen(false)}
                        className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                          isActive('/admin')
                            ? 'bg-purple-100 text-purple-700'
                            : 'text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <Settings className="h-5 w-5" />
                        <span className="ml-3">Admin Panel</span>
                      </Link>
                    </div>
                  </>
                )}
              </nav>
            </div>

            {/* Footer Actions */}
            <div className="p-4 border-t bg-gray-50">
              {user ? (
                <div className="space-y-2">
                  <Link
                    to="/help"
                    onClick={() => setIsOpen(false)}
                    className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <HelpCircle className="h-5 w-5" />
                    <span className="ml-3">Help & Support</span>
                  </Link>
                  <Button
                    variant="ghost"
                    onClick={handleSignOut}
                    className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <LogOut className="h-5 w-5" />
                    <span className="ml-3">Sign Out</span>
                  </Button>
                </div>
              ) : (
                <div className="space-y-2">
                  <Link to="/login" onClick={() => setIsOpen(false)}>
                    <Button className="w-full">Sign In</Button>
                  </Link>
                  <Link to="/signup" onClick={() => setIsOpen(false)}>
                    <Button variant="outline" className="w-full">
                      Sign Up
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          </div>
        </SheetContent>
        </Sheet>
      </div>
    </div>
  );
}
