import React, { createContext, useContext, useEffect, useState } from 'react';

type Theme = 'light' | 'dark' | 'system';

interface ThemeContextType {
  theme: Theme;
  actualTheme: 'light' | 'dark';
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<Theme>(() => {
    // Get theme from localStorage or default to system
    const savedTheme = localStorage.getItem('theme') as Theme;
    return savedTheme || 'system';
  });

  const [actualTheme, setActualTheme] = useState<'light' | 'dark'>('light');

  // Function to get system theme
  const getSystemTheme = (): 'light' | 'dark' => {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  };

  // Function to apply theme to document
  const applyTheme = (themeToApply: 'light' | 'dark') => {
    const root = document.documentElement;
    
    if (themeToApply === 'dark') {
      root.classList.add('dark');
      // Apply gold accent colors for dark mode
      root.style.setProperty('--primary', '45 93% 58%'); // Gold
      root.style.setProperty('--primary-foreground', '26 83% 14%'); // Dark gold
      root.style.setProperty('--accent', '45 93% 58%'); // Gold
      root.style.setProperty('--accent-foreground', '26 83% 14%'); // Dark gold
      root.style.setProperty('--ring', '45 93% 58%'); // Gold ring
    } else {
      root.classList.remove('dark');
      // Apply blue accent colors for light mode
      root.style.setProperty('--primary', '221.2 83.2% 53.3%'); // Blue
      root.style.setProperty('--primary-foreground', '210 40% 98%'); // Light blue
      root.style.setProperty('--accent', '210 40% 96%'); // Light accent
      root.style.setProperty('--accent-foreground', '222.2 84% 4.9%'); // Dark accent
      root.style.setProperty('--ring', '221.2 83.2% 53.3%'); // Blue ring
    }
    
    setActualTheme(themeToApply);
  };

  // Update theme when theme state changes
  useEffect(() => {
    let themeToApply: 'light' | 'dark';

    if (theme === 'system') {
      themeToApply = getSystemTheme();
    } else {
      themeToApply = theme;
    }

    applyTheme(themeToApply);
    localStorage.setItem('theme', theme);
  }, [theme]);

  // Listen for system theme changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = () => {
      if (theme === 'system') {
        applyTheme(getSystemTheme());
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [theme]);

  // Initialize theme on mount
  useEffect(() => {
    const initialTheme = theme === 'system' ? getSystemTheme() : theme;
    applyTheme(initialTheme);
  }, []);

  const toggleTheme = () => {
    if (theme === 'light') {
      setTheme('dark');
    } else if (theme === 'dark') {
      setTheme('system');
    } else {
      setTheme('light');
    }
  };

  const value: ThemeContextType = {
    theme,
    actualTheme,
    setTheme,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Theme configuration object for easy access
export const themeConfig = {
  light: {
    primary: 'hsl(221.2, 83.2%, 53.3%)', // Blue
    primaryForeground: 'hsl(210, 40%, 98%)',
    accent: 'hsl(210, 40%, 96%)',
    accentForeground: 'hsl(222.2, 84%, 4.9%)',
    background: 'hsl(0, 0%, 100%)',
    foreground: 'hsl(222.2, 84%, 4.9%)',
    card: 'hsl(0, 0%, 100%)',
    cardForeground: 'hsl(222.2, 84%, 4.9%)',
    border: 'hsl(214.3, 31.8%, 91.4%)',
    ring: 'hsl(221.2, 83.2%, 53.3%)',
  },
  dark: {
    primary: 'hsl(45, 93%, 58%)', // Gold
    primaryForeground: 'hsl(26, 83%, 14%)',
    accent: 'hsl(45, 93%, 58%)', // Gold
    accentForeground: 'hsl(26, 83%, 14%)',
    background: 'hsl(222.2, 84%, 4.9%)',
    foreground: 'hsl(210, 40%, 98%)',
    card: 'hsl(222.2, 84%, 4.9%)',
    cardForeground: 'hsl(210, 40%, 98%)',
    border: 'hsl(217.2, 32.6%, 17.5%)',
    ring: 'hsl(45, 93%, 58%)', // Gold
  },
};

// Utility function to get current theme colors
export function getCurrentThemeColors(actualTheme: 'light' | 'dark') {
  return themeConfig[actualTheme];
}

// CSS custom properties for dynamic theming
export const cssVariables = {
  light: {
    '--background': '0 0% 100%',
    '--foreground': '222.2 84% 4.9%',
    '--card': '0 0% 100%',
    '--card-foreground': '222.2 84% 4.9%',
    '--popover': '0 0% 100%',
    '--popover-foreground': '222.2 84% 4.9%',
    '--primary': '221.2 83.2% 53.3%',
    '--primary-foreground': '210 40% 98%',
    '--secondary': '210 40% 96%',
    '--secondary-foreground': '222.2 84% 4.9%',
    '--muted': '210 40% 96%',
    '--muted-foreground': '215.4 16.3% 46.9%',
    '--accent': '210 40% 96%',
    '--accent-foreground': '222.2 84% 4.9%',
    '--destructive': '0 84.2% 60.2%',
    '--destructive-foreground': '210 40% 98%',
    '--border': '214.3 31.8% 91.4%',
    '--input': '214.3 31.8% 91.4%',
    '--ring': '221.2 83.2% 53.3%',
    '--radius': '0.5rem',
  },
  dark: {
    '--background': '222.2 84% 4.9%',
    '--foreground': '210 40% 98%',
    '--card': '222.2 84% 4.9%',
    '--card-foreground': '210 40% 98%',
    '--popover': '222.2 84% 4.9%',
    '--popover-foreground': '210 40% 98%',
    '--primary': '45 93% 58%', // Gold
    '--primary-foreground': '26 83% 14%',
    '--secondary': '217.2 32.6% 17.5%',
    '--secondary-foreground': '210 40% 98%',
    '--muted': '217.2 32.6% 17.5%',
    '--muted-foreground': '215 20.2% 65.1%',
    '--accent': '45 93% 58%', // Gold
    '--accent-foreground': '26 83% 14%',
    '--destructive': '0 62.8% 30.6%',
    '--destructive-foreground': '210 40% 98%',
    '--border': '217.2 32.6% 17.5%',
    '--input': '217.2 32.6% 17.5%',
    '--ring': '45 93% 58%', // Gold
    '--radius': '0.5rem',
  },
};
