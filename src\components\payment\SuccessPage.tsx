import React, { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  CheckCircle,
  Package,
  Mail,
  Download,
  ArrowRight,
  Home,
  ShoppingBag,
  Calendar,
  Truck,
  Star,
} from 'lucide-react';

interface OrderDetails {
  orderNumber: string;
  orderDate: string;
  estimatedDelivery: string;
  items: Array<{
    id: string;
    name: string;
    price: number;
    quantity: number;
    type: 'physical' | 'digital';
    downloadUrl?: string;
  }>;
  subtotal: number;
  shipping: number;
  tax: number;
  total: number;
  shippingAddress: {
    name: string;
    address: string;
    city: string;
    postalCode: string;
    country: string;
  };
}

export function SuccessPage() {
  const navigate = useNavigate();
  const [orderDetails, setOrderDetails] = useState<OrderDetails | null>(null);
  const [showConfetti, setShowConfetti] = useState(true);

  useEffect(() => {
    // Simulate loading order details
    const mockOrderDetails: OrderDetails = {
      orderNumber: `ORD-${Date.now()}`,
      orderDate: new Date().toLocaleDateString(),
      estimatedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString(),
      items: [
        {
          id: '1',
          name: 'Premium Wireless Headphones',
          price: 249.99,
          quantity: 1,
          type: 'physical',
        },
        {
          id: '2',
          name: 'Digital Photography Course',
          price: 99.99,
          quantity: 1,
          type: 'digital',
          downloadUrl: '/downloads/photography-course',
        },
      ],
      subtotal: 349.98,
      shipping: 0,
      tax: 28.00,
      total: 377.98,
      shippingAddress: {
        name: 'John Doe',
        address: '123 Main Street',
        city: 'New York',
        postalCode: '10001',
        country: 'United States',
      },
    };

    setOrderDetails(mockOrderDetails);

    // Hide confetti after 3 seconds
    const timer = setTimeout(() => setShowConfetti(false), 3000);
    return () => clearTimeout(timer);
  }, []);

  const physicalItems = orderDetails?.items.filter(item => item.type === 'physical') || [];
  const digitalItems = orderDetails?.items.filter(item => item.type === 'digital') || [];

  if (!orderDetails) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-48 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-32"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 relative">
      {/* Confetti Animation */}
      {showConfetti && (
        <div className="fixed inset-0 pointer-events-none z-10">
          <div className="absolute inset-0 overflow-hidden">
            {[...Array(50)].map((_, i) => (
              <div
                key={i}
                className="absolute animate-bounce"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 2}s`,
                  animationDuration: `${2 + Math.random() * 2}s`,
                }}
              >
                <div
                  className="w-2 h-2 rounded-full"
                  style={{
                    backgroundColor: ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6'][
                      Math.floor(Math.random() * 5)
                    ],
                  }}
                />
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="max-w-4xl mx-auto px-4 py-12">
        {/* Success Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-green-100 rounded-full mb-6">
            <CheckCircle className="h-10 w-10 text-green-600" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Order Confirmed!
          </h1>
          <p className="text-xl text-gray-600 mb-2">
            Thank you for your purchase. Your order has been successfully processed.
          </p>
          <p className="text-gray-500">
            Order #{orderDetails.orderNumber} • Placed on {orderDetails.orderDate}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Order Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Physical Items */}
            {physicalItems.length > 0 && (
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <Package className="h-5 w-5 text-blue-600" />
                    <h3 className="text-lg font-semibold">Physical Items</h3>
                    <Badge variant="outline" className="ml-auto">
                      Ships to your address
                    </Badge>
                  </div>
                  
                  <div className="space-y-4">
                    {physicalItems.map((item) => (
                      <div key={item.id} className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
                        <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                          <Package className="h-6 w-6 text-gray-400" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium">{item.name}</h4>
                          <p className="text-sm text-gray-600">Quantity: {item.quantity}</p>
                        </div>
                        <div className="text-right">
                          <div className="font-semibold">${(item.price * item.quantity).toFixed(2)}</div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Truck className="h-4 w-4 text-blue-600" />
                      <span className="font-medium text-blue-900">Shipping Information</span>
                    </div>
                    <p className="text-sm text-blue-800 mb-1">
                      Estimated delivery: {orderDetails.estimatedDelivery}
                    </p>
                    <p className="text-sm text-blue-700">
                      We'll send you tracking information once your order ships.
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Digital Items */}
            {digitalItems.length > 0 && (
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <Download className="h-5 w-5 text-green-600" />
                    <h3 className="text-lg font-semibold">Digital Items</h3>
                    <Badge variant="outline" className="ml-auto bg-green-50 text-green-700">
                      Available now
                    </Badge>
                  </div>
                  
                  <div className="space-y-4">
                    {digitalItems.map((item) => (
                      <div key={item.id} className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
                        <div className="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center">
                          <Download className="h-6 w-6 text-green-600" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium">{item.name}</h4>
                          <p className="text-sm text-gray-600">Digital download</p>
                        </div>
                        <div className="text-right">
                          <div className="font-semibold mb-2">${item.price.toFixed(2)}</div>
                          <Button size="sm" className="bg-green-600 hover:bg-green-700">
                            <Download className="h-3 w-3 mr-1" />
                            Download
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="mt-6 p-4 bg-green-50 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Mail className="h-4 w-4 text-green-600" />
                      <span className="font-medium text-green-900">Download Links</span>
                    </div>
                    <p className="text-sm text-green-800">
                      Download links have been sent to your email address. Links are valid for 30 days.
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Next Steps */}
            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold mb-4">What's Next?</h3>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-semibold text-blue-600">1</span>
                    </div>
                    <div>
                      <h4 className="font-medium">Order Confirmation Email</h4>
                      <p className="text-sm text-gray-600">
                        You'll receive a confirmation email with your order details and receipt.
                      </p>
                    </div>
                  </div>
                  
                  {physicalItems.length > 0 && (
                    <div className="flex items-start gap-3">
                      <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-xs font-semibold text-blue-600">2</span>
                      </div>
                      <div>
                        <h4 className="font-medium">Order Processing</h4>
                        <p className="text-sm text-gray-600">
                          We'll prepare your order for shipping within 1-2 business days.
                        </p>
                      </div>
                    </div>
                  )}
                  
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-semibold text-blue-600">
                        {physicalItems.length > 0 ? '3' : '2'}
                      </span>
                    </div>
                    <div>
                      <h4 className="font-medium">Customer Support</h4>
                      <p className="text-sm text-gray-600">
                        Need help? Contact our support team <NAME_EMAIL>
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Order Summary Sidebar */}
          <div className="space-y-6">
            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold mb-4">Order Summary</h3>
                
                <div className="space-y-3 mb-4">
                  <div className="flex justify-between">
                    <span>Subtotal</span>
                    <span>${orderDetails.subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Shipping</span>
                    <span>{orderDetails.shipping === 0 ? 'Free' : `$${orderDetails.shipping.toFixed(2)}`}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Tax</span>
                    <span>${orderDetails.tax.toFixed(2)}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-semibold text-lg">
                    <span>Total</span>
                    <span>${orderDetails.total.toFixed(2)}</span>
                  </div>
                </div>

                <div className="space-y-3">
                  <Button onClick={() => navigate('/dashboard')} className="w-full">
                    <Home className="h-4 w-4 mr-2" />
                    Go to Dashboard
                  </Button>
                  <Button variant="outline" onClick={() => navigate('/products')} className="w-full">
                    <ShoppingBag className="h-4 w-4 mr-2" />
                    Continue Shopping
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Shipping Address */}
            {physicalItems.length > 0 && (
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold mb-4">Shipping Address</h3>
                  <div className="text-sm space-y-1">
                    <div className="font-medium">{orderDetails.shippingAddress.name}</div>
                    <div>{orderDetails.shippingAddress.address}</div>
                    <div>
                      {orderDetails.shippingAddress.city}, {orderDetails.shippingAddress.postalCode}
                    </div>
                    <div>{orderDetails.shippingAddress.country}</div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Review Prompt */}
            <Card className="bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200">
              <CardContent className="p-6 text-center">
                <Star className="h-8 w-8 text-yellow-500 mx-auto mb-3" />
                <h3 className="font-semibold mb-2">Love your purchase?</h3>
                <p className="text-sm text-gray-600 mb-4">
                  Share your experience and help other customers make informed decisions.
                </p>
                <Button variant="outline" size="sm" className="border-yellow-300 hover:bg-yellow-50">
                  Write a Review
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
