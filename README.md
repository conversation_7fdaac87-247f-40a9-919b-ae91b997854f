# News & Commerce Platform

A modern React TypeScript application built with Vite, featuring authentication, dashboard, and e-commerce functionality.

## Features

- 🔐 **Authentication** - Supabase-powered auth with role-based access
- 📊 **Dashboard** - Admin panel with user management and analytics
- 🛒 **E-commerce** - Product and order management
- 🎨 **Modern UI** - Built with Radix UI and Tailwind CSS
- 🔧 **Developer Experience** - TypeScript, ESLint, and comprehensive tooling

## Tech Stack

- **Frontend**: React 18, TypeScript, Vite
- **UI Components**: Radix UI, Tailwind CSS, Lucide Icons
- **Backend**: Supabase (Auth, Database, Real-time)
- **State Management**: React Context
- **Routing**: React Router v6
- **Forms**: React Hook Form with Zod validation
- **Development**: ESLint, TypeScript strict mode, Tempo devtools

## Quick Start

1. **Clone and install dependencies**
   ```bash
   npm install
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   Fill in your Supabase credentials in the `.env` file.

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Build for production**
   ```bash
   npm run build
   ```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run type-check` - Run TypeScript type checking
- `npm run check` - Run both type checking and linting
- `npm run types:supabase` - Generate Supabase types

## Environment Variables

Copy `.env.example` to `.env` and configure:

- `VITE_SUPABASE_URL` - Your Supabase project URL
- `VITE_SUPABASE_ANON_KEY` - Your Supabase anonymous key
- `SUPABASE_PROJECT_ID` - For type generation
- `VITE_TEMPO` - Enable Tempo devtools (optional)

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── auth/           # Authentication components
│   ├── dashboard/      # Dashboard-specific components
│   ├── pages/          # Page components
│   └── ui/             # Base UI components (shadcn/ui)
├── lib/                # Utility functions
├── types/              # TypeScript type definitions
└── supabase/           # Supabase configuration and auth
```

## Recent Improvements

✅ **Fixed Critical Issues:**
- Removed unused imports and missing dependencies
- Added proper ESLint configuration
- Fixed TypeScript strict mode issues
- Improved error handling with Error Boundary
- Added environment variable validation

✅ **Enhanced Developer Experience:**
- Enabled TypeScript strict mode for better type safety
- Added comprehensive linting rules
- Improved build scripts and type checking
- Added proper environment variable documentation

## Expanding the ESLint configuration

If you are developing a production application, we recommend updating the configuration to enable type aware lint rules:

- Configure the top-level `parserOptions` property like this:

```js
export default {
  // other rules...
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
    project: ['./tsconfig.json', './tsconfig.node.json'],
    tsconfigRootDir: __dirname,
  },
}
```

- Replace `plugin:@typescript-eslint/recommended` to `plugin:@typescript-eslint/recommended-type-checked` or `plugin:@typescript-eslint/strict-type-checked`
- Optionally add `plugin:@typescript-eslint/stylistic-type-checked`
- Install [eslint-plugin-react](https://github.com/jsx-eslint/eslint-plugin-react) and add `plugin:react/recommended` & `plugin:react/jsx-runtime` to the `extends` list
