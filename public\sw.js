// Advanced Service Worker for The Chronicle
// Intelligent caching, offline support, and performance optimization

const CACHE_NAME = 'chronicle-v1.0.0';
const STATIC_CACHE = 'chronicle-static-v1.0.0';
const DYNAMIC_CACHE = 'chronicle-dynamic-v1.0.0';
const API_CACHE = 'chronicle-api-v1.0.0';
const IMAGE_CACHE = 'chronicle-images-v1.0.0';

// Cache strategies
const CACHE_STRATEGIES = {
  CACHE_FIRST: 'cache-first',
  NETWORK_FIRST: 'network-first',
  STALE_WHILE_REVALIDATE: 'stale-while-revalidate',
  NETWORK_ONLY: 'network-only',
  CACHE_ONLY: 'cache-only',
};

// Static assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/manifest.json',
  '/offline.html',
  // Core CSS and JS will be added dynamically
];

// API endpoints configuration
const API_CONFIG = {
  '/api/articles': {
    strategy: CACHE_STRATEGIES.STALE_WHILE_REVALIDATE,
    ttl: 300000, // 5 minutes
    maxEntries: 50,
  },
  '/api/products': {
    strategy: CACHE_STRATEGIES.STALE_WHILE_REVALIDATE,
    ttl: 600000, // 10 minutes
    maxEntries: 50,
  },
  '/api/categories': {
    strategy: CACHE_STRATEGIES.CACHE_FIRST,
    ttl: 3600000, // 1 hour
    maxEntries: 20,
  },
  '/api/auth': {
    strategy: CACHE_STRATEGIES.NETWORK_ONLY,
  },
};

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    Promise.all([
      caches.open(STATIC_CACHE).then((cache) => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      }),
      self.skipWaiting(),
    ])
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (
              cacheName !== CACHE_NAME &&
              cacheName !== STATIC_CACHE &&
              cacheName !== DYNAMIC_CACHE &&
              cacheName !== API_CACHE &&
              cacheName !== IMAGE_CACHE
            ) {
              console.log('Service Worker: Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      self.clients.claim(),
    ])
  );
});

// Fetch event - handle all network requests
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Skip chrome-extension and other non-http requests
  if (!url.protocol.startsWith('http')) {
    return;
  }
  
  // Handle different types of requests
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleApiRequest(request));
  } else if (isImageRequest(request)) {
    event.respondWith(handleImageRequest(request));
  } else if (isStaticAsset(request)) {
    event.respondWith(handleStaticAsset(request));
  } else {
    event.respondWith(handlePageRequest(request));
  }
});

// Handle API requests with intelligent caching
async function handleApiRequest(request) {
  const url = new URL(request.url);
  const pathname = url.pathname;
  
  // Find matching API configuration
  const apiConfig = Object.keys(API_CONFIG).find(pattern => 
    pathname.startsWith(pattern)
  );
  
  const config = apiConfig ? API_CONFIG[apiConfig] : {
    strategy: CACHE_STRATEGIES.NETWORK_FIRST,
    ttl: 300000,
  };
  
  switch (config.strategy) {
    case CACHE_STRATEGIES.CACHE_FIRST:
      return cacheFirst(request, API_CACHE, config);
    case CACHE_STRATEGIES.NETWORK_FIRST:
      return networkFirst(request, API_CACHE, config);
    case CACHE_STRATEGIES.STALE_WHILE_REVALIDATE:
      return staleWhileRevalidate(request, API_CACHE, config);
    case CACHE_STRATEGIES.NETWORK_ONLY:
      return fetch(request);
    case CACHE_STRATEGIES.CACHE_ONLY:
      return cacheOnly(request, API_CACHE);
    default:
      return networkFirst(request, API_CACHE, config);
  }
}

// Handle image requests with aggressive caching
async function handleImageRequest(request) {
  return cacheFirst(request, IMAGE_CACHE, {
    ttl: 86400000, // 24 hours
    maxEntries: 100,
  });
}

// Handle static assets with cache-first strategy
async function handleStaticAsset(request) {
  return cacheFirst(request, STATIC_CACHE, {
    ttl: 31536000000, // 1 year
    maxEntries: 200,
  });
}

// Handle page requests with network-first strategy
async function handlePageRequest(request) {
  return networkFirst(request, DYNAMIC_CACHE, {
    ttl: 600000, // 10 minutes
    maxEntries: 50,
    fallback: '/offline.html',
  });
}

// Cache-first strategy
async function cacheFirst(request, cacheName, config = {}) {
  try {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse && !isExpired(cachedResponse, config.ttl)) {
      // Return cached response and update in background
      updateCacheInBackground(request, cache, config);
      return addCacheHeaders(cachedResponse, 'HIT');
    }
    
    // Cache miss or expired - fetch from network
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      await updateCache(cache, request, networkResponse.clone(), config);
    }
    
    return addCacheHeaders(networkResponse, 'MISS');
  } catch (error) {
    console.error('Cache-first strategy failed:', error);
    
    // Try to return stale cache as fallback
    const cache = await caches.open(cacheName);
    const staleResponse = await cache.match(request);
    
    if (staleResponse) {
      return addCacheHeaders(staleResponse, 'STALE');
    }
    
    throw error;
  }
}

// Network-first strategy
async function networkFirst(request, cacheName, config = {}) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      await updateCache(cache, request, networkResponse.clone(), config);
    }
    
    return addCacheHeaders(networkResponse, 'NETWORK');
  } catch (error) {
    console.error('Network request failed:', error);
    
    // Fallback to cache
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return addCacheHeaders(cachedResponse, 'FALLBACK');
    }
    
    // Return offline page for navigation requests
    if (config.fallback && request.mode === 'navigate') {
      const fallbackCache = await caches.open(STATIC_CACHE);
      const fallbackResponse = await fallbackCache.match(config.fallback);
      if (fallbackResponse) {
        return fallbackResponse;
      }
    }
    
    throw error;
  }
}

// Stale-while-revalidate strategy
async function staleWhileRevalidate(request, cacheName, config = {}) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  // Always try to update cache in background
  const networkPromise = fetch(request).then(async (networkResponse) => {
    if (networkResponse.ok) {
      await updateCache(cache, request, networkResponse.clone(), config);
    }
    return networkResponse;
  }).catch((error) => {
    console.error('Background revalidation failed:', error);
  });
  
  // Return cached response immediately if available
  if (cachedResponse) {
    // Don't await the network promise - let it run in background
    networkPromise;
    return addCacheHeaders(cachedResponse, 'SWR-CACHED');
  }
  
  // No cache - wait for network
  try {
    const networkResponse = await networkPromise;
    return addCacheHeaders(networkResponse, 'SWR-NETWORK');
  } catch (error) {
    throw error;
  }
}

// Cache-only strategy
async function cacheOnly(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse) {
    return addCacheHeaders(cachedResponse, 'CACHE-ONLY');
  }
  
  throw new Error('No cached response available');
}

// Update cache with response
async function updateCache(cache, request, response, config = {}) {
  try {
    // Add timestamp for TTL checking
    const responseWithTimestamp = new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        ...Object.fromEntries(response.headers.entries()),
        'sw-cached-at': Date.now().toString(),
      },
    });
    
    await cache.put(request, responseWithTimestamp);
    
    // Implement cache size limits
    if (config.maxEntries) {
      await limitCacheSize(cache, config.maxEntries);
    }
  } catch (error) {
    console.error('Failed to update cache:', error);
  }
}

// Update cache in background
async function updateCacheInBackground(request, cache, config) {
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      await updateCache(cache, request, networkResponse, config);
    }
  } catch (error) {
    console.error('Background cache update failed:', error);
  }
}

// Check if cached response is expired
function isExpired(response, ttl) {
  if (!ttl) return false;
  
  const cachedAt = response.headers.get('sw-cached-at');
  if (!cachedAt) return true;
  
  const age = Date.now() - parseInt(cachedAt);
  return age > ttl;
}

// Add cache status headers
function addCacheHeaders(response, status) {
  const newResponse = new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: {
      ...Object.fromEntries(response.headers.entries()),
      'x-cache-status': status,
      'x-cache-date': new Date().toISOString(),
    },
  });
  
  return newResponse;
}

// Limit cache size by removing oldest entries
async function limitCacheSize(cache, maxEntries) {
  const keys = await cache.keys();
  
  if (keys.length > maxEntries) {
    // Sort by cache date and remove oldest
    const keysWithDates = await Promise.all(
      keys.map(async (key) => {
        const response = await cache.match(key);
        const cachedAt = response?.headers.get('sw-cached-at') || '0';
        return { key, cachedAt: parseInt(cachedAt) };
      })
    );
    
    keysWithDates.sort((a, b) => a.cachedAt - b.cachedAt);
    
    const keysToDelete = keysWithDates.slice(0, keys.length - maxEntries);
    await Promise.all(keysToDelete.map(({ key }) => cache.delete(key)));
  }
}

// Helper functions
function isImageRequest(request) {
  const url = new URL(request.url);
  return /\.(png|jpg|jpeg|gif|webp|svg|ico)$/i.test(url.pathname);
}

function isStaticAsset(request) {
  const url = new URL(request.url);
  return /\.(js|css|woff|woff2|ttf|eot)$/i.test(url.pathname) ||
         url.pathname.startsWith('/assets/');
}

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

async function doBackgroundSync() {
  // Handle offline actions when back online
  console.log('Service Worker: Background sync triggered');
  
  // Process any queued actions
  // This would integrate with your offline action queue
}

// Push notifications
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json();
    
    const options = {
      body: data.body,
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-72x72.png',
      vibrate: [100, 50, 100],
      data: {
        dateOfArrival: Date.now(),
        primaryKey: data.primaryKey,
      },
      actions: [
        {
          action: 'explore',
          title: 'View Article',
          icon: '/icons/checkmark.png',
        },
        {
          action: 'close',
          title: 'Close',
          icon: '/icons/xmark.png',
        },
      ],
    };
    
    event.waitUntil(
      self.registration.showNotification(data.title, options)
    );
  }
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/articles/' + event.notification.data.primaryKey)
    );
  }
});

// Message handling for cache management
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'CACHE_URLS') {
    event.waitUntil(
      caches.open(DYNAMIC_CACHE).then((cache) => {
        return cache.addAll(event.data.payload);
      })
    );
  }
  
  if (event.data && event.data.type === 'CLEAR_CACHE') {
    event.waitUntil(
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => caches.delete(cacheName))
        );
      })
    );
  }
});
