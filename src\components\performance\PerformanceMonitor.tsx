// Performance Monitor Component for The Chronicle
// Real-time performance tracking and optimization recommendations

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Activity, 
  Zap, 
  Clock, 
  Eye, 
  Gauge, 
  TrendingUp, 
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Download,
  Optimize
} from 'lucide-react';
import { performanceMonitor } from '@/utils/performanceMonitoring';

interface PerformanceMetrics {
  webVitals: {
    lcp: number;
    fid: number;
    cls: number;
    fcp: number;
    ttfb: number;
    score: number;
    rating: 'good' | 'needs-improvement' | 'poor';
  };
  runtime: {
    memoryUsage: number;
    jsHeapSize: number;
    domNodes: number;
    eventListeners: number;
  };
  network: {
    connectionType: string;
    downlink: number;
    rtt: number;
    effectiveType: string;
  };
  resources: {
    totalSize: number;
    jsSize: number;
    cssSize: number;
    imageSize: number;
    loadTime: number;
  };
  recommendations: PerformanceRecommendation[];
}

interface PerformanceRecommendation {
  id: string;
  type: 'critical' | 'warning' | 'info';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  effort: 'easy' | 'medium' | 'hard';
  action?: () => void;
}

export function PerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  useEffect(() => {
    loadPerformanceMetrics();
    const interval = setInterval(loadPerformanceMetrics, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const loadPerformanceMetrics = useCallback(async () => {
    setIsLoading(true);
    try {
      const data = await gatherPerformanceMetrics();
      setMetrics(data);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to load performance metrics:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const optimizePerformance = useCallback(async () => {
    setIsOptimizing(true);
    try {
      await performOptimizations();
      await loadPerformanceMetrics();
    } catch (error) {
      console.error('Performance optimization failed:', error);
    } finally {
      setIsOptimizing(false);
    }
  }, [loadPerformanceMetrics]);

  const exportMetrics = useCallback(() => {
    if (!metrics) return;
    
    const data = {
      timestamp: new Date().toISOString(),
      metrics,
      userAgent: navigator.userAgent,
      url: window.location.href,
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `performance-metrics-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [metrics]);

  if (isLoading && !metrics) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!metrics) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-center text-gray-500">Failed to load performance metrics</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Performance Monitor</h2>
          <p className="text-gray-600">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={exportMetrics}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button 
            variant="outline" 
            onClick={optimizePerformance}
            disabled={isOptimizing}
          >
            <Optimize className={`w-4 h-4 mr-2 ${isOptimizing ? 'animate-spin' : ''}`} />
            Optimize
          </Button>
          <Button onClick={loadPerformanceMetrics} disabled={isLoading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Performance Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Gauge className="w-5 h-5 mr-2" />
            Performance Score
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Overall Score</span>
                <span className="text-2xl font-bold">{metrics.webVitals.score}</span>
              </div>
              <Progress value={metrics.webVitals.score} className="h-3" />
            </div>
            <Badge 
              variant={
                metrics.webVitals.rating === 'good' ? 'default' :
                metrics.webVitals.rating === 'needs-improvement' ? 'secondary' : 'destructive'
              }
            >
              {metrics.webVitals.rating.replace('-', ' ').toUpperCase()}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Metrics */}
      <Tabs defaultValue="vitals" className="space-y-4">
        <TabsList>
          <TabsTrigger value="vitals">Core Web Vitals</TabsTrigger>
          <TabsTrigger value="runtime">Runtime</TabsTrigger>
          <TabsTrigger value="network">Network</TabsTrigger>
          <TabsTrigger value="resources">Resources</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
        </TabsList>

        {/* Core Web Vitals */}
        <TabsContent value="vitals" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <MetricCard
              title="Largest Contentful Paint"
              value={`${metrics.webVitals.lcp.toFixed(0)}ms`}
              description="Loading performance"
              icon={<Clock className="w-4 h-4" />}
              status={getVitalStatus(metrics.webVitals.lcp, [2500, 4000])}
              target="< 2.5s"
            />
            <MetricCard
              title="First Input Delay"
              value={`${metrics.webVitals.fid.toFixed(0)}ms`}
              description="Interactivity"
              icon={<Zap className="w-4 h-4" />}
              status={getVitalStatus(metrics.webVitals.fid, [100, 300])}
              target="< 100ms"
            />
            <MetricCard
              title="Cumulative Layout Shift"
              value={metrics.webVitals.cls.toFixed(3)}
              description="Visual stability"
              icon={<Eye className="w-4 h-4" />}
              status={getVitalStatus(metrics.webVitals.cls, [0.1, 0.25])}
              target="< 0.1"
            />
            <MetricCard
              title="First Contentful Paint"
              value={`${metrics.webVitals.fcp.toFixed(0)}ms`}
              description="Rendering performance"
              icon={<Activity className="w-4 h-4" />}
              status={getVitalStatus(metrics.webVitals.fcp, [1800, 3000])}
              target="< 1.8s"
            />
            <MetricCard
              title="Time to First Byte"
              value={`${metrics.webVitals.ttfb.toFixed(0)}ms`}
              description="Server response"
              icon={<TrendingUp className="w-4 h-4" />}
              status={getVitalStatus(metrics.webVitals.ttfb, [800, 1800])}
              target="< 800ms"
            />
          </div>
        </TabsContent>

        {/* Runtime Metrics */}
        <TabsContent value="runtime" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Memory Usage</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>JS Heap Size</span>
                    <span>{formatBytes(metrics.runtime.jsHeapSize)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Memory Usage</span>
                    <span>{formatBytes(metrics.runtime.memoryUsage)}</span>
                  </div>
                  <Progress 
                    value={(metrics.runtime.memoryUsage / (50 * 1024 * 1024)) * 100} 
                    className="h-2" 
                  />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>DOM Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>DOM Nodes</span>
                    <span>{metrics.runtime.domNodes.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Event Listeners</span>
                    <span>{metrics.runtime.eventListeners.toLocaleString()}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Network Metrics */}
        <TabsContent value="network" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Network Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{metrics.network.connectionType}</div>
                  <div className="text-sm text-gray-600">Connection Type</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{metrics.network.downlink} Mbps</div>
                  <div className="text-sm text-gray-600">Downlink</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{metrics.network.rtt}ms</div>
                  <div className="text-sm text-gray-600">Round Trip Time</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{metrics.network.effectiveType}</div>
                  <div className="text-sm text-gray-600">Effective Type</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Resource Metrics */}
        <TabsContent value="resources" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Resource Sizes</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Total Size</span>
                    <span>{formatBytes(metrics.resources.totalSize)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>JavaScript</span>
                    <span>{formatBytes(metrics.resources.jsSize)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>CSS</span>
                    <span>{formatBytes(metrics.resources.cssSize)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Images</span>
                    <span>{formatBytes(metrics.resources.imageSize)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Load Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-3xl font-bold">{metrics.resources.loadTime.toFixed(2)}s</div>
                  <div className="text-sm text-gray-600">Total Load Time</div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Recommendations */}
        <TabsContent value="recommendations" className="space-y-4">
          <div className="space-y-3">
            {metrics.recommendations.map((recommendation) => (
              <RecommendationCard key={recommendation.id} recommendation={recommendation} />
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Helper Components

interface MetricCardProps {
  title: string;
  value: string;
  description: string;
  icon: React.ReactNode;
  status: 'good' | 'needs-improvement' | 'poor';
  target: string;
}

function MetricCard({ title, value, description, icon, status, target }: MetricCardProps) {
  const StatusIcon = status === 'good' ? CheckCircle : status === 'needs-improvement' ? AlertTriangle : TrendingDown;
  const statusColor = status === 'good' ? 'text-green-600' : status === 'needs-improvement' ? 'text-yellow-600' : 'text-red-600';

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium flex items-center">
          {icon}
          <span className="ml-2">{title}</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div>
            <div className="text-2xl font-bold">{value}</div>
            <div className="text-xs text-gray-600">{description}</div>
            <div className="text-xs text-gray-500">Target: {target}</div>
          </div>
          <StatusIcon className={`w-6 h-6 ${statusColor}`} />
        </div>
      </CardContent>
    </Card>
  );
}

interface RecommendationCardProps {
  recommendation: PerformanceRecommendation;
}

function RecommendationCard({ recommendation }: RecommendationCardProps) {
  const typeColors = {
    critical: 'border-red-500 bg-red-50',
    warning: 'border-yellow-500 bg-yellow-50',
    info: 'border-blue-500 bg-blue-50',
  };

  const impactColors = {
    high: 'bg-red-100 text-red-800',
    medium: 'bg-yellow-100 text-yellow-800',
    low: 'bg-green-100 text-green-800',
  };

  const effortColors = {
    easy: 'bg-green-100 text-green-800',
    medium: 'bg-yellow-100 text-yellow-800',
    hard: 'bg-red-100 text-red-800',
  };

  return (
    <Card className={`border-l-4 ${typeColors[recommendation.type]}`}>
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h4 className="font-semibold">{recommendation.title}</h4>
            <p className="text-sm text-gray-600 mt-1">{recommendation.description}</p>
            <div className="flex space-x-2 mt-2">
              <Badge className={impactColors[recommendation.impact]}>
                {recommendation.impact} impact
              </Badge>
              <Badge className={effortColors[recommendation.effort]}>
                {recommendation.effort} effort
              </Badge>
            </div>
          </div>
          {recommendation.action && (
            <Button size="sm" onClick={recommendation.action}>
              Fix
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Helper Functions

function getVitalStatus(value: number, thresholds: [number, number]): 'good' | 'needs-improvement' | 'poor' {
  if (value <= thresholds[0]) return 'good';
  if (value <= thresholds[1]) return 'needs-improvement';
  return 'poor';
}

function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Performance data gathering
async function gatherPerformanceMetrics(): Promise<PerformanceMetrics> {
  // This would integrate with your performance monitoring system
  // For now, return mock data
  return {
    webVitals: {
      lcp: 2100,
      fid: 85,
      cls: 0.08,
      fcp: 1600,
      ttfb: 650,
      score: 85,
      rating: 'good',
    },
    runtime: {
      memoryUsage: 25 * 1024 * 1024,
      jsHeapSize: 15 * 1024 * 1024,
      domNodes: 1250,
      eventListeners: 45,
    },
    network: {
      connectionType: '4g',
      downlink: 10,
      rtt: 100,
      effectiveType: '4g',
    },
    resources: {
      totalSize: 2.5 * 1024 * 1024,
      jsSize: 800 * 1024,
      cssSize: 150 * 1024,
      imageSize: 1.2 * 1024 * 1024,
      loadTime: 2.1,
    },
    recommendations: [
      {
        id: '1',
        type: 'warning',
        title: 'Optimize Images',
        description: 'Some images could be compressed further to reduce load time.',
        impact: 'medium',
        effort: 'easy',
      },
      {
        id: '2',
        type: 'info',
        title: 'Enable Text Compression',
        description: 'Enable gzip or brotli compression for text resources.',
        impact: 'low',
        effort: 'easy',
      },
    ],
  };
}

async function performOptimizations(): Promise<void> {
  // Implement performance optimizations
  console.log('Performing optimizations...');
  
  // Clear caches
  if ('caches' in window) {
    const cacheNames = await caches.keys();
    await Promise.all(cacheNames.map(name => caches.delete(name)));
  }
  
  // Force garbage collection if available
  if ('gc' in window) {
    (window as any).gc();
  }
}

export default PerformanceMonitor;
