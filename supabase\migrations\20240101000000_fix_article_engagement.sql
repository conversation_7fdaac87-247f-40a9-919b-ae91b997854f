-- Fix article_engagement table and RLS policies

-- Drop existing table if it exists
DROP TABLE IF EXISTS article_engagement CASCADE;

-- Create article_engagement table with proper structure
CREATE TABLE article_engagement (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  article_id UUID REFERENCES articles(id) ON DELETE CASCADE,
  liked BOOLEAN DEFAULT FALSE,
  bookmarked BOOLEAN DEFAULT FALSE,
  viewed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, article_id)
);

-- Enable RLS on article_engagement
ALTER TABLE article_engagement ENABLE ROW LEVEL SECURITY;

-- Create policies for article_engagement
CREATE POLICY "Users can view their own engagement" ON article_engagement
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own engagement" ON article_engagement
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own engagement" ON article_engagement
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own engagement" ON article_engagement
  FOR DELETE USING (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX idx_article_engagement_user_id ON article_engagement(user_id);
CREATE INDEX idx_article_engagement_article_id ON article_engagement(article_id);
CREATE INDEX idx_article_engagement_liked ON article_engagement(liked) WHERE liked = TRUE;
CREATE INDEX idx_article_engagement_bookmarked ON article_engagement(bookmarked) WHERE bookmarked = TRUE;

-- Create function to handle engagement upsert
CREATE OR REPLACE FUNCTION handle_article_engagement(
  p_article_id UUID,
  p_liked BOOLEAN DEFAULT NULL,
  p_bookmarked BOOLEAN DEFAULT NULL
)
RETURNS article_engagement
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result article_engagement;
BEGIN
  -- Insert or update engagement
  INSERT INTO article_engagement (user_id, article_id, liked, bookmarked)
  VALUES (auth.uid(), p_article_id, COALESCE(p_liked, FALSE), COALESCE(p_bookmarked, FALSE))
  ON CONFLICT (user_id, article_id)
  DO UPDATE SET
    liked = CASE WHEN p_liked IS NOT NULL THEN p_liked ELSE article_engagement.liked END,
    bookmarked = CASE WHEN p_bookmarked IS NOT NULL THEN p_bookmarked ELSE article_engagement.bookmarked END,
    updated_at = NOW()
  RETURNING * INTO result;
  
  RETURN result;
END;
$$;

-- Create function to get user engagement for an article
CREATE OR REPLACE FUNCTION get_article_engagement(p_article_id UUID)
RETURNS TABLE(liked BOOLEAN, bookmarked BOOLEAN)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COALESCE(ae.liked, FALSE) as liked,
    COALESCE(ae.bookmarked, FALSE) as bookmarked
  FROM article_engagement ae
  WHERE ae.user_id = auth.uid() AND ae.article_id = p_article_id
  UNION ALL
  SELECT FALSE, FALSE
  WHERE NOT EXISTS (
    SELECT 1 FROM article_engagement ae 
    WHERE ae.user_id = auth.uid() AND ae.article_id = p_article_id
  )
  LIMIT 1;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION handle_article_engagement TO authenticated;
GRANT EXECUTE ON FUNCTION get_article_engagement TO authenticated;
