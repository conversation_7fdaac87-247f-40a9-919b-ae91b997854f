import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Link, useNavigate } from "react-router-dom";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuth } from "../../../supabase/auth";
import { useCart } from "@/contexts/CartContext";
import HeroSection from "../blog/HeroSection";
import ArticleCard from "../blog/ArticleCard";
import ProductShowcase from "../ecommerce/ProductShowcase";
import NewsletterSignup from "../blog/NewsletterSignup";
import { Article } from "@/types/blog";
import { Product } from "@/types/ecommerce";
import {
  Search,
  Menu,
  Bell,
  User,
  Settings,
  ShoppingCart,
  Grid3X3,
  Newspaper,
  TrendingUp,
  Star,
  ArrowRight,
} from "lucide-react";

// Mock data - In a real app, this would come from your API
const featuredArticle: Article = {
  id: "1",
  title: "The Future of AI in Content Creation",
  slug: "future-ai-content-creation",
  excerpt: "How artificial intelligence is revolutionizing the way we create and consume content across industries.",
  content: "Full article content would go here...",
  author: {
    id: "1",
    name: "Alex Johnson",
    avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&q=80",
    bio: "Technology journalist and AI researcher"
  },
  category: {
    id: "1",
    name: "Technology",
    slug: "technology"
  },
  tags: ["AI", "Content", "Technology", "Innovation"],
  featuredImage: "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&q=80",
  isPremium: true,
  isPublished: true,
  publishedAt: new Date("2024-05-15"),
  createdAt: new Date("2024-05-15"),
  updatedAt: new Date("2024-05-15"),
  readTime: 5,
  views: 12450,
  likes: 245,
  comments: 56,
  shares: 89
};

const trendingArticles: Article[] = [
  {
    id: "2",
    title: "Sustainable Fashion: Beyond the Buzzword",
    slug: "sustainable-fashion-beyond-buzzword",
    excerpt: "Exploring what it truly means for fashion to be sustainable in today's global economy.",
    content: "Full article content...",
    author: {
      id: "2",
      name: "Mia Chen",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&q=80"
    },
    category: {
      id: "2",
      name: "Fashion",
      slug: "fashion"
    },
    tags: ["Fashion", "Sustainability", "Environment"],
    featuredImage: "https://images.unsplash.com/photo-1523381210434-271e8be1f52b?w=800&q=80",
    isPremium: false,
    isPublished: true,
    publishedAt: new Date("2024-05-14"),
    createdAt: new Date("2024-05-14"),
    updatedAt: new Date("2024-05-14"),
    readTime: 8,
    views: 8920,
    likes: 189,
    comments: 32,
    shares: 45
  },
  {
    id: "3",
    title: "The Science of Productivity",
    slug: "science-of-productivity",
    excerpt: "Research-backed strategies to maximize your efficiency and well-being.",
    content: "Full article content...",
    author: {
      id: "3",
      name: "David Park",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&q=80"
    },
    category: {
      id: "3",
      name: "Health",
      slug: "health"
    },
    tags: ["Productivity", "Health", "Science", "Wellness"],
    featuredImage: "https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=800&q=80",
    isPremium: true,
    isPublished: true,
    publishedAt: new Date("2024-05-13"),
    createdAt: new Date("2024-05-13"),
    updatedAt: new Date("2024-05-13"),
    readTime: 6,
    views: 15670,
    likes: 312,
    comments: 47,
    shares: 78
  },
  {
    id: "4",
    title: "Crypto Markets: Analysis and Predictions",
    slug: "crypto-markets-analysis-predictions",
    excerpt: "Expert insights on the current state of cryptocurrency and where it might be headed.",
    content: "Full article content...",
    author: {
      id: "4",
      name: "Sarah Williams",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&q=80"
    },
    category: {
      id: "4",
      name: "Finance",
      slug: "finance"
    },
    tags: ["Cryptocurrency", "Finance", "Investment", "Markets"],
    featuredImage: "https://images.unsplash.com/photo-1621761191319-c6fb62004040?w=800&q=80",
    isPremium: false,
    isPublished: true,
    publishedAt: new Date("2024-05-12"),
    createdAt: new Date("2024-05-12"),
    updatedAt: new Date("2024-05-12"),
    readTime: 10,
    views: 9340,
    likes: 276,
    comments: 89,
    shares: 134
  }
];

const featuredProducts: Product[] = [
  {
    id: "1",
    name: "Premium Annual Subscription",
    slug: "premium-annual-subscription",
    description: "Get unlimited access to all premium content for a full year. Includes exclusive articles, ad-free reading, and member-only events.",
    shortDescription: "Unlimited access to premium content for one year",
    price: 99.99,
    comparePrice: 119.99,
    type: "digital",
    category: {
      id: "1",
      name: "Subscriptions",
      slug: "subscriptions"
    },
    images: ["https://images.unsplash.com/photo-1586892477838-2b96e85e0f96?w=800&q=80"],
    stock: 999,
    status: "active",
    featured: true,
    tags: ["Premium", "Subscription", "Digital"],
    downloadUrl: "/download/subscription",
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2024-05-15")
  },
  {
    id: "2",
    name: "The Ultimate Productivity Journal",
    slug: "ultimate-productivity-journal",
    description: "A beautifully designed journal to track your goals, habits, and daily progress. Made with premium materials and thoughtful design.",
    shortDescription: "Premium journal for goal tracking and productivity",
    price: 29.99,
    type: "physical",
    category: {
      id: "2",
      name: "Stationery",
      slug: "stationery"
    },
    images: ["https://images.unsplash.com/photo-1531346878377-a5be20888e57?w=800&q=80"],
    stock: 45,
    status: "active",
    featured: true,
    tags: ["Productivity", "Journal", "Physical"],
    createdAt: new Date("2024-02-01"),
    updatedAt: new Date("2024-05-15")
  },
  {
    id: "3",
    name: "Digital Marketing Masterclass",
    slug: "digital-marketing-masterclass",
    description: "Learn the latest digital marketing strategies from industry experts. Includes video lessons, templates, and lifetime access.",
    shortDescription: "Complete digital marketing course with expert insights",
    price: 49.99,
    comparePrice: 79.99,
    type: "digital",
    category: {
      id: "3",
      name: "Courses",
      slug: "courses"
    },
    images: ["https://images.unsplash.com/photo-1432888498266-38ffec3eaf0a?w=800&q=80"],
    stock: 999,
    status: "active",
    featured: true,
    tags: ["Marketing", "Course", "Digital"],
    downloadUrl: "/download/marketing-course",
    createdAt: new Date("2024-03-01"),
    updatedAt: new Date("2024-05-15")
  }
];

export default function LandingPage() {
  const { user, signOut } = useAuth();
  const { addItem, getItemCount } = useCart();
  const navigate = useNavigate();
  const [wishlistItems, setWishlistItems] = useState<string[]>([]);

  const handleAddToCart = (productId: string) => {
    const product = featuredProducts.find(p => p.id === productId);
    if (product) {
      addItem(product);
    }
  };

  const handleToggleWishlist = (productId: string) => {
    setWishlistItems(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  return (
    <div className="min-h-screen bg-white text-black">
      {/* Navigation */}
      <header className="fixed top-0 z-50 w-full bg-white/95 backdrop-blur-md border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
          <div className="flex items-center">
            <Link to="/" className="font-bold text-2xl tracking-tight text-gray-900">
              The Chronicle
            </Link>
          </div>

          <nav className="hidden md:flex items-center space-x-8 text-sm font-medium">
            <Link to="/articles" className="text-gray-700 hover:text-blue-600 transition-colors">
              <Newspaper className="w-4 h-4 inline mr-1" />
              Articles
            </Link>
            <Link to="/categories" className="text-gray-700 hover:text-blue-600 transition-colors">
              <Grid3X3 className="w-4 h-4 inline mr-1" />
              Categories
            </Link>
            <Link to="/products" className="text-gray-700 hover:text-blue-600 transition-colors">
              <ShoppingCart className="w-4 h-4 inline mr-1" />
              Shop
            </Link>
          </nav>

          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm">
              <Search className="w-4 h-4" />
            </Button>

            {/* Shopping Cart */}
            <Button variant="ghost" size="sm" className="relative">
              <ShoppingCart className="w-4 h-4" />
              {getItemCount() > 0 && (
                <span className="absolute -top-1 -right-1 bg-blue-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {getItemCount()}
                </span>
              )}
            </Button>

            {user ? (
              <div className="flex items-center gap-3">
                <Button variant="ghost" size="sm">
                  <Bell className="w-4 h-4" />
                </Button>

                <Link to="/dashboard">
                  <Button variant="ghost" size="sm">
                    Dashboard
                  </Button>
                </Link>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Avatar className="h-8 w-8 cursor-pointer">
                      <AvatarImage
                        src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${user.email}`}
                        alt={user.email || ""}
                      />
                      <AvatarFallback>
                        {user.email?.[0].toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <DropdownMenuLabel className="text-xs text-gray-500">
                      {user.email}
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <User className="mr-2 h-4 w-4" />
                      Profile
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Settings className="mr-2 h-4 w-4" />
                      Settings
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onSelect={() => signOut()}>
                      Log out
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            ) : (
              <div className="flex items-center space-x-3">
                <Link to="/login">
                  <Button variant="ghost" size="sm">
                    Sign In
                  </Button>
                </Link>
                <Link to="/signup">
                  <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                    Subscribe
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </div>
      </header>

      <main className="pt-16">
        {/* Hero Section with Featured Article */}
        <HeroSection
          featuredArticle={featuredArticle}
          trendingArticles={trendingArticles.slice(0, 3)}
        />

        {/* Latest Articles Grid */}
        <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="flex justify-between items-center mb-12">
            <div className="flex items-center gap-3">
              <TrendingUp className="h-6 w-6 text-red-500" />
              <h2 className="text-3xl font-bold text-gray-900">Latest Articles</h2>
            </div>
            <Link
              to="/articles"
              className="flex items-center text-blue-600 hover:text-blue-700 font-medium transition-colors"
            >
              View all articles <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {trendingArticles.map((article, index) => (
              <ArticleCard
                key={article.id}
                article={article}
                variant={index === 0 ? "featured" : "default"}
              />
            ))}
          </div>
        </section>

        {/* Featured Products Showcase */}
        <ProductShowcase
          title="Featured Products"
          subtitle="Discover our curated selection of premium digital and physical products"
          products={featuredProducts}
          onAddToCart={handleAddToCart}
          onToggleWishlist={handleToggleWishlist}
          wishlistItems={wishlistItems}
        />

        {/* Newsletter Signup */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <NewsletterSignup />
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-[1200px] mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 pb-8 border-b border-gray-700">
            <div>
              <h3 className="font-bold text-xl mb-4">The Chronicle</h3>
              <p className="text-gray-400 mb-4">
                Delivering thought-provoking journalism and premium content to
                readers worldwide.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="text-gray-400 hover:text-white" aria-label="Follow us on Facebook">
                  <svg
                    className="h-6 w-6"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                    aria-hidden="true"
                  >
                    <path
                      fillRule="evenodd"
                      d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                      clipRule="evenodd"
                    />
                  </svg>
                </a>
                <a href="#" className="text-gray-400 hover:text-white" aria-label="Follow us on Twitter">
                  <svg
                    className="h-6 w-6"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                    aria-hidden="true"
                  >
                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                  </svg>
                </a>
                <a href="#" className="text-gray-400 hover:text-white" aria-label="Follow us on Instagram">
                  <svg
                    className="h-6 w-6"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                    aria-hidden="true"
                  >
                    <path
                      fillRule="evenodd"
                      d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                      clipRule="evenodd"
                    />
                  </svg>
                </a>
              </div>
            </div>
            <div>
              <h4 className="font-semibold text-lg mb-4">Categories</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link to="/" className="hover:text-white">
                    News
                  </Link>
                </li>
                <li>
                  <Link to="/" className="hover:text-white">
                    Technology
                  </Link>
                </li>
                <li>
                  <Link to="/" className="hover:text-white">
                    Business
                  </Link>
                </li>
                <li>
                  <Link to="/" className="hover:text-white">
                    Health
                  </Link>
                </li>
                <li>
                  <Link to="/" className="hover:text-white">
                    Culture
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-lg mb-4">Company</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link to="/" className="hover:text-white">
                    About Us
                  </Link>
                </li>
                <li>
                  <Link to="/" className="hover:text-white">
                    Careers
                  </Link>
                </li>
                <li>
                  <Link to="/" className="hover:text-white">
                    Contact
                  </Link>
                </li>
                <li>
                  <Link to="/" className="hover:text-white">
                    Press
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-lg mb-4">Legal</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link to="/" className="hover:text-white">
                    Terms of Service
                  </Link>
                </li>
                <li>
                  <Link to="/" className="hover:text-white">
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link to="/" className="hover:text-white">
                    Cookie Policy
                  </Link>
                </li>
                <li>
                  <Link to="/" className="hover:text-white">
                    Accessibility
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="pt-8 text-center text-gray-400">
            <p>© 2024 The Chronicle. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
