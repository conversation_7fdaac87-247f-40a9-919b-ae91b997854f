import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg";
  className?: string;
  text?: string;
}

export function LoadingSpinner({
  size = "md",
  className,
  text,
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "h-5 w-5",
    md: "h-8 w-8",
    lg: "h-12 w-12",
  };

  return (
    <div className="flex flex-col items-center justify-center">
      <div
        className={cn(
          "relative animate-spin rounded-full border-2 border-gray-200",
          "border-t-blue-500",
          sizeClasses[size],
          className
        )}
      />
      {text && (
        <p className="mt-3 text-sm font-medium text-gray-500">{text}</p>
      )}
    </div>
  );
}

interface LoadingScreenProps {
  text?: string;
  fullScreen?: boolean;
}

// Typewriter animation component
function TypewriterText({ text, speed = 100 }: { text: string; speed?: number }) {
  const [displayText, setDisplayText] = useState("");
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (currentIndex < text.length) {
      const timeout = setTimeout(() => {
        setDisplayText(prev => prev + text[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, speed);

      return () => clearTimeout(timeout);
    }
  }, [currentIndex, text, speed]);

  return (
    <span className="inline-block">
      {displayText}
      <span className="animate-pulse">|</span>
    </span>
  );
}

export function LoadingScreen({ text, fullScreen = false }: LoadingScreenProps) {
  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center bg-white p-8",
        fullScreen ? "fixed inset-0 z-50" : "h-screen w-full"
      )}
    >
      {/* Logo Animation */}
      <div className="relative mb-8">
        {/* Outer rotating ring */}
        <div className="h-24 w-24 rounded-full border-4 border-gray-200 border-t-blue-600 animate-spin" />

        {/* Inner pulsing circle */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="h-16 w-16 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 animate-pulse flex items-center justify-center">
            {/* Logo/Icon */}
            <svg
              className="h-8 w-8 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"
              />
            </svg>
          </div>
        </div>

        {/* Floating particles */}
        <div className="absolute -inset-4">
          <div className="h-2 w-2 bg-blue-400 rounded-full animate-ping absolute top-0 left-1/2 transform -translate-x-1/2" style={{ animationDelay: '0s' }} />
          <div className="h-2 w-2 bg-purple-400 rounded-full animate-ping absolute bottom-0 right-0" style={{ animationDelay: '0.5s' }} />
          <div className="h-2 w-2 bg-indigo-400 rounded-full animate-ping absolute top-1/2 left-0 transform -translate-y-1/2" style={{ animationDelay: '1s' }} />
        </div>
      </div>

      {/* Site Title with Typewriter Effect */}
      <div className="text-center mb-4">
        <h1 className="text-4xl font-bold text-gray-900 mb-2">
          <TypewriterText text="The Chronicle" speed={150} />
        </h1>
        <p className="text-lg text-gray-600">
          <TypewriterText text="Premium Content Platform" speed={80} />
        </p>
      </div>

      {/* Loading Text */}
      {text && (
        <div className="mt-6 text-center">
          <p className="text-base font-medium text-gray-700 animate-pulse">
            {text}
          </p>
          <div className="flex justify-center mt-3">
            <div className="flex space-x-1">
              <div className="h-2 w-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0s' }} />
              <div className="h-2 w-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
              <div className="h-2 w-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export function LoadingDots({ className }: { className?: string }) {
  return (
    <div className={cn("flex space-x-1.5", className)}>
      <div className="h-2 w-2 rounded-full bg-gray-400 animate-bounce [animation-delay:-0.3s]" />
      <div className="h-2 w-2 rounded-full bg-gray-400 animate-bounce [animation-delay:-0.15s]" />
      <div className="h-2 w-2 rounded-full bg-gray-400 animate-bounce" />
    </div>
  );
}

export function LoadingPulse({ className }: { className?: string }) {
  return (
    <div className="flex justify-center">
      <div className={cn("relative", className)}>
        <div className="h-12 w-12 rounded-full border-2 border-gray-200" />
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="h-6 w-6 rounded-full bg-blue-500 animate-ping opacity-75" />
        </div>
      </div>
    </div>
  );
}
