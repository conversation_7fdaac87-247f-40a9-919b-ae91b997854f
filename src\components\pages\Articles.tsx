import React, { useState, useEffect } from "react";
import { Link, useSearchParams } from "react-router-dom";
import { useSEO } from "@/hooks/useSEO";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Navbar } from "@/components/layout/Navbar";
import { Footer } from "@/components/layout/Footer";
import { ScrollToTop } from "@/components/ui/scroll-to-top";
import {
  Search,
  Calendar,
  User,
  Eye,
  Heart,
  MessageSquare,
  BookOpen,
  Filter,
  TrendingUp,
  Clock,
  FileText,
  SlidersHorizontal,
  Star,
  X,
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useDebounce } from "@/hooks/useDebounce";
import { supabase } from "../../../supabase/supabase";

interface Article {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  featured_image: string | null;
  is_premium: boolean;
  views: number;
  likes: number;
  comments_count: number;
  read_time: number;
  published_at: string;
  author: {
    email: string;
  };
  category: {
    name: string;
    color: string;
  } | null;
}

interface Category {
  id: string;
  name: string;
  slug: string;
  color: string;
}

export function Articles() {
  const [searchParams, setSearchParams] = useSearchParams();
  const { toast } = useToast();
  const [articles, setArticles] = useState<Article[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState(
    searchParams.get("search") || ""
  );
  const [selectedCategory, setSelectedCategory] = useState(
    searchParams.get("category") || ""
  );
  const [sortBy, setSortBy] = useState<"latest" | "popular" | "trending">(
    "latest"
  );
  const [showFilters, setShowFilters] = useState(false);
  const [selectedAuthors, setSelectedAuthors] = useState<string[]>([]);
  const [selectedReadTime, setSelectedReadTime] = useState<string>("all");
  const [isPremiumOnly, setIsPremiumOnly] = useState(false);
  const [minViews, setMinViews] = useState<number>(0);
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // SEO for articles page
  useSEO({
    title: selectedCategory
      ? `${
          selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)
        } Articles`
      : "Articles",
    description: selectedCategory
      ? `Discover the latest ${selectedCategory} articles and insights from The Chronicle. Stay informed with our expert analysis and in-depth reporting.`
      : "Explore our comprehensive collection of articles covering technology, business, lifestyle, and more. Quality journalism and expert insights from The Chronicle.",
    keywords: [
      "articles",
      "news",
      "journalism",
      "blog",
      "insights",
      "analysis",
      selectedCategory || "general",
      "technology",
      "business",
      "lifestyle",
    ],
    type: "website",
  });

  useEffect(() => {
    loadArticles();
    loadCategories();
  }, [selectedCategory, sortBy, debouncedSearchTerm]);

  useEffect(() => {
    const params = new URLSearchParams();
    if (debouncedSearchTerm) params.set("search", debouncedSearchTerm);
    if (selectedCategory) params.set("category", selectedCategory);
    setSearchParams(params);
  }, [debouncedSearchTerm, selectedCategory, setSearchParams]);

  const loadArticles = async () => {
    try {
      setIsLoading(true);

      // Try to fetch articles with category relationship first
      let query = supabase
        .from("articles")
        .select(
          `
          *,
          category:categories(name, color)
        `
        )
        .eq("is_published", true);

      let { data, error } = await query;

      // If the relationship doesn't exist, fetch without category
      if (error && error.code === "PGRST200") {
        query = supabase.from("articles").select("*").eq("is_published", true);

        const result = await query;
        data = result.data;
        error = result.error;
      }

      // Apply sorting on client side if we have data
      if (!error && data) {
        // We already have the data, so we'll sort on the client side
        // In a real app, you'd want to do this on the server side for better performance
      }

      if (error) throw error;

      // Add fallback author and category data, then filter
      let articlesWithMockData = (data || []).map((article) => ({
        ...article,
        author: article.author || { email: "<EMAIL>" },
        category: article.category || {
          name: selectedCategory
            ? selectedCategory.charAt(0).toUpperCase() +
              selectedCategory.slice(1)
            : "Technology",
          color: "#3B82F6",
        },
      }));

      // Filter by category on client side
      if (selectedCategory) {
        articlesWithMockData = articlesWithMockData.filter(
          (article) =>
            article.category_slug === selectedCategory ||
            article.title.toLowerCase().includes(selectedCategory.toLowerCase())
        );
      }

      // Filter by search term on client side
      if (debouncedSearchTerm) {
        articlesWithMockData = articlesWithMockData.filter(
          (article) =>
            article.title
              .toLowerCase()
              .includes(debouncedSearchTerm.toLowerCase()) ||
            article.excerpt
              ?.toLowerCase()
              .includes(debouncedSearchTerm.toLowerCase())
        );
      }

      setArticles(articlesWithMockData);
    } catch (error) {
      console.error("Error loading articles:", error);
      // Use mock data as fallback
      const mockArticles = [
        {
          id: "1",
          title: "The Future of Web Development",
          slug: "future-of-web-development",
          excerpt:
            "Exploring the latest trends and technologies shaping the future of web development.",
          featured_image:
            "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=500",
          published_at: new Date().toISOString(),
          read_time: 5,
          views: 1250,
          likes: 89,
          comments_count: 23,
          is_premium: false,
          author: { email: "<EMAIL>" },
          category: { name: "Technology", color: "#3B82F6" },
        },
        {
          id: "2",
          title: "Building Scalable Applications",
          slug: "building-scalable-applications",
          excerpt:
            "Learn the best practices for building applications that can scale with your business.",
          featured_image:
            "https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=500",
          published_at: new Date(Date.now() - 86400000).toISOString(),
          read_time: 8,
          views: 890,
          likes: 67,
          comments_count: 15,
          is_premium: true,
          author: { email: "<EMAIL>" },
          category: { name: "Development", color: "#10B981" },
        },
      ];

      // Apply filters to mock data
      let filteredMockData = mockArticles;

      if (selectedCategory) {
        filteredMockData = filteredMockData.filter((article) =>
          article.category.name
            .toLowerCase()
            .includes(selectedCategory.toLowerCase())
        );
      }

      if (debouncedSearchTerm) {
        filteredMockData = filteredMockData.filter(
          (article) =>
            article.title
              .toLowerCase()
              .includes(debouncedSearchTerm.toLowerCase()) ||
            article.excerpt
              .toLowerCase()
              .includes(debouncedSearchTerm.toLowerCase())
        );
      }

      setArticles(filteredMockData);

      toast({
        title: "Using Demo Data",
        description: "Database connection failed, showing demo articles",
        variant: "default",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const { data, error } = await supabase
        .from("categories")
        .select("*")
        .order("name");

      if (error) throw error;

      setCategories(data || []);
    } catch (error) {
      console.error("Error loading categories:", error);
      // Use mock categories as fallback
      const mockCategories = [
        { id: "1", name: "Technology", slug: "technology", color: "#3B82F6" },
        { id: "2", name: "Business", slug: "business", color: "#10B981" },
        { id: "3", name: "Design", slug: "design", color: "#F59E0B" },
        { id: "4", name: "Development", slug: "development", color: "#8B5CF6" },
        { id: "5", name: "Marketing", slug: "marketing", color: "#EF4444" },
      ];
      setCategories(mockCategories);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(9)].map((_, i) => (
                <div key={i} className="h-80 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Latest Articles
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Discover insights, stories, and knowledge from our community of
            writers
          </p>
        </div>

        {/* Search Bar */}
        <div className="mb-8">
          <div className="flex gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search articles... (live search)"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="lg:hidden"
            >
              <SlidersHorizontal className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </div>
        </div>

        {/* Main Content with Sidebar */}
        <div className="flex gap-8">
          {/* Filters Sidebar */}
          <div
            className={`w-80 flex-shrink-0 ${
              showFilters ? "block" : "hidden lg:block"
            }`}
          >
            <Card className="sticky top-4">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold">Filters</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setSelectedCategory("");
                      setSelectedAuthors([]);
                      setSelectedReadTime("all");
                      setIsPremiumOnly(false);
                      setMinViews(0);
                    }}
                  >
                    Clear All
                  </Button>
                </div>

                <div className="space-y-6">
                  {/* Categories */}
                  <div>
                    <h4 className="font-medium mb-3">Categories</h4>
                    <div className="space-y-2">
                      <Button
                        variant={selectedCategory === "" ? "default" : "ghost"}
                        size="sm"
                        className="w-full justify-start"
                        onClick={() => setSelectedCategory("")}
                      >
                        All Categories
                      </Button>
                      {categories.map((category) => (
                        <Button
                          key={category.id}
                          variant={
                            selectedCategory === category.slug
                              ? "default"
                              : "ghost"
                          }
                          size="sm"
                          className="w-full justify-start"
                          onClick={() => setSelectedCategory(category.slug)}
                          style={{
                            backgroundColor:
                              selectedCategory === category.slug
                                ? category.color
                                : undefined,
                          }}
                        >
                          <div
                            className="w-3 h-3 rounded-full mr-2"
                            style={{ backgroundColor: category.color }}
                          ></div>
                          {category.name}
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* Authors */}
                  <div>
                    <h4 className="font-medium mb-3">Authors</h4>
                    <div className="space-y-2">
                      {[
                        "John Doe",
                        "Sarah Smith",
                        "Mike Johnson",
                        "Emily Davis",
                        "Alex Wilson",
                      ].map((author) => (
                        <label
                          key={author}
                          className="flex items-center space-x-2 cursor-pointer"
                        >
                          <input
                            type="checkbox"
                            checked={selectedAuthors.includes(author)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedAuthors([
                                  ...selectedAuthors,
                                  author,
                                ]);
                              } else {
                                setSelectedAuthors(
                                  selectedAuthors.filter((a) => a !== author)
                                );
                              }
                            }}
                            className="rounded border-gray-300"
                          />
                          <span className="text-sm">{author}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Read Time */}
                  <div>
                    <h4 className="font-medium mb-3">Reading Time</h4>
                    <div className="space-y-2">
                      {[
                        { value: "all", label: "Any length" },
                        { value: "quick", label: "< 5 min" },
                        { value: "medium", label: "5-10 min" },
                        { value: "long", label: "> 10 min" },
                      ].map((time) => (
                        <Button
                          key={time.value}
                          variant={
                            selectedReadTime === time.value
                              ? "default"
                              : "ghost"
                          }
                          size="sm"
                          className="w-full justify-start"
                          onClick={() => setSelectedReadTime(time.value)}
                        >
                          <Clock className="h-4 w-4 mr-2" />
                          {time.label}
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* Premium Content */}
                  <div>
                    <h4 className="font-medium mb-3">Content Type</h4>
                    <label className="flex items-center space-x-2 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={isPremiumOnly}
                        onChange={(e) => setIsPremiumOnly(e.target.checked)}
                        className="rounded border-gray-300"
                      />
                      <span className="text-sm flex items-center">
                        <Star className="h-3 w-3 mr-1 text-yellow-500" />
                        Premium Only
                      </span>
                    </label>
                  </div>

                  {/* Minimum Views */}
                  <div>
                    <h4 className="font-medium mb-3">Popularity</h4>
                    <div className="space-y-2">
                      {[
                        { value: 0, label: "Any views" },
                        { value: 100, label: "100+ views" },
                        { value: 500, label: "500+ views" },
                        { value: 1000, label: "1000+ views" },
                      ].map((view) => (
                        <Button
                          key={view.value}
                          variant={
                            minViews === view.value ? "default" : "ghost"
                          }
                          size="sm"
                          className="w-full justify-start"
                          onClick={() => setMinViews(view.value)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          {view.label}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Articles Content */}
          <div className="flex-1">
            {/* Sort Options */}
            <div className="flex items-center justify-between mb-6">
              <p className="text-sm text-gray-600">
                Showing {articles.length} articles
              </p>
              <div className="flex gap-2">
                <Button
                  variant={sortBy === "latest" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSortBy("latest")}
                >
                  <Clock className="h-4 w-4 mr-1" />
                  Latest
                </Button>
                <Button
                  variant={sortBy === "popular" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSortBy("popular")}
                >
                  <Eye className="h-4 w-4 mr-1" />
                  Popular
                </Button>
                <Button
                  variant={sortBy === "trending" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSortBy("trending")}
                >
                  <TrendingUp className="h-4 w-4 mr-1" />
                  Trending
                </Button>
              </div>
            </div>

            {/* Articles Grid */}
            {articles.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {articles.map((article) => (
                  <Card
                    key={article.id}
                    className="group hover:shadow-2xl transition-all duration-500 h-full border-0 shadow-lg overflow-hidden bg-white"
                  >
                    <div className="relative overflow-hidden">
                      {article.featured_image ? (
                        <img
                          src={article.featured_image}
                          alt={article.title}
                          className="w-full h-56 object-cover group-hover:scale-110 transition-transform duration-700"
                        />
                      ) : (
                        <div className="w-full h-56 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center">
                          <FileText className="h-20 w-20 text-blue-400" />
                        </div>
                      )}

                      {/* Overlay */}
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-500" />

                      {/* Badges */}
                      <div className="absolute top-4 left-4 flex flex-col gap-2">
                        {article.category && (
                          <Badge
                            className="text-white font-medium shadow-lg text-xs px-3 py-1"
                            style={{ backgroundColor: article.category.color }}
                          >
                            {article.category.name}
                          </Badge>
                        )}
                        {article.is_premium && (
                          <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white font-medium shadow-lg text-xs px-3 py-1">
                            ⭐ Premium
                          </Badge>
                        )}
                      </div>

                      {/* Reading Time Badge */}
                      <div className="absolute top-4 right-4">
                        <Badge
                          variant="secondary"
                          className="bg-white/90 text-gray-700 font-medium"
                        >
                          {article.read_time || 5} min read
                        </Badge>
                      </div>
                    </div>

                    <CardContent className="p-6 flex flex-col h-full">
                      <div className="flex-1 space-y-4">
                        {/* Title */}
                        <h3 className="font-bold text-xl line-clamp-2 group-hover:text-blue-600 transition-colors leading-tight">
                          {article.title}
                        </h3>

                        {/* Excerpt */}
                        <p className="text-gray-600 text-sm line-clamp-3 leading-relaxed">
                          {article.excerpt}
                        </p>

                        {/* Author Info */}
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <User className="h-4 w-4" />
                          <span className="font-medium">
                            {article.author?.email?.split("@")[0] || "Author"}
                          </span>
                          <span>•</span>
                          <Calendar className="h-4 w-4" />
                          <span>
                            {new Date(
                              article.published_at || Date.now()
                            ).toLocaleDateString()}
                          </span>
                        </div>
                      </div>

                      {/* Stats and Actions */}
                      <div className="space-y-4 mt-6 pt-4 border-t border-gray-100">
                        {/* Stats */}
                        <div className="flex items-center justify-between text-sm text-gray-500">
                          <div className="flex items-center gap-4">
                            <span className="flex items-center gap-1">
                              <Eye className="h-4 w-4" />
                              {article.views || 0}
                            </span>
                            <span className="flex items-center gap-1">
                              <Heart className="h-4 w-4" />
                              {article.likes || 0}
                            </span>
                            <span className="flex items-center gap-1">
                              <MessageSquare className="h-4 w-4" />
                              {article.comments_count || 0}
                            </span>
                          </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1 hover:bg-red-50 hover:border-red-300 hover:text-red-600 transition-all duration-300"
                          >
                            <Heart className="h-3 w-3 mr-1" />
                            Save
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1 hover:bg-green-50 hover:border-green-300 hover:text-green-600 transition-all duration-300"
                          >
                            <MessageSquare className="h-3 w-3 mr-1" />
                            Share
                          </Button>
                        </div>

                        {/* Read Button */}
                        <Link
                          to={`/articles/${article.slug}`}
                          className="block"
                        >
                          <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-300">
                            <FileText className="h-4 w-4 mr-2" />
                            Read Article
                          </Button>
                        </Link>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Search className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-gray-900 mb-2">
                  {searchTerm || selectedCategory
                    ? "No articles found"
                    : "No articles available"}
                </h3>
                <p className="text-gray-500 mb-6">
                  {searchTerm || selectedCategory
                    ? "Try adjusting your search criteria or browse all articles."
                    : "Check back later for new content."}
                </p>
                {(searchTerm || selectedCategory) && (
                  <Button
                    onClick={() => {
                      setSearchTerm("");
                      setSelectedCategory("");
                    }}
                  >
                    Clear Filters
                  </Button>
                )}
              </div>
            )}

            {/* Load More Button */}
            {articles.length > 0 && articles.length % 9 === 0 && (
              <div className="text-center mt-12">
                <Button variant="outline" size="lg">
                  Load More Articles
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      <Footer />
      <ScrollToTop />
    </div>
  );
}
