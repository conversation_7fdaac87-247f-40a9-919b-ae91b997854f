import React, { useState, useEffect } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Navbar } from '@/components/layout/Navbar';
import {
  Search,
  Calendar,
  User,
  Eye,
  Heart,
  MessageSquare,
  BookOpen,
  Filter,
  TrendingUp,
  Clock,
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useDebounce } from '@/hooks/useDebounce';
import { supabase } from '../../../supabase/supabase';

interface Article {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  featured_image: string | null;
  is_premium: boolean;
  views: number;
  likes: number;
  comments_count: number;
  read_time: number;
  published_at: string;
  author: {
    email: string;
  };
  category: {
    name: string;
    color: string;
  } | null;
}

interface Category {
  id: string;
  name: string;
  slug: string;
  color: string;
}

export function Articles() {
  const [searchParams, setSearchParams] = useSearchParams();
  const { toast } = useToast();
  const [articles, setArticles] = useState<Article[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || '');
  const [selectedCategory, setSelectedCategory] = useState(searchParams.get('category') || '');
  const [sortBy, setSortBy] = useState<'latest' | 'popular' | 'trending'>('latest');
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  useEffect(() => {
    loadArticles();
    loadCategories();
  }, [selectedCategory, sortBy, debouncedSearchTerm]);

  useEffect(() => {
    const params = new URLSearchParams();
    if (debouncedSearchTerm) params.set('search', debouncedSearchTerm);
    if (selectedCategory) params.set('category', selectedCategory);
    setSearchParams(params);
  }, [debouncedSearchTerm, selectedCategory, setSearchParams]);

  const loadArticles = async () => {
    try {
      setIsLoading(true);
      
      let query = supabase
        .from('articles')
        .select(`
          *,
          author:users(email),
          category:categories(name, color)
        `)
        .eq('is_published', true);

      // Filter by category
      if (selectedCategory) {
        query = query.eq('category.slug', selectedCategory);
      }

      // Sort articles
      switch (sortBy) {
        case 'popular':
          query = query.order('views', { ascending: false });
          break;
        case 'trending':
          query = query.order('likes', { ascending: false });
          break;
        default:
          query = query.order('published_at', { ascending: false });
      }

      const { data, error } = await query;

      if (error) throw error;

      // Filter by search term on client side
      let filteredArticles = data || [];
      if (debouncedSearchTerm) {
        filteredArticles = filteredArticles.filter(article =>
          article.title.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
          article.excerpt?.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
        );
      }

      setArticles(filteredArticles);
    } catch (error) {
      console.error('Error loading articles:', error);
      toast({
        title: 'Error',
        description: 'Failed to load articles',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('name');

      if (error) throw error;

      setCategories(data || []);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };



  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(9)].map((_, i) => (
                <div key={i} className="h-80 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Latest Articles
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Discover insights, stories, and knowledge from our community of writers
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          <div className="flex gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search articles... (live search)"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div className="flex flex-wrap gap-4 items-center">
            {/* Categories */}
            <div className="flex flex-wrap gap-2">
              <Button
                variant={selectedCategory === '' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory('')}
              >
                All Categories
              </Button>
              {categories.map((category) => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.slug ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedCategory(category.slug)}
                  style={{
                    borderColor: selectedCategory === category.slug ? category.color : undefined,
                    backgroundColor: selectedCategory === category.slug ? category.color : undefined,
                  }}
                >
                  {category.name}
                </Button>
              ))}
            </div>

            {/* Sort Options */}
            <div className="flex gap-2 ml-auto">
              <Button
                variant={sortBy === 'latest' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSortBy('latest')}
              >
                <Clock className="h-4 w-4 mr-1" />
                Latest
              </Button>
              <Button
                variant={sortBy === 'popular' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSortBy('popular')}
              >
                <Eye className="h-4 w-4 mr-1" />
                Popular
              </Button>
              <Button
                variant={sortBy === 'trending' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSortBy('trending')}
              >
                <TrendingUp className="h-4 w-4 mr-1" />
                Trending
              </Button>
            </div>
          </div>
        </div>

        {/* Articles Grid */}
        {articles.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {articles.map((article) => (
              <Card key={article.id} className="group hover:shadow-lg transition-all duration-300 h-full">
                <div className="relative">
                  {article.featured_image && (
                    <img
                      src={article.featured_image}
                      alt={article.title}
                      className="w-full h-48 object-cover rounded-t-lg"
                    />
                  )}
                  {article.is_premium && (
                    <Badge className="absolute top-3 right-3 bg-yellow-500 text-white">
                      Premium
                    </Badge>
                  )}
                </div>
                
                <CardContent className="p-6 flex flex-col h-full">
                  <div className="flex-1">
                    {/* Category */}
                    {article.category && (
                      <Badge 
                        variant="outline" 
                        style={{ borderColor: article.category.color }}
                        className="mb-3"
                      >
                        {article.category.name}
                      </Badge>
                    )}

                    {/* Title */}
                    <h3 className="font-bold text-xl mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors">
                      {article.title}
                    </h3>

                    {/* Excerpt */}
                    <p className="text-gray-600 mb-4 line-clamp-3">
                      {article.excerpt}
                    </p>
                  </div>

                  {/* Meta Info */}
                  <div className="space-y-3 mt-auto">
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center gap-4">
                        <span className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {article.author?.email?.split('@')[0]}
                        </span>
                        <span className="flex items-center gap-1">
                          <BookOpen className="h-3 w-3" />
                          {article.read_time} min
                        </span>
                      </div>
                      <span className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {new Date(article.published_at).toLocaleDateString()}
                      </span>
                    </div>

                    {/* Stats */}
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center gap-4">
                        <span className="flex items-center gap-1">
                          <Eye className="h-3 w-3" />
                          {article.views}
                        </span>
                        <span className="flex items-center gap-1">
                          <Heart className="h-3 w-3" />
                          {article.likes}
                        </span>
                        <span className="flex items-center gap-1">
                          <MessageSquare className="h-3 w-3" />
                          {article.comments_count}
                        </span>
                      </div>
                    </div>

                    {/* Read Button */}
                    <Link to={`/articles/${article.slug}`} className="block">
                      <Button className="w-full mt-3">
                        Read Article
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Search className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">
              {searchTerm || selectedCategory ? 'No articles found' : 'No articles available'}
            </h3>
            <p className="text-gray-500 mb-6">
              {searchTerm || selectedCategory 
                ? 'Try adjusting your search criteria or browse all articles.'
                : 'Check back later for new content.'
              }
            </p>
            {(searchTerm || selectedCategory) && (
              <Button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedCategory('');
                }}
              >
                Clear Filters
              </Button>
            )}
          </div>
        )}

        {/* Load More Button */}
        {articles.length > 0 && articles.length % 9 === 0 && (
          <div className="text-center mt-12">
            <Button variant="outline" size="lg">
              Load More Articles
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
