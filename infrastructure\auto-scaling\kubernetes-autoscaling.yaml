# Kubernetes Auto-scaling Configuration for The Chronicle
# Horizontal Pod Autoscaler (HPA) and Vertical Pod Autoscaler (VPA) setup

apiVersion: v1
kind: Namespace
metadata:
  name: thechronicle
  labels:
    name: thechronicle
    environment: production

---
# ConfigMap for application configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: thechronicle
data:
  NODE_ENV: "production"
  PORT: "3000"
  HEALTH_CHECK_PATH: "/health"
  METRICS_PATH: "/metrics"

---
# Secret for sensitive configuration
apiVersion: v1
kind: Secret
metadata:
  name: app-secrets
  namespace: thechronicle
type: Opaque
stringData:
  SUPABASE_URL: "https://your-project.supabase.co"
  SUPABASE_ANON_KEY: "your_supabase_anon_key"
  STRIPE_PUBLISHABLE_KEY: "pk_live_your_stripe_key"
  GA_MEASUREMENT_ID: "G-XXXXXXXXXX"
  SENTRY_DSN: "https://your-sentry-dsn"

---
# Deployment for the main application
apiVersion: apps/v1
kind: Deployment
metadata:
  name: thechronicle-app
  namespace: thechronicle
  labels:
    app: thechronicle-app
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: thechronicle-app
  template:
    metadata:
      labels:
        app: thechronicle-app
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3000"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: app
        image: thechronicle/app:latest
        ports:
        - containerPort: 3000
          name: http
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: NODE_ENV
        - name: PORT
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: PORT
        - name: VITE_SUPABASE_URL
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: SUPABASE_URL
        - name: VITE_SUPABASE_ANON_KEY
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: SUPABASE_ANON_KEY
        - name: VITE_STRIPE_PUBLISHABLE_KEY
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: STRIPE_PUBLISHABLE_KEY
        - name: VITE_GA_MEASUREMENT_ID
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: GA_MEASUREMENT_ID
        - name: VITE_SENTRY_DSN
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: SENTRY_DSN
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        lifecycle:
          preStop:
            exec:
              command: ["/bin/sh", "-c", "sleep 15"]
      terminationGracePeriodSeconds: 30
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - thechronicle-app
              topologyKey: kubernetes.io/hostname

---
# Service for the application
apiVersion: v1
kind: Service
metadata:
  name: thechronicle-app-service
  namespace: thechronicle
  labels:
    app: thechronicle-app
spec:
  selector:
    app: thechronicle-app
  ports:
  - name: http
    port: 80
    targetPort: 3000
    protocol: TCP
  type: ClusterIP

---
# Horizontal Pod Autoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: thechronicle-app-hpa
  namespace: thechronicle
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: thechronicle-app
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: http_requests_per_second
      target:
        type: AverageValue
        averageValue: "100"
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Min
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 4
        periodSeconds: 60
      selectPolicy: Max

---
# Vertical Pod Autoscaler
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: thechronicle-app-vpa
  namespace: thechronicle
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: thechronicle-app
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: app
      minAllowed:
        cpu: 100m
        memory: 128Mi
      maxAllowed:
        cpu: 1000m
        memory: 1Gi
      controlledResources: ["cpu", "memory"]

---
# Pod Disruption Budget
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: thechronicle-app-pdb
  namespace: thechronicle
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: thechronicle-app

---
# Network Policy for security
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: thechronicle-app-netpol
  namespace: thechronicle
spec:
  podSelector:
    matchLabels:
      app: thechronicle-app
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 3000
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
    - protocol: UDP
      port: 53

---
# ServiceMonitor for Prometheus
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: thechronicle-app-metrics
  namespace: thechronicle
  labels:
    app: thechronicle-app
spec:
  selector:
    matchLabels:
      app: thechronicle-app
  endpoints:
  - port: http
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s

---
# Ingress for external access
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: thechronicle-app-ingress
  namespace: thechronicle
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://thechronicle.com"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - thechronicle.com
    - www.thechronicle.com
    secretName: thechronicle-tls
  rules:
  - host: thechronicle.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: thechronicle-app-service
            port:
              number: 80
  - host: www.thechronicle.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: thechronicle-app-service
            port:
              number: 80
