-- Fix admin system to work without admin API

-- Create admin_users view that works with regular API
CREATE OR REPLACE VIEW admin_users AS
SELECT 
  p.id,
  p.email,
  p.first_name,
  p.last_name,
  p.avatar_url,
  p.role,
  p.created_at,
  p.updated_at,
  p.last_sign_in_at,
  -- Calculate user stats
  (SELECT COUNT(*) FROM articles WHERE author_id = p.id) as article_count,
  (SELECT COUNT(*) FROM comments WHERE user_id = p.id) as comment_count,
  (SELECT COUNT(*) FROM orders WHERE user_id = p.id) as order_count
FROM profiles p
ORDER BY p.created_at DESC;

-- Enable RLS on the view
ALTER VIEW admin_users SET (security_barrier = true);

-- Create policy for admin_users view
CREATE POLICY "Ad<PERSON> can view all users" ON admin_users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- <PERSON>reate function to get user statistics
CREATE OR REPLACE FUNCTION get_user_stats()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
BEGIN
  -- Check if user is admin
  IF NOT EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() AND role = 'admin'
  ) THEN
    RAISE EXCEPTION 'Access denied. Admin role required.';
  END IF;

  SELECT json_build_object(
    'total_users', (SELECT COUNT(*) FROM profiles),
    'new_users_today', (SELECT COUNT(*) FROM profiles WHERE created_at >= CURRENT_DATE),
    'new_users_week', (SELECT COUNT(*) FROM profiles WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'),
    'new_users_month', (SELECT COUNT(*) FROM profiles WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'),
    'admin_users', (SELECT COUNT(*) FROM profiles WHERE role = 'admin'),
    'author_users', (SELECT COUNT(*) FROM profiles WHERE role = 'author'),
    'regular_users', (SELECT COUNT(*) FROM profiles WHERE role = 'user'),
    'total_articles', (SELECT COUNT(*) FROM articles),
    'published_articles', (SELECT COUNT(*) FROM articles WHERE is_published = true),
    'total_orders', (SELECT COUNT(*) FROM orders),
    'total_revenue', (SELECT COALESCE(SUM(total), 0) FROM orders WHERE status = 'completed')
  ) INTO result;

  RETURN result;
END;
$$;

-- Create function to update user role
CREATE OR REPLACE FUNCTION update_user_role(
  target_user_id UUID,
  new_role TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if current user is admin
  IF NOT EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() AND role = 'admin'
  ) THEN
    RAISE EXCEPTION 'Access denied. Admin role required.';
  END IF;

  -- Validate role
  IF new_role NOT IN ('user', 'author', 'admin') THEN
    RAISE EXCEPTION 'Invalid role. Must be user, author, or admin.';
  END IF;

  -- Prevent self-demotion from admin
  IF target_user_id = auth.uid() AND new_role != 'admin' THEN
    RAISE EXCEPTION 'Cannot change your own admin role.';
  END IF;

  -- Update the role
  UPDATE profiles 
  SET role = new_role, updated_at = NOW()
  WHERE id = target_user_id;

  RETURN FOUND;
END;
$$;

-- Create function to get paginated users
CREATE OR REPLACE FUNCTION get_users_paginated(
  page_size INTEGER DEFAULT 10,
  page_offset INTEGER DEFAULT 0,
  search_term TEXT DEFAULT NULL,
  role_filter TEXT DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
  total_count INTEGER;
BEGIN
  -- Check if user is admin
  IF NOT EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() AND role = 'admin'
  ) THEN
    RAISE EXCEPTION 'Access denied. Admin role required.';
  END IF;

  -- Get total count for pagination
  SELECT COUNT(*) INTO total_count
  FROM profiles p
  WHERE 
    (search_term IS NULL OR 
     p.email ILIKE '%' || search_term || '%' OR 
     p.first_name ILIKE '%' || search_term || '%' OR 
     p.last_name ILIKE '%' || search_term || '%')
    AND (role_filter IS NULL OR p.role = role_filter);

  -- Get paginated results
  SELECT json_build_object(
    'users', (
      SELECT json_agg(
        json_build_object(
          'id', p.id,
          'email', p.email,
          'first_name', p.first_name,
          'last_name', p.last_name,
          'avatar_url', p.avatar_url,
          'role', p.role,
          'created_at', p.created_at,
          'updated_at', p.updated_at,
          'last_sign_in_at', p.last_sign_in_at,
          'article_count', (SELECT COUNT(*) FROM articles WHERE author_id = p.id),
          'comment_count', (SELECT COUNT(*) FROM comments WHERE user_id = p.id),
          'order_count', (SELECT COUNT(*) FROM orders WHERE user_id = p.id)
        )
      )
      FROM profiles p
      WHERE 
        (search_term IS NULL OR 
         p.email ILIKE '%' || search_term || '%' OR 
         p.first_name ILIKE '%' || search_term || '%' OR 
         p.last_name ILIKE '%' || search_term || '%')
        AND (role_filter IS NULL OR p.role = role_filter)
      ORDER BY p.created_at DESC
      LIMIT page_size OFFSET page_offset
    ),
    'total_count', total_count,
    'page_size', page_size,
    'page_offset', page_offset,
    'has_more', (page_offset + page_size) < total_count
  ) INTO result;

  RETURN result;
END;
$$;

-- Create function to delete user (soft delete)
CREATE OR REPLACE FUNCTION delete_user_account(target_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if current user is admin
  IF NOT EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() AND role = 'admin'
  ) THEN
    RAISE EXCEPTION 'Access denied. Admin role required.';
  END IF;

  -- Prevent self-deletion
  IF target_user_id = auth.uid() THEN
    RAISE EXCEPTION 'Cannot delete your own account.';
  END IF;

  -- Soft delete by updating email and marking as deleted
  UPDATE profiles 
  SET 
    email = 'deleted_' || id || '@deleted.local',
    first_name = 'Deleted',
    last_name = 'User',
    avatar_url = NULL,
    role = 'user',
    updated_at = NOW()
  WHERE id = target_user_id;

  -- Anonymize user's articles
  UPDATE articles 
  SET author_id = NULL
  WHERE author_id = target_user_id;

  -- Delete user's comments
  DELETE FROM comments WHERE user_id = target_user_id;

  -- Delete user's engagement
  DELETE FROM article_engagement WHERE user_id = target_user_id;

  RETURN FOUND;
END;
$$;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION get_user_stats TO authenticated;
GRANT EXECUTE ON FUNCTION update_user_role TO authenticated;
GRANT EXECUTE ON FUNCTION get_users_paginated TO authenticated;
GRANT EXECUTE ON FUNCTION delete_user_account TO authenticated;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_created_at ON profiles(created_at);
CREATE INDEX IF NOT EXISTS idx_profiles_email_search ON profiles USING gin(to_tsvector('english', email));
CREATE INDEX IF NOT EXISTS idx_profiles_name_search ON profiles USING gin(to_tsvector('english', first_name || ' ' || last_name));
