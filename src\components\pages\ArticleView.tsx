import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Navbar } from '@/components/layout/Navbar';
import { ScrollToTop } from '@/components/ui/scroll-to-top';
import { useToast } from '@/components/ui/use-toast';
import {
  Calendar,
  User,
  Eye,
  Heart,
  MessageSquare,
  BookOpen,
  Share2,
  Bookmark,
  ArrowLeft,
  Clock,
  Tag,
} from 'lucide-react';
import { useAuth } from '../../../supabase/auth';
import { supabase } from '../../../supabase/supabase';

interface Article {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  featured_image: string | null;
  is_premium: boolean;
  views: number;
  likes: number;
  comments_count: number;
  read_time: number;
  published_at: string;
  seo_title: string | null;
  seo_description: string | null;
  author: {
    id: string;
    email: string;
  };
  category: {
    name: string;
    color: string;
    slug: string;
  } | null;
  tags: Array<{
    tag: string;
  }>;
}

interface Comment {
  id: string;
  content: string;
  created_at: string;
  user: {
    email: string;
  };
  likes: number;
}

export function ArticleView() {
  const { slug } = useParams<{ slug: string }>();
  const { user } = useAuth();
  const { toast } = useToast();
  const [article, setArticle] = useState<Article | null>(null);
  const [comments, setComments] = useState<Comment[]>([]);
  const [relatedArticles, setRelatedArticles] = useState<Article[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLiked, setIsLiked] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [newComment, setNewComment] = useState('');

  useEffect(() => {
    if (slug) {
      loadArticle();
    }
  }, [slug]);

  useEffect(() => {
    if (article && user) {
      checkUserEngagement();
    }
  }, [article, user]);

  const loadArticle = async () => {
    try {
      setIsLoading(true);
      
      const { data, error } = await supabase
        .from('articles')
        .select('*')
        .eq('slug', slug)
        .eq('is_published', true)
        .single();

      if (error) throw error;

      // Add mock author and category data
      const articleWithMockData = {
        ...data,
        author: { id: data.author_id, email: '<EMAIL>' },
        category: { name: 'Technology', color: '#3B82F6', slug: 'technology' },
        tags: ['technology', 'web development']
      };

      setArticle(articleWithMockData);

      // Increment view count
      await supabase
        .from('articles')
        .update({ views: (data.views || 0) + 1 })
        .eq('id', data.id);

      // Load comments
      loadComments(data.id);
      
      // Load related articles
      loadRelatedArticles(data.category?.slug, data.id);
    } catch (error) {
      console.error('Error loading article:', error);
      toast({
        title: 'Error',
        description: 'Article not found',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadComments = async (articleId: string) => {
    try {
      const { data, error } = await supabase
        .from('comments')
        .select('*')
        .eq('article_id', articleId)
        .eq('is_approved', true)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Add mock user data
      const commentsWithMockData = (data || []).map(comment => ({
        ...comment,
        user: { email: '<EMAIL>' }
      }));

      setComments(commentsWithMockData);
    } catch (error) {
      console.error('Error loading comments:', error);
    }
  };

  const loadRelatedArticles = async (categorySlug?: string, currentArticleId?: string) => {
    try {
      let query = supabase
        .from('articles')
        .select('*')
        .eq('is_published', true)
        .neq('id', currentArticleId)
        .limit(3);

      const { data, error } = await query;

      if (error) throw error;

      // Add mock author and category data
      const relatedWithMockData = (data || []).map(article => ({
        ...article,
        author: { email: '<EMAIL>' },
        category: { name: 'Technology', color: '#3B82F6', slug: 'technology' }
      }));

      setRelatedArticles(relatedWithMockData);
    } catch (error) {
      console.error('Error loading related articles:', error);
    }
  };

  const checkUserEngagement = async () => {
    if (!user || !article) return;

    try {
      const { data, error } = await supabase
        .from('article_engagement')
        .select('liked, bookmarked')
        .eq('user_id', user.id)
        .eq('article_id', article.id)
        .single();

      if (data) {
        setIsLiked(data.liked || false);
        setIsBookmarked(data.bookmarked || false);
      }
    } catch (error) {
      // User hasn't engaged with this article yet
    }
  };

  const handleLike = async () => {
    if (!user || !article) {
      toast({
        title: 'Please sign in',
        description: 'You need to be signed in to like articles',
      });
      return;
    }

    try {
      const { error } = await supabase
        .from('article_engagement')
        .upsert({
          user_id: user.id,
          article_id: article.id,
          liked: !isLiked,
          bookmarked: isBookmarked,
        });

      if (error) throw error;

      setIsLiked(!isLiked);
      
      // Update article likes count
      const newLikes = isLiked ? article.likes - 1 : article.likes + 1;
      setArticle(prev => prev ? { ...prev, likes: newLikes } : null);
    } catch (error) {
      console.error('Error updating like:', error);
      toast({
        title: 'Error',
        description: 'Failed to update like',
        variant: 'destructive',
      });
    }
  };

  const handleBookmark = async () => {
    if (!user || !article) {
      toast({
        title: 'Please sign in',
        description: 'You need to be signed in to bookmark articles',
      });
      return;
    }

    try {
      const { error } = await supabase
        .from('article_engagement')
        .upsert({
          user_id: user.id,
          article_id: article.id,
          liked: isLiked,
          bookmarked: !isBookmarked,
        });

      if (error) throw error;

      setIsBookmarked(!isBookmarked);
      toast({
        title: isBookmarked ? 'Removed from favorites' : 'Added to favorites',
        description: isBookmarked 
          ? 'Article removed from your favorites'
          : 'Article saved to your favorites',
      });
    } catch (error) {
      console.error('Error updating bookmark:', error);
      toast({
        title: 'Error',
        description: 'Failed to update bookmark',
        variant: 'destructive',
      });
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: article?.title,
          text: article?.excerpt,
          url: window.location.href,
        });
      } catch (error) {
        // User cancelled sharing
      }
    } else {
      // Fallback to copying URL
      navigator.clipboard.writeText(window.location.href);
      toast({
        title: 'Link copied',
        description: 'Article link copied to clipboard',
      });
    }
  };

  const handleCommentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user || !article || !newComment.trim()) {
      toast({
        title: 'Please sign in',
        description: 'You need to be signed in to comment',
      });
      return;
    }

    try {
      const { error } = await supabase
        .from('comments')
        .insert({
          article_id: article.id,
          user_id: user.id,
          content: newComment.trim(),
          is_approved: true, // Auto-approve for now
        });

      if (error) throw error;

      setNewComment('');
      loadComments(article.id);
      toast({
        title: 'Comment posted',
        description: 'Your comment has been posted successfully',
      });
    } catch (error) {
      console.error('Error posting comment:', error);
      toast({
        title: 'Error',
        description: 'Failed to post comment',
        variant: 'destructive',
      });
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
            <div className="h-64 bg-gray-200 rounded mb-8"></div>
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-4 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!article) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-7xl mx-auto px-4 py-8 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Article Not Found</h1>
          <p className="text-gray-600 mb-8">The article you're looking for doesn't exist.</p>
          <Link to="/articles">
            <Button>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Articles
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <article className="max-w-4xl mx-auto px-4 py-8">
        {/* Back Button */}
        <Link to="/articles" className="inline-flex items-center text-blue-600 hover:text-blue-700 mb-6">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Articles
        </Link>

        {/* Article Header */}
        <header className="mb-8">
          {article.category && (
            <Badge 
              variant="outline" 
              style={{ borderColor: article.category.color }}
              className="mb-4"
            >
              {article.category.name}
            </Badge>
          )}
          
          <h1 className="text-4xl font-bold text-gray-900 mb-4 leading-tight">
            {article.title}
          </h1>
          
          <p className="text-xl text-gray-600 mb-6">
            {article.excerpt}
          </p>

          {/* Author and Meta Info */}
          <div className="flex items-center justify-between flex-wrap gap-4 mb-6">
            <div className="flex items-center gap-4">
              <Avatar>
                <AvatarImage
                  src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${article.author.email}`}
                  alt={article.author.email}
                />
                <AvatarFallback>
                  {article.author.email[0].toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium text-gray-900">
                  {article.author.email.split('@')[0]}
                </p>
                <div className="flex items-center gap-4 text-sm text-gray-500">
                  <span className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {new Date(article.published_at).toLocaleDateString()}
                  </span>
                  <span className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    {article.read_time} min read
                  </span>
                  <span className="flex items-center gap-1">
                    <Eye className="h-3 w-3" />
                    {article.views} views
                  </span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleLike}
                className={isLiked ? 'text-red-600 border-red-600' : ''}
              >
                <Heart className={`h-4 w-4 mr-1 ${isLiked ? 'fill-current' : ''}`} />
                {article.likes}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleBookmark}
                className={isBookmarked ? 'text-blue-600 border-blue-600' : ''}
              >
                <Bookmark className={`h-4 w-4 ${isBookmarked ? 'fill-current' : ''}`} />
              </Button>
              <Button variant="outline" size="sm" onClick={handleShare}>
                <Share2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Featured Image */}
          {article.featured_image && (
            <img
              src={article.featured_image}
              alt={article.title}
              className="w-full h-64 md:h-96 object-cover rounded-lg mb-8"
            />
          )}
        </header>

        {/* Article Content */}
        <div className="prose prose-lg max-w-none mb-12">
          <div dangerouslySetInnerHTML={{ __html: article.content }} />
        </div>

        {/* Tags */}
        {article.tags && article.tags.length > 0 && (
          <div className="mb-8">
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <Tag className="h-5 w-5" />
              Tags
            </h3>
            <div className="flex flex-wrap gap-2">
              {article.tags.map((tag, index) => (
                <Badge key={index} variant="secondary">
                  {tag.tag}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Comments Section */}
        <section className="mb-12">
          <h3 className="text-2xl font-bold mb-6 flex items-center gap-2">
            <MessageSquare className="h-6 w-6" />
            Comments ({comments.length})
          </h3>

          {/* Comment Form */}
          {user ? (
            <form onSubmit={handleCommentSubmit} className="mb-8">
              <textarea
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder="Share your thoughts..."
                className="w-full p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={4}
              />
              <Button type="submit" className="mt-3" disabled={!newComment.trim()}>
                Post Comment
              </Button>
            </form>
          ) : (
            <div className="mb-8 p-4 bg-gray-100 rounded-lg text-center">
              <p className="text-gray-600 mb-3">Sign in to join the conversation</p>
              <Link to="/login">
                <Button>Sign In</Button>
              </Link>
            </div>
          )}

          {/* Comments List */}
          <div className="space-y-6">
            {comments.map((comment) => (
              <Card key={comment.id}>
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <Avatar>
                      <AvatarImage
                        src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${comment.user.email}`}
                        alt={comment.user.email}
                      />
                      <AvatarFallback>
                        {comment.user.email[0].toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="font-medium">
                          {comment.user.email.split('@')[0]}
                        </span>
                        <span className="text-sm text-gray-500">
                          {new Date(comment.created_at).toLocaleDateString()}
                        </span>
                      </div>
                      <p className="text-gray-700">{comment.content}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Related Articles */}
        {relatedArticles.length > 0 && (
          <section>
            <h3 className="text-2xl font-bold mb-6">Related Articles</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {relatedArticles.map((relatedArticle) => (
                <Card key={relatedArticle.id} className="hover:shadow-lg transition-shadow overflow-hidden">
                  {/* Article Image */}
                  <div className="relative h-48 overflow-hidden">
                    {relatedArticle.featured_image ? (
                      <img
                        src={relatedArticle.featured_image}
                        alt={relatedArticle.title}
                        className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center">
                        <BookOpen className="h-16 w-16 text-blue-400" />
                      </div>
                    )}

                    {/* Category Badge */}
                    {relatedArticle.category && (
                      <div className="absolute top-3 left-3">
                        <Badge
                          className="text-white font-medium shadow-lg text-xs px-2 py-1"
                          style={{ backgroundColor: relatedArticle.category.color }}
                        >
                          {relatedArticle.category.name}
                        </Badge>
                      </div>
                    )}

                    {/* Reading Time */}
                    <div className="absolute top-3 right-3">
                      <Badge variant="secondary" className="bg-white/90 text-gray-700 font-medium text-xs">
                        {relatedArticle.read_time || 5} min read
                      </Badge>
                    </div>
                  </div>

                  <CardContent className="p-4">
                    <h4 className="font-semibold mb-2 line-clamp-2 hover:text-blue-600 transition-colors">
                      {relatedArticle.title}
                    </h4>
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                      {relatedArticle.excerpt}
                    </p>

                    {/* Article Stats */}
                    <div className="flex items-center gap-3 text-xs text-gray-500 mb-3">
                      <span className="flex items-center gap-1">
                        <Eye className="h-3 w-3" />
                        {relatedArticle.views || 0}
                      </span>
                      <span className="flex items-center gap-1">
                        <Heart className="h-3 w-3" />
                        {relatedArticle.likes || 0}
                      </span>
                      <span className="flex items-center gap-1">
                        <MessageSquare className="h-3 w-3" />
                        {relatedArticle.comments_count || 0}
                      </span>
                    </div>

                    <Link to={`/articles/${relatedArticle.slug}`}>
                      <Button variant="outline" size="sm" className="w-full hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600 transition-all duration-300">
                        <BookOpen className="h-3 w-3 mr-2" />
                        Read Article
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>
        )}
      </article>

      <ScrollToTop />
    </div>
  );
}
