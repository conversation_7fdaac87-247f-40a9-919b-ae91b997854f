import { ReactNode } from "react";
import { Link } from "react-router-dom";

export default function AuthLayout({ children }: { children: ReactNode }) {
  return (
    <div className="min-h-screen bg-[#f5f5f7] text-black">
      {/* Modern navigation */}
      <header className="fixed top-0 z-50 w-full bg-white/80 backdrop-blur-md border-b border-gray-200 shadow-sm">
        <div className="max-w-[1200px] mx-auto flex h-16 items-center justify-between px-4">
          <div className="flex items-center">
            <Link to="/" className="font-semibold text-xl">
              News & Commerce Platform
            </Link>
          </div>
          <nav className="hidden md:flex items-center space-x-7 text-sm font-medium">
            <Link to="/" className="hover:text-blue-600 transition-colors">
              Home
            </Link>
            <Link to="/" className="hover:text-blue-600 transition-colors">
              Articles
            </Link>
            <Link to="/" className="hover:text-blue-600 transition-colors">
              Products
            </Link>
            <Link to="/" className="hover:text-blue-600 transition-colors">
              Pricing
            </Link>
            <Link to="/" className="hover:text-blue-600 transition-colors">
              About
            </Link>
          </nav>
        </div>
      </header>

      <div className="min-h-screen flex items-center justify-center pt-16">
        <div className="max-w-md w-full px-4">
          <div className="text-center mb-8">
            <h2 className="text-4xl font-bold tracking-tight">
              News & Commerce
            </h2>
            <p className="text-xl font-medium text-gray-600 mt-2">
              Your premium content platform
            </p>
          </div>
          {children}
        </div>
      </div>
    </div>
  );
}
