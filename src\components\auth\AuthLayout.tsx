import { ReactNode } from "react";
import { Link } from "react-router-dom";

export default function AuthLayout({ children }: { children: ReactNode }) {
  return (
    <div className="min-h-screen bg-[#f5f5f7] text-black">
      <div className="min-h-screen flex items-center justify-center">
        <div className="max-w-md w-full px-4">
          <div className="text-center mb-8">
            <h2 className="text-4xl font-bold tracking-tight">
              The Chronicle
            </h2>
            <p className="text-xl font-medium text-gray-600 mt-2">
              Your premium content platform
            </p>
          </div>
          {children}
        </div>
      </div>
    </div>
  );
}
