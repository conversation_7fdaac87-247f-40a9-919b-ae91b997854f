import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { LoadingButton } from '@/components/ui/loading-button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Navbar } from '@/components/layout/Navbar';
import { useToast } from '@/components/ui/use-toast';
import { useCart } from '@/contexts/CartContext';
import { useDebounce } from '@/hooks/useDebounce';
import {
  Search,
  Filter,
  Star,
  ShoppingCart,
  Heart,
  Eye,
  DollarSign,
  Package,
  TrendingUp,
} from 'lucide-react';
import { supabase } from '../../../supabase/supabase';

interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  price: number;
  sale_price: number | null;
  image_url: string | null;
  gallery: string[] | null;
  type: 'physical' | 'digital' | 'subscription';
  status: 'active' | 'inactive' | 'out_of_stock';
  stock_quantity: number | null;
  rating: number;
  reviews_count: number;
  created_at: string;
  category: {
    name: string;
    color: string;
  } | null;
}

export function Products() {
  const { toast } = useToast();
  const { addItem } = useCart();
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [addingToCart, setAddingToCart] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'physical' | 'digital' | 'subscription'>('all');
  const [sortBy, setSortBy] = useState<'newest' | 'price_low' | 'price_high' | 'popular'>('newest');
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  useEffect(() => {
    loadProducts();
  }, [filterType, sortBy, debouncedSearchTerm]);

  const loadProducts = async () => {
    try {
      setIsLoading(true);
      
      let query = supabase
        .from('products')
        .select('*')
        .eq('status', 'active');

      // Filter by type
      if (filterType !== 'all') {
        query = query.eq('type', filterType);
      }

      // Sort products
      switch (sortBy) {
        case 'price_low':
          query = query.order('price', { ascending: true });
          break;
        case 'price_high':
          query = query.order('price', { ascending: false });
          break;
        case 'popular':
          query = query.order('reviews_count', { ascending: false });
          break;
        default:
          query = query.order('created_at', { ascending: false });
      }

      const { data, error } = await query;

      if (error) throw error;

      // Add mock category data and filter by search term
      let productsWithMockData = (data || []).map(product => ({
        ...product,
        category: { name: 'Electronics', color: '#3B82F6' }
      }));

      if (debouncedSearchTerm) {
        productsWithMockData = productsWithMockData.filter(product =>
          product.name.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
          product.description?.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
        );
      }

      setProducts(productsWithMockData);
    } catch (error) {
      console.error('Error loading products:', error);
      toast({
        title: 'Error',
        description: 'Failed to load products',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddToCart = async (product: Product) => {
    setAddingToCart(product.id);
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 800));
      addItem(product, 1);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to add item to cart',
        variant: 'destructive',
      });
    } finally {
      setAddingToCart(null);
    }
  };



  const getDiscountPercentage = (price: number, salePrice: number) => {
    return Math.round(((price - salePrice) / price) * 100);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="h-96 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Our Products
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Discover our curated collection of digital and physical products
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          <div className="flex gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search products... (live search)"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div className="flex flex-wrap gap-4 items-center justify-between">
            {/* Type Filters */}
            <div className="flex flex-wrap gap-2">
              <Button
                variant={filterType === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterType('all')}
              >
                All Products
              </Button>
              <Button
                variant={filterType === 'physical' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterType('physical')}
              >
                <Package className="h-4 w-4 mr-1" />
                Physical
              </Button>
              <Button
                variant={filterType === 'digital' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterType('digital')}
              >
                <Eye className="h-4 w-4 mr-1" />
                Digital
              </Button>
              <Button
                variant={filterType === 'subscription' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterType('subscription')}
              >
                <TrendingUp className="h-4 w-4 mr-1" />
                Subscription
              </Button>
            </div>

            {/* Sort Options */}
            <div className="flex gap-2">
              <Button
                variant={sortBy === 'newest' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSortBy('newest')}
              >
                Newest
              </Button>
              <Button
                variant={sortBy === 'price_low' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSortBy('price_low')}
              >
                Price: Low to High
              </Button>
              <Button
                variant={sortBy === 'price_high' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSortBy('price_high')}
              >
                Price: High to Low
              </Button>
              <Button
                variant={sortBy === 'popular' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSortBy('popular')}
              >
                Popular
              </Button>
            </div>
          </div>
        </div>

        {/* Products Grid */}
        {products.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {products.map((product) => (
              <Card key={product.id} className="group hover:shadow-2xl transition-all duration-500 h-full border-0 shadow-lg overflow-hidden bg-white">
                <div className="relative overflow-hidden">
                  {product.image_url ? (
                    <img
                      src={product.image_url}
                      alt={product.name}
                      className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-700"
                    />
                  ) : (
                    <div className="w-full h-64 bg-gradient-to-br from-gray-100 via-gray-50 to-gray-100 flex items-center justify-center">
                      <Package className="h-20 w-20 text-gray-400" />
                    </div>
                  )}

                  {/* Overlay */}
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-500" />

                  {/* Badges */}
                  <div className="absolute top-4 left-4 flex flex-col gap-2">
                    <Badge className="bg-gradient-to-r from-blue-500 to-purple-600 text-white font-medium shadow-lg text-xs px-3 py-1">
                      {product.type.charAt(0).toUpperCase() + product.type.slice(1)}
                    </Badge>
                    {product.sale_price && (
                      <Badge className="bg-gradient-to-r from-red-500 to-pink-600 text-white font-bold shadow-lg text-xs px-3 py-1">
                        -{getDiscountPercentage(product.price, product.sale_price)}% OFF
                      </Badge>
                    )}
                    {product.featured && (
                      <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white font-medium shadow-lg text-xs px-3 py-1">
                        ⭐ Featured
                      </Badge>
                    )}
                  </div>

                  {/* Wishlist Button */}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute top-4 right-4 bg-white/90 hover:bg-white shadow-lg backdrop-blur-sm transition-all duration-300 hover:scale-110"
                  >
                    <Heart className="h-4 w-4 text-gray-600 hover:text-red-500 transition-colors" />
                  </Button>

                  {/* Quick View Overlay */}
                  <div className="absolute inset-x-0 bottom-0 p-4 bg-gradient-to-t from-black/80 via-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-4 group-hover:translate-y-0">
                    <Link to={`/products/${product.slug}`} className="block">
                      <Button className="w-full bg-white/90 hover:bg-white text-gray-900 font-medium shadow-lg" size="sm">
                        <Eye className="h-4 w-4 mr-2" />
                        Quick View
                      </Button>
                    </Link>
                  </div>
                </div>
                
                <CardContent className="p-6 flex flex-col h-full">
                  <div className="flex-1 space-y-4">
                    {/* Category */}
                    {product.category && (
                      <Badge
                        variant="outline"
                        style={{ borderColor: product.category.color }}
                        className="text-xs font-medium px-2 py-1"
                      >
                        {product.category.name}
                      </Badge>
                    )}

                    {/* Title */}
                    <h3 className="font-bold text-xl line-clamp-2 group-hover:text-blue-600 transition-colors leading-tight">
                      {product.name}
                    </h3>

                    {/* Description */}
                    <p className="text-gray-600 text-sm line-clamp-3 leading-relaxed">
                      {product.description}
                    </p>

                    {/* Rating */}
                    <div className="flex items-center gap-2">
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${
                              i < Math.floor(product.rating || 4)
                                ? 'text-yellow-400 fill-current'
                                : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                      <span className="text-sm text-gray-600 font-medium">
                        {(product.rating || 4).toFixed(1)}
                      </span>
                      <span className="text-xs text-gray-400">
                        ({product.reviews_count || 127} reviews)
                      </span>
                    </div>
                  </div>

                  {/* Price and Actions */}
                  <div className="space-y-4 mt-6 pt-4 border-t border-gray-100">
                    {/* Price */}
                    <div className="flex items-baseline justify-between">
                      <div className="flex items-baseline gap-2">
                        {product.sale_price ? (
                          <>
                            <span className="text-2xl font-bold text-green-600">
                              ${product.sale_price}
                            </span>
                            <span className="text-lg text-gray-500 line-through">
                              ${product.price}
                            </span>
                          </>
                        ) : (
                          <span className="text-2xl font-bold text-gray-900">
                            ${product.price}
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Stock Status */}
                    {product.type === 'physical' && product.stock_quantity !== null && (
                      <div className={`text-sm font-medium flex items-center gap-2 ${
                        product.stock_quantity > 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        <div className={`w-2 h-2 rounded-full ${
                          product.stock_quantity > 0 ? 'bg-green-500' : 'bg-red-500'
                        }`}></div>
                        {product.stock_quantity > 0
                          ? `${product.stock_quantity} in stock`
                          : 'Out of stock'
                        }
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex gap-3">
                      <Link to={`/products/${product.slug}`} className="flex-1">
                        <Button
                          variant="outline"
                          className="w-full border-2 hover:border-blue-500 hover:text-blue-600 transition-all duration-300"
                          size="sm"
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </Button>
                      </Link>
                      <LoadingButton
                        onClick={() => handleAddToCart(product)}
                        className="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                        size="sm"
                        loading={addingToCart === product.id}
                        loadingText="Adding..."
                        disabled={product.type === 'physical' && product.stock_quantity === 0}
                      >
                        <ShoppingCart className="h-4 w-4 mr-2" />
                        Add to Cart
                      </LoadingButton>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">
              {searchTerm || filterType !== 'all' ? 'No products found' : 'No products available'}
            </h3>
            <p className="text-gray-500 mb-6">
              {searchTerm || filterType !== 'all'
                ? 'Try adjusting your search criteria or browse all products.'
                : 'Check back later for new products.'
              }
            </p>
            {(searchTerm || filterType !== 'all') && (
              <Button
                onClick={() => {
                  setSearchTerm('');
                  setFilterType('all');
                }}
              >
                Clear Filters
              </Button>
            )}
          </div>
        )}

        {/* Load More Button */}
        {products.length > 0 && products.length % 12 === 0 && (
          <div className="text-center mt-12">
            <Button variant="outline" size="lg">
              Load More Products
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
