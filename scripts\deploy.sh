#!/bin/bash

# Deployment script for The Chronicle
# Usage: ./scripts/deploy.sh [environment] [version]

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-staging}"
VERSION="${2:-latest}"
BUILD_TIME=$(date -u +%Y-%m-%dT%H:%M:%SZ)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validate environment
validate_environment() {
    case $ENVIRONMENT in
        development|staging|production)
            log_info "Deploying to $ENVIRONMENT environment"
            ;;
        *)
            log_error "Invalid environment: $ENVIRONMENT"
            log_error "Valid environments: development, staging, production"
            exit 1
            ;;
    esac
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if required commands exist
    local required_commands=("node" "npm" "git")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            log_error "$cmd is required but not installed"
            exit 1
        fi
    done
    
    # Check Node.js version
    local node_version=$(node --version | cut -d'v' -f2)
    local required_version="18.0.0"
    if ! npx semver "$node_version" -r ">=$required_version" &> /dev/null; then
        log_error "Node.js version $required_version or higher is required (current: $node_version)"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Load environment variables
load_environment() {
    log_info "Loading environment configuration..."
    
    local env_file="$PROJECT_ROOT/.env.$ENVIRONMENT"
    if [[ -f "$env_file" ]]; then
        set -a
        source "$env_file"
        set +a
        log_success "Environment variables loaded from $env_file"
    else
        log_warning "Environment file $env_file not found"
    fi
    
    # Set build-time variables
    export VITE_BUILD_VERSION="$VERSION"
    export VITE_BUILD_TIME="$BUILD_TIME"
    export NODE_ENV="$ENVIRONMENT"
}

# Install dependencies
install_dependencies() {
    log_info "Installing dependencies..."
    cd "$PROJECT_ROOT"
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        npm ci --only=production
    else
        npm ci
    fi
    
    log_success "Dependencies installed"
}

# Run tests
run_tests() {
    if [[ "$ENVIRONMENT" != "production" ]]; then
        log_info "Running tests..."
        cd "$PROJECT_ROOT"
        
        npm run type-check
        npm run lint
        npm run test:run
        
        log_success "All tests passed"
    else
        log_info "Skipping tests for production deployment"
    fi
}

# Build application
build_application() {
    log_info "Building application for $ENVIRONMENT..."
    cd "$PROJECT_ROOT"
    
    npm run build
    
    # Verify build output
    if [[ ! -d "dist" ]]; then
        log_error "Build failed: dist directory not found"
        exit 1
    fi
    
    # Check if main files exist
    if [[ ! -f "dist/index.html" ]]; then
        log_error "Build failed: index.html not found"
        exit 1
    fi
    
    log_success "Application built successfully"
}

# Deploy to Vercel
deploy_vercel() {
    log_info "Deploying to Vercel..."
    
    if ! command -v vercel &> /dev/null; then
        log_info "Installing Vercel CLI..."
        npm install -g vercel
    fi
    
    local vercel_args=""
    if [[ "$ENVIRONMENT" == "production" ]]; then
        vercel_args="--prod"
    fi
    
    vercel deploy $vercel_args --yes
    
    log_success "Deployed to Vercel"
}

# Deploy to Docker
deploy_docker() {
    log_info "Building Docker image..."
    
    local image_name="thechronicle:$VERSION"
    local dockerfile_target="production"
    
    docker build \
        --target "$dockerfile_target" \
        --build-arg NODE_ENV="$ENVIRONMENT" \
        --build-arg VITE_BUILD_VERSION="$VERSION" \
        --build-arg VITE_BUILD_TIME="$BUILD_TIME" \
        -t "$image_name" \
        "$PROJECT_ROOT"
    
    log_success "Docker image built: $image_name"
    
    # Tag for registry if specified
    if [[ -n "$DOCKER_REGISTRY" ]]; then
        local registry_image="$DOCKER_REGISTRY/$image_name"
        docker tag "$image_name" "$registry_image"
        
        log_info "Pushing to registry..."
        docker push "$registry_image"
        log_success "Image pushed to registry: $registry_image"
    fi
}

# Deploy to static hosting
deploy_static() {
    log_info "Preparing static deployment..."
    
    local deploy_dir="$PROJECT_ROOT/deploy"
    rm -rf "$deploy_dir"
    mkdir -p "$deploy_dir"
    
    # Copy build files
    cp -r "$PROJECT_ROOT/dist/"* "$deploy_dir/"
    
    # Create deployment info
    cat > "$deploy_dir/deployment-info.json" << EOF
{
  "version": "$VERSION",
  "environment": "$ENVIRONMENT",
  "buildTime": "$BUILD_TIME",
  "gitCommit": "$(git rev-parse HEAD)",
  "gitBranch": "$(git rev-parse --abbrev-ref HEAD)"
}
EOF
    
    log_success "Static files prepared in $deploy_dir"
}

# Run smoke tests
run_smoke_tests() {
    if [[ -n "$DEPLOYMENT_URL" ]]; then
        log_info "Running smoke tests against $DEPLOYMENT_URL..."
        
        # Basic health check
        if curl -f -s "$DEPLOYMENT_URL/health" > /dev/null; then
            log_success "Health check passed"
        else
            log_warning "Health check failed or endpoint not available"
        fi
        
        # Check if main page loads
        if curl -f -s "$DEPLOYMENT_URL" | grep -q "The Chronicle"; then
            log_success "Main page loads correctly"
        else
            log_error "Main page failed to load"
            exit 1
        fi
        
        log_success "Smoke tests passed"
    else
        log_info "Skipping smoke tests (DEPLOYMENT_URL not set)"
    fi
}

# Cleanup
cleanup() {
    log_info "Cleaning up..."
    
    # Remove temporary files
    rm -rf "$PROJECT_ROOT/deploy"
    
    # Clean npm cache
    npm cache clean --force
    
    log_success "Cleanup completed"
}

# Send notification
send_notification() {
    if [[ -n "$SLACK_WEBHOOK_URL" ]]; then
        log_info "Sending deployment notification..."
        
        local message="🚀 Deployment completed successfully!
Environment: $ENVIRONMENT
Version: $VERSION
Time: $BUILD_TIME"
        
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"$message\"}" \
            "$SLACK_WEBHOOK_URL" || log_warning "Failed to send notification"
    fi
}

# Main deployment function
main() {
    log_info "Starting deployment process..."
    log_info "Environment: $ENVIRONMENT"
    log_info "Version: $VERSION"
    log_info "Build Time: $BUILD_TIME"
    
    validate_environment
    check_prerequisites
    load_environment
    install_dependencies
    run_tests
    build_application
    
    # Choose deployment method based on environment variables
    if [[ -n "$VERCEL_TOKEN" ]]; then
        deploy_vercel
    elif [[ -n "$DOCKER_REGISTRY" ]]; then
        deploy_docker
    else
        deploy_static
    fi
    
    run_smoke_tests
    send_notification
    cleanup
    
    log_success "Deployment completed successfully!"
    log_info "Environment: $ENVIRONMENT"
    log_info "Version: $VERSION"
    
    if [[ -n "$DEPLOYMENT_URL" ]]; then
        log_info "URL: $DEPLOYMENT_URL"
    fi
}

# Error handling
trap 'log_error "Deployment failed at line $LINENO"; exit 1' ERR

# Run main function
main "$@"
