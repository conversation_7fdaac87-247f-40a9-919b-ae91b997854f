// Advanced User Behavior Tracking System
// Real-time user interaction analytics and insights

export interface UserEvent {
  id: string;
  timestamp: Date;
  userId?: string;
  sessionId: string;
  type: 'page_view' | 'click' | 'scroll' | 'form_submit' | 'search' | 'purchase' | 'custom';
  category: string;
  action: string;
  label?: string;
  value?: number;
  properties: Record<string, any>;
  page: {
    url: string;
    title: string;
    referrer: string;
  };
  device: {
    type: 'desktop' | 'mobile' | 'tablet';
    os: string;
    browser: string;
    screenSize: string;
  };
  location?: {
    country: string;
    city: string;
    timezone: string;
  };
}

export interface UserSession {
  id: string;
  userId?: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  pageViews: number;
  events: number;
  bounced: boolean;
  converted: boolean;
  revenue?: number;
  utm: Record<string, string>;
  device: UserEvent['device'];
  location?: UserEvent['location'];
}

export interface UserJourney {
  userId: string;
  sessions: UserSession[];
  totalEvents: number;
  totalPageViews: number;
  totalDuration: number;
  firstSeen: Date;
  lastSeen: Date;
  conversionEvents: UserEvent[];
  segments: string[];
}

export interface HeatmapData {
  page: string;
  clicks: Array<{
    x: number;
    y: number;
    count: number;
    element: string;
  }>;
  scrollDepth: Array<{
    depth: number;
    percentage: number;
  }>;
  timeOnPage: number;
  exitRate: number;
}

class BehaviorTracker {
  private sessionId: string;
  private userId?: string;
  private events: UserEvent[] = [];
  private sessionStart: Date;
  private isTracking: boolean = true;
  private flushInterval: number = 10000; // 10 seconds
  private maxQueueSize: number = 50;
  private observers: Map<string, IntersectionObserver | MutationObserver> = new Map();

  constructor() {
    this.sessionId = this.generateSessionId();
    this.sessionStart = new Date();
    this.setupEventListeners();
    this.setupScrollTracking();
    this.setupClickTracking();
    this.setupFormTracking();
    this.startFlushTimer();
  }

  /**
   * Initialize behavior tracking
   */
  init(config: {
    userId?: string;
    enabled?: boolean;
    trackClicks?: boolean;
    trackScrolls?: boolean;
    trackForms?: boolean;
  }) {
    this.userId = config.userId;
    this.isTracking = config.enabled !== false;

    if (config.trackClicks !== false) {
      this.setupClickTracking();
    }
    if (config.trackScrolls !== false) {
      this.setupScrollTracking();
    }
    if (config.trackForms !== false) {
      this.setupFormTracking();
    }
  }

  /**
   * Track a custom event
   */
  track(
    type: UserEvent['type'],
    category: string,
    action: string,
    label?: string,
    value?: number,
    properties?: Record<string, any>
  ): string {
    if (!this.isTracking) return '';

    const event: UserEvent = {
      id: this.generateId(),
      timestamp: new Date(),
      userId: this.userId,
      sessionId: this.sessionId,
      type,
      category,
      action,
      label,
      value,
      properties: properties || {},
      page: this.getCurrentPage(),
      device: this.getDeviceInfo(),
      location: this.getLocationInfo(),
    };

    this.addEvent(event);
    return event.id;
  }

  /**
   * Track page view
   */
  trackPageView(url?: string, title?: string): string {
    return this.track(
      'page_view',
      'navigation',
      'page_view',
      url || window.location.pathname,
      undefined,
      {
        url: url || window.location.href,
        title: title || document.title,
        referrer: document.referrer,
      }
    );
  }

  /**
   * Track user interaction
   */
  trackInteraction(element: HTMLElement, action: string, properties?: Record<string, any>): string {
    const elementInfo = this.getElementInfo(element);
    
    return this.track(
      'click',
      'interaction',
      action,
      elementInfo.selector,
      undefined,
      {
        ...elementInfo,
        ...properties,
      }
    );
  }

  /**
   * Track form submission
   */
  trackFormSubmit(form: HTMLFormElement, success: boolean, properties?: Record<string, any>): string {
    const formData = this.getFormData(form);
    
    return this.track(
      'form_submit',
      'form',
      success ? 'submit_success' : 'submit_error',
      form.id || form.name || 'unnamed_form',
      undefined,
      {
        formId: form.id,
        formName: form.name,
        fieldCount: formData.fieldCount,
        ...properties,
      }
    );
  }

  /**
   * Track search
   */
  trackSearch(query: string, results: number, properties?: Record<string, any>): string {
    return this.track(
      'search',
      'search',
      'query',
      query,
      results,
      {
        query,
        resultsCount: results,
        ...properties,
      }
    );
  }

  /**
   * Track purchase/conversion
   */
  trackPurchase(
    transactionId: string,
    revenue: number,
    items: Array<{ id: string; name: string; price: number; quantity: number }>,
    properties?: Record<string, any>
  ): string {
    return this.track(
      'purchase',
      'ecommerce',
      'purchase',
      transactionId,
      revenue,
      {
        transactionId,
        revenue,
        items,
        itemCount: items.length,
        ...properties,
      }
    );
  }

  /**
   * Track scroll depth
   */
  trackScrollDepth(depth: number): string {
    return this.track(
      'scroll',
      'engagement',
      'scroll_depth',
      `${depth}%`,
      depth,
      {
        scrollDepth: depth,
        pageHeight: document.documentElement.scrollHeight,
        viewportHeight: window.innerHeight,
      }
    );
  }

  /**
   * Get user session data
   */
  getSession(): UserSession {
    const now = new Date();
    const duration = now.getTime() - this.sessionStart.getTime();
    const pageViews = this.events.filter(e => e.type === 'page_view').length;
    const bounced = pageViews <= 1 && duration < 30000; // Less than 30 seconds, single page
    const converted = this.events.some(e => e.type === 'purchase');
    const revenue = this.events
      .filter(e => e.type === 'purchase')
      .reduce((sum, e) => sum + (e.value || 0), 0);

    return {
      id: this.sessionId,
      userId: this.userId,
      startTime: this.sessionStart,
      endTime: now,
      duration: Math.round(duration / 1000), // in seconds
      pageViews,
      events: this.events.length,
      bounced,
      converted,
      revenue: revenue > 0 ? revenue : undefined,
      utm: this.getUtmParams(),
      device: this.getDeviceInfo(),
      location: this.getLocationInfo(),
    };
  }

  /**
   * Generate heatmap data for current page
   */
  generateHeatmapData(): HeatmapData {
    const pageEvents = this.events.filter(e => e.page.url === window.location.href);
    const clicks = pageEvents
      .filter(e => e.type === 'click')
      .map(e => ({
        x: e.properties.x || 0,
        y: e.properties.y || 0,
        count: 1,
        element: e.properties.tagName || 'unknown',
      }));

    const scrollEvents = pageEvents.filter(e => e.type === 'scroll');
    const maxScroll = Math.max(...scrollEvents.map(e => e.value || 0), 0);
    
    return {
      page: window.location.href,
      clicks,
      scrollDepth: [{ depth: maxScroll, percentage: maxScroll }],
      timeOnPage: pageEvents.length > 0 ? Date.now() - pageEvents[0].timestamp.getTime() : 0,
      exitRate: 0, // Would be calculated server-side
    };
  }

  // Private methods

  private setupEventListeners() {
    // Page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.track('custom', 'engagement', 'page_hidden');
      } else {
        this.track('custom', 'engagement', 'page_visible');
      }
    });

    // Page unload
    window.addEventListener('beforeunload', () => {
      this.flush();
    });

    // Hash changes (SPA navigation)
    window.addEventListener('hashchange', () => {
      this.trackPageView();
    });

    // History API changes (SPA navigation)
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function(...args) {
      originalPushState.apply(history, args);
      setTimeout(() => behaviorTracker.trackPageView(), 0);
    };

    history.replaceState = function(...args) {
      originalReplaceState.apply(history, args);
      setTimeout(() => behaviorTracker.trackPageView(), 0);
    };
  }

  private setupClickTracking() {
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      if (!target) return;

      this.trackInteraction(target, 'click', {
        x: event.clientX,
        y: event.clientY,
        button: event.button,
      });
    });
  }

  private setupScrollTracking() {
    let lastScrollDepth = 0;
    const scrollThresholds = [25, 50, 75, 90, 100];

    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollDepth = Math.round((scrollTop / documentHeight) * 100);

      for (const threshold of scrollThresholds) {
        if (scrollDepth >= threshold && lastScrollDepth < threshold) {
          this.trackScrollDepth(threshold);
        }
      }

      lastScrollDepth = scrollDepth;
    };

    let scrollTimeout: number;
    window.addEventListener('scroll', () => {
      clearTimeout(scrollTimeout);
      scrollTimeout = window.setTimeout(handleScroll, 100);
    });
  }

  private setupFormTracking() {
    document.addEventListener('submit', (event) => {
      const form = event.target as HTMLFormElement;
      if (!form || form.tagName !== 'FORM') return;

      // Track form submission attempt
      this.trackFormSubmit(form, true);
    });

    // Track form field interactions
    document.addEventListener('focus', (event) => {
      const target = event.target as HTMLElement;
      if (target && (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.tagName === 'SELECT')) {
        this.track('custom', 'form', 'field_focus', target.name || target.id);
      }
    }, true);
  }

  private addEvent(event: UserEvent) {
    this.events.push(event);
    
    if (this.events.length >= this.maxQueueSize) {
      this.flush();
    }
  }

  private startFlushTimer() {
    setInterval(() => {
      if (this.events.length > 0) {
        this.flush();
      }
    }, this.flushInterval);
  }

  private async flush() {
    if (!this.isTracking || this.events.length === 0) return;

    const eventsToSend = [...this.events];
    this.events = [];

    try {
      await this.sendEvents(eventsToSend);
    } catch (error) {
      console.warn('Failed to send behavior events:', error);
      // Re-queue events
      this.events.unshift(...eventsToSend);
    }
  }

  private async sendEvents(events: UserEvent[]) {
    const response = await fetch('/api/analytics/events', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        session: this.getSession(),
        events,
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to send events: ${response.status}`);
    }
  }

  private getCurrentPage() {
    return {
      url: window.location.href,
      title: document.title,
      referrer: document.referrer,
    };
  }

  private getDeviceInfo(): UserEvent['device'] {
    const userAgent = navigator.userAgent;
    
    return {
      type: this.getDeviceType(),
      os: this.getOS(userAgent),
      browser: this.getBrowser(userAgent),
      screenSize: `${screen.width}x${screen.height}`,
    };
  }

  private getDeviceType(): 'desktop' | 'mobile' | 'tablet' {
    const userAgent = navigator.userAgent;
    if (/tablet|ipad|playbook|silk/i.test(userAgent)) {
      return 'tablet';
    }
    if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) {
      return 'mobile';
    }
    return 'desktop';
  }

  private getOS(userAgent: string): string {
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iOS')) return 'iOS';
    return 'Unknown';
  }

  private getBrowser(userAgent: string): string {
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    return 'Unknown';
  }

  private getLocationInfo(): UserEvent['location'] | undefined {
    // This would typically be set by server-side geolocation
    return undefined;
  }

  private getElementInfo(element: HTMLElement) {
    return {
      tagName: element.tagName.toLowerCase(),
      id: element.id,
      className: element.className,
      text: element.textContent?.slice(0, 100),
      selector: this.getElementSelector(element),
    };
  }

  private getElementSelector(element: HTMLElement): string {
    if (element.id) {
      return `#${element.id}`;
    }
    
    if (element.className) {
      return `.${element.className.split(' ').join('.')}`;
    }
    
    return element.tagName.toLowerCase();
  }

  private getFormData(form: HTMLFormElement) {
    const formData = new FormData(form);
    const fields = Array.from(formData.keys());
    
    return {
      fieldCount: fields.length,
      fields: fields,
    };
  }

  private getUtmParams(): Record<string, string> {
    const params = new URLSearchParams(window.location.search);
    const utm: Record<string, string> = {};
    
    ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content'].forEach(param => {
      const value = params.get(param);
      if (value) {
        utm[param] = value;
      }
    });
    
    return utm;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Global instance
export const behaviorTracker = new BehaviorTracker();

// Utility functions
export function trackEvent(category: string, action: string, label?: string, value?: number): string {
  return behaviorTracker.track('custom', category, action, label, value);
}

export function trackPageView(url?: string, title?: string): string {
  return behaviorTracker.trackPageView(url, title);
}

export function trackClick(element: HTMLElement, properties?: Record<string, any>): string {
  return behaviorTracker.trackInteraction(element, 'click', properties);
}

export function trackSearch(query: string, results: number): string {
  return behaviorTracker.trackSearch(query, results);
}

export function trackPurchase(transactionId: string, revenue: number, items: any[]): string {
  return behaviorTracker.trackPurchase(transactionId, revenue, items);
}

export function initBehaviorTracking(config: { userId?: string; enabled?: boolean }) {
  behaviorTracker.init(config);
}

export default behaviorTracker;
