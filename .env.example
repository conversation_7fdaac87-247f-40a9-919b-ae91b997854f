# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Supabase Project ID (for type generation)
SUPABASE_PROJECT_ID=your_supabase_project_id

# Tempo Development Tools (optional)
VITE_TEMPO=false

# Base path for deployment (optional)
VITE_BASE_PATH=/

# OpenAI API Key for Supabase AI (optional)
OPENAI_API_KEY=your_openai_api_key

# Stripe Configuration (Test Keys - Replace with your actual test keys)
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_51234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890
STRIPE_SECRET_KEY=sk_test_51234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890
STRIPE_WEBHOOK_SECRET=whsec_1234567890abcdefghijklmnopqrstuvwxyz

# Social Authentication Keys (Replace with your actual keys)
VITE_GOOGLE_CLIENT_ID=123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com
VITE_GITHUB_CLIENT_ID=1234567890abcdef1234
VITE_TWITTER_CLIENT_ID=abcdefghijklmnopqrstuvwxyz1234567890
VITE_FACEBOOK_APP_ID=1234567890123456

# Application Configuration
VITE_APP_URL=http://localhost:5173
VITE_API_URL=http://localhost:3000/api

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# Analytics (Optional)
VITE_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
VITE_MIXPANEL_TOKEN=your_mixpanel_token

# Storage Configuration
VITE_STORAGE_BUCKET=your_storage_bucket_name

# Security
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random
ENCRYPTION_KEY=your_encryption_key_here
