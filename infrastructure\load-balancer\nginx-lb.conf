# Advanced Load Balancer Configuration for The Chronicle
# High-performance Nginx configuration with load balancing, caching, and security

# Main context configuration
user nginx;
worker_processes auto;
worker_rlimit_nofile 65535;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# Events configuration for high performance
events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
    accept_mutex off;
}

# HTTP configuration
http {
    # Basic settings
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Performance optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 1000;
    types_hash_max_size 2048;
    server_tokens off;
    
    # Buffer sizes
    client_body_buffer_size 128k;
    client_max_body_size 50m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;
    
    # Timeouts
    client_body_timeout 12;
    client_header_timeout 12;
    send_timeout 10;
    
    # Logging format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';
    
    access_log /var/log/nginx/access.log main;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml
        application/x-font-ttf
        application/vnd.ms-fontobject
        font/opentype;
    
    # Brotli compression (if module available)
    # brotli on;
    # brotli_comp_level 6;
    # brotli_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # Rate limiting zones
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=general:10m rate=30r/s;
    
    # Connection limiting
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
    
    # Upstream servers for load balancing
    upstream app_servers {
        # Load balancing method
        least_conn;
        
        # Application servers
        server app1.thechronicle.internal:3000 weight=3 max_fails=3 fail_timeout=30s;
        server app2.thechronicle.internal:3000 weight=3 max_fails=3 fail_timeout=30s;
        server app3.thechronicle.internal:3000 weight=2 max_fails=3 fail_timeout=30s backup;
        
        # Health check (if nginx_upstream_check module available)
        # check interval=3000 rise=2 fall=5 timeout=1000 type=http;
        # check_http_send "HEAD /health HTTP/1.0\r\n\r\n";
        # check_http_expect_alive http_2xx http_3xx;
        
        # Keepalive connections
        keepalive 32;
        keepalive_requests 100;
        keepalive_timeout 60s;
    }
    
    # API servers for backend services
    upstream api_servers {
        least_conn;
        server api1.thechronicle.internal:8000 weight=3 max_fails=3 fail_timeout=30s;
        server api2.thechronicle.internal:8000 weight=3 max_fails=3 fail_timeout=30s;
        server api3.thechronicle.internal:8000 weight=2 max_fails=3 fail_timeout=30s backup;
        keepalive 16;
    }
    
    # Cache configuration
    proxy_cache_path /var/cache/nginx/app levels=1:2 keys_zone=app_cache:10m max_size=1g inactive=60m use_temp_path=off;
    proxy_cache_path /var/cache/nginx/api levels=1:2 keys_zone=api_cache:10m max_size=500m inactive=30m use_temp_path=off;
    proxy_cache_path /var/cache/nginx/static levels=1:2 keys_zone=static_cache:10m max_size=2g inactive=24h use_temp_path=off;
    
    # Security headers map
    map $sent_http_content_type $security_headers {
        default "X-Content-Type-Options: nosniff; X-Frame-Options: SAMEORIGIN; X-XSS-Protection: 1; mode=block";
        ~image/ "X-Content-Type-Options: nosniff";
    }
    
    # Real IP configuration (for CloudFlare/CDN)
    set_real_ip_from ************/22;
    set_real_ip_from ************/22;
    set_real_ip_from **********/22;
    set_real_ip_from **********/13;
    set_real_ip_from **********/14;
    set_real_ip_from *************/18;
    set_real_ip_from **********/22;
    set_real_ip_from ************/18;
    set_real_ip_from ***********/15;
    set_real_ip_from **********/13;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from *************/22;
    set_real_ip_from ************/17;
    real_ip_header CF-Connecting-IP;
    
    # Main server configuration
    server {
        listen 80;
        listen [::]:80;
        server_name thechronicle.com www.thechronicle.com;
        
        # Redirect HTTP to HTTPS
        return 301 https://$server_name$request_uri;
    }
    
    # HTTPS server configuration
    server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;
        server_name thechronicle.com www.thechronicle.com;
        
        # SSL configuration
        ssl_certificate /etc/ssl/certs/thechronicle.com.crt;
        ssl_certificate_key /etc/ssl/private/thechronicle.com.key;
        ssl_session_timeout 1d;
        ssl_session_cache shared:SSL:50m;
        ssl_session_tickets off;
        
        # Modern SSL configuration
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        
        # HSTS
        add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
        
        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com https://js.stripe.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: https:; font-src 'self' https://fonts.gstatic.com; connect-src 'self' https://api.stripe.com https://www.google-analytics.com https://*.supabase.co; frame-src 'self' https://js.stripe.com;" always;
        
        # Connection limiting
        limit_conn conn_limit_per_ip 20;
        
        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # Load balancer status
        location /nginx_status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            allow 10.0.0.0/8;
            allow **********/12;
            allow ***********/16;
            deny all;
        }
        
        # API endpoints with load balancing
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            # Proxy settings
            proxy_pass http://api_servers;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            
            # Timeouts
            proxy_connect_timeout 5s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # Caching for GET requests
            proxy_cache api_cache;
            proxy_cache_valid 200 302 5m;
            proxy_cache_valid 404 1m;
            proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
            proxy_cache_lock on;
            proxy_cache_lock_timeout 5s;
            
            # Cache bypass for authenticated requests
            proxy_cache_bypass $http_authorization;
            proxy_no_cache $http_authorization;
            
            add_header X-Cache-Status $upstream_cache_status;
        }
        
        # Authentication endpoints with stricter rate limiting
        location /api/auth/ {
            limit_req zone=login burst=5 nodelay;
            
            proxy_pass http://api_servers;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # No caching for auth endpoints
            proxy_no_cache 1;
            proxy_cache_bypass 1;
        }
        
        # Static assets with aggressive caching
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            proxy_pass http://app_servers;
            proxy_cache static_cache;
            proxy_cache_valid 200 24h;
            proxy_cache_valid 404 1h;
            proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
            
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header X-Cache-Status $upstream_cache_status;
            
            # Gzip static assets
            gzip_static on;
        }
        
        # Main application with load balancing
        location / {
            limit_req zone=general burst=50 nodelay;
            
            # Proxy to application servers
            proxy_pass http://app_servers;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            
            # Timeouts
            proxy_connect_timeout 5s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
            
            # Caching for static content
            proxy_cache app_cache;
            proxy_cache_valid 200 302 10m;
            proxy_cache_valid 404 1m;
            proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
            
            # Cache bypass for dynamic content
            proxy_cache_bypass $http_authorization $arg_nocache;
            proxy_no_cache $http_authorization $arg_nocache;
            
            add_header X-Cache-Status $upstream_cache_status;
        }
        
        # Error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /404.html {
            root /usr/share/nginx/html;
            internal;
        }
        
        location = /50x.html {
            root /usr/share/nginx/html;
            internal;
        }
        
        # Deny access to hidden files
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }
        
        # Deny access to backup files
        location ~ ~$ {
            deny all;
            access_log off;
            log_not_found off;
        }
    }
}
