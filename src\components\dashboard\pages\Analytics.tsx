import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Eye,
  Heart,
  MessageSquare,
  Users,
  Calendar,
  Target,
  Award,
  Clock,
} from 'lucide-react';
import { useAuth } from '../../../../supabase/auth';
import { supabase } from '../../../../supabase/supabase';

interface AnalyticsData {
  totalViews: number;
  totalLikes: number;
  totalComments: number;
  totalShares: number;
  articlesPublished: number;
  averageReadTime: number;
  topPerformingArticles: Array<{
    id: string;
    title: string;
    views: number;
    likes: number;
    comments_count: number;
    published_at: string;
  }>;
  monthlyStats: Array<{
    month: string;
    views: number;
    likes: number;
    articles: number;
  }>;
  categoryPerformance: Array<{
    category: string;
    articles: number;
    views: number;
    engagement: number;
  }>;
}

export function Analytics() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');

  useEffect(() => {
    if (user) {
      loadAnalytics();
    }
  }, [user, timeRange]);

  const loadAnalytics = async () => {
    try {
      setIsLoading(true);
      
      // Get user's articles
      const { data: articles, error: articlesError } = await supabase
        .from('articles')
        .select(`
          *,
          category:categories(name, color)
        `)
        .eq('author_id', user?.id)
        .eq('is_published', true);

      if (articlesError) throw articlesError;

      // Calculate analytics
      const totalViews = articles?.reduce((sum, article) => sum + (article.views || 0), 0) || 0;
      const totalLikes = articles?.reduce((sum, article) => sum + (article.likes || 0), 0) || 0;
      const totalComments = articles?.reduce((sum, article) => sum + (article.comments_count || 0), 0) || 0;
      const totalShares = articles?.reduce((sum, article) => sum + (article.shares || 0), 0) || 0;
      const averageReadTime = articles?.reduce((sum, article) => sum + (article.read_time || 5), 0) / (articles?.length || 1) || 5;

      // Top performing articles
      const topPerformingArticles = articles
        ?.sort((a, b) => (b.views || 0) - (a.views || 0))
        .slice(0, 5) || [];

      // Category performance
      const categoryStats = articles?.reduce((acc, article) => {
        const categoryName = article.category?.name || 'Uncategorized';
        if (!acc[categoryName]) {
          acc[categoryName] = { articles: 0, views: 0, engagement: 0 };
        }
        acc[categoryName].articles += 1;
        acc[categoryName].views += article.views || 0;
        acc[categoryName].engagement += (article.likes || 0) + (article.comments_count || 0);
        return acc;
      }, {} as Record<string, { articles: number; views: number; engagement: number }>);

      const categoryPerformance = Object.entries(categoryStats || {}).map(([category, stats]) => ({
        category,
        ...stats,
      }));

      // Mock monthly stats (in a real app, you'd calculate this from actual data)
      const monthlyStats = [
        { month: 'Jan', views: Math.floor(totalViews * 0.15), likes: Math.floor(totalLikes * 0.15), articles: Math.floor((articles?.length || 0) * 0.2) },
        { month: 'Feb', views: Math.floor(totalViews * 0.18), likes: Math.floor(totalLikes * 0.18), articles: Math.floor((articles?.length || 0) * 0.25) },
        { month: 'Mar', views: Math.floor(totalViews * 0.22), likes: Math.floor(totalLikes * 0.22), articles: Math.floor((articles?.length || 0) * 0.3) },
        { month: 'Apr', views: Math.floor(totalViews * 0.25), likes: Math.floor(totalLikes * 0.25), articles: Math.floor((articles?.length || 0) * 0.25) },
        { month: 'May', views: Math.floor(totalViews * 0.20), likes: Math.floor(totalLikes * 0.20), articles: Math.floor((articles?.length || 0) * 0.2) },
      ];

      setAnalytics({
        totalViews,
        totalLikes,
        totalComments,
        totalShares,
        articlesPublished: articles?.length || 0,
        averageReadTime,
        topPerformingArticles,
        monthlyStats,
        categoryPerformance,
      });
    } catch (error) {
      console.error('Error loading analytics:', error);
      toast({
        title: 'Error',
        description: 'Failed to load analytics data',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="h-64 bg-gray-200 rounded"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="text-center py-12">
        <BarChart3 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-xl font-medium text-gray-900 mb-2">No analytics data available</h3>
        <p className="text-gray-500">Publish some articles to see your analytics.</p>
      </div>
    );
  }

  const engagementRate = analytics.totalViews > 0 
    ? ((analytics.totalLikes + analytics.totalComments) / analytics.totalViews * 100).toFixed(1)
    : '0';

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <BarChart3 className="h-8 w-8 text-blue-600" />
            Analytics
          </h1>
          <p className="text-gray-600 mt-1">
            Track your content performance and engagement
          </p>
        </div>
        <div className="flex gap-2">
          {(['7d', '30d', '90d', '1y'] as const).map((range) => (
            <Button
              key={range}
              variant={timeRange === range ? 'default' : 'outline'}
              size="sm"
              onClick={() => setTimeRange(range)}
            >
              {range}
            </Button>
          ))}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Views</p>
                <p className="text-2xl font-bold">{analytics.totalViews.toLocaleString()}</p>
                <p className="text-xs text-green-600 flex items-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  +12% from last month
                </p>
              </div>
              <Eye className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Likes</p>
                <p className="text-2xl font-bold">{analytics.totalLikes.toLocaleString()}</p>
                <p className="text-xs text-green-600 flex items-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  +8% from last month
                </p>
              </div>
              <Heart className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Comments</p>
                <p className="text-2xl font-bold">{analytics.totalComments.toLocaleString()}</p>
                <p className="text-xs text-green-600 flex items-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  +15% from last month
                </p>
              </div>
              <MessageSquare className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Engagement Rate</p>
                <p className="text-2xl font-bold">{engagementRate}%</p>
                <p className="text-xs text-green-600 flex items-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  +3% from last month
                </p>
              </div>
              <Target className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts and Performance */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Performing Articles */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              Top Performing Articles
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.topPerformingArticles.map((article, index) => (
                <div key={article.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant="outline" className="text-xs">
                        #{index + 1}
                      </Badge>
                      <span className="text-sm font-medium truncate">
                        {article.title}
                      </span>
                    </div>
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <span className="flex items-center gap-1">
                        <Eye className="h-3 w-3" />
                        {article.views}
                      </span>
                      <span className="flex items-center gap-1">
                        <Heart className="h-3 w-3" />
                        {article.likes}
                      </span>
                      <span className="flex items-center gap-1">
                        <MessageSquare className="h-3 w-3" />
                        {article.comments_count}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Category Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Category Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.categoryPerformance.map((category) => (
                <div key={category.category} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">{category.category}</span>
                    <span className="text-sm text-gray-500">
                      {category.articles} articles
                    </span>
                  </div>
                  <div className="space-y-1">
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>Views: {category.views}</span>
                      <span>Engagement: {category.engagement}</span>
                    </div>
                    <Progress 
                      value={(category.views / analytics.totalViews) * 100} 
                      className="h-2" 
                    />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Additional Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Reading Stats
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Average Read Time</span>
                <span className="font-medium">{analytics.averageReadTime.toFixed(1)} min</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Articles Published</span>
                <span className="font-medium">{analytics.articlesPublished}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Total Shares</span>
                <span className="font-medium">{analytics.totalShares}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Audience Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Unique Readers</span>
                <span className="font-medium">{Math.floor(analytics.totalViews * 0.7).toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Return Readers</span>
                <span className="font-medium">{Math.floor(analytics.totalViews * 0.3).toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Avg. Session Duration</span>
                <span className="font-medium">3.2 min</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Publishing Schedule
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">This Month</span>
                <span className="font-medium">
                  {analytics.monthlyStats[analytics.monthlyStats.length - 1]?.articles || 0} articles
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Publishing Frequency</span>
                <span className="font-medium">2.3 per week</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Best Publishing Day</span>
                <span className="font-medium">Tuesday</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
