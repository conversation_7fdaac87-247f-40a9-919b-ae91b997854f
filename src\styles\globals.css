@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%; /* Blue */
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%; /* Blue */
    --radius: 0.5rem;
  }

  .dark {
    /* Dark theme colors with gold accents */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 45 93% 58%; /* Gold */
    --primary-foreground: 26 83% 14%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 45 93% 58%; /* Gold */
    --accent-foreground: 26 83% 14%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 45 93% 58%; /* Gold */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Smooth transitions for theme changes */
  * {
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
  }

  /* Custom scrollbar for dark mode */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-md;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }

  .dark ::-webkit-scrollbar-thumb {
    @apply bg-gold-500/30;
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    @apply bg-gold-500/50;
  }

  /* Gold glow effect for dark mode interactive elements */
  .dark .gold-glow {
    box-shadow: 0 0 10px rgba(245, 158, 11, 0.3);
  }

  .dark .gold-glow:hover {
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.5);
  }

  /* Enhanced focus styles for dark mode */
  .dark *:focus-visible {
    @apply ring-gold-500 ring-offset-background;
  }

  /* Custom selection colors */
  ::selection {
    @apply bg-primary/20 text-primary-foreground;
  }

  .dark ::selection {
    @apply bg-gold-500/30 text-gold-900;
  }

  /* Gradient backgrounds for hero sections */
  .hero-gradient-light {
    background: linear-gradient(135deg, 
      hsl(var(--background)) 0%, 
      hsl(var(--muted)) 100%
    );
  }

  .hero-gradient-dark {
    background: linear-gradient(135deg, 
      hsl(var(--background)) 0%, 
      hsl(217.2, 32.6%, 8%) 100%
    );
  }

  /* Gold accent gradients for dark mode */
  .gold-gradient {
    background: linear-gradient(135deg, 
      #f59e0b 0%, 
      #d97706 50%, 
      #b45309 100%
    );
  }

  .gold-text-gradient {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Card hover effects */
  .card-hover {
    transition: all 0.3s ease;
  }

  .card-hover:hover {
    transform: translateY(-2px);
    @apply shadow-lg;
  }

  .dark .card-hover:hover {
    box-shadow: 0 10px 25px rgba(245, 158, 11, 0.1);
  }

  /* Button variants for gold theme */
  .btn-gold {
    @apply bg-gold-500 text-gold-900 hover:bg-gold-600 focus:ring-gold-500;
  }

  .btn-gold-outline {
    @apply border-gold-500 text-gold-500 hover:bg-gold-500 hover:text-gold-900 focus:ring-gold-500;
  }

  /* Loading spinner with gold color in dark mode */
  .spinner-gold {
    border-color: rgba(245, 158, 11, 0.3);
    border-top-color: #f59e0b;
  }

  /* Custom animations */
  @keyframes shimmer {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  .shimmer {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.4),
      transparent
    );
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
  }

  .dark .shimmer {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(245, 158, 11, 0.2),
      transparent
    );
  }

  /* Typography enhancements */
  .text-balance {
    text-wrap: balance;
  }

  /* Print styles */
  @media print {
    .no-print {
      display: none !important;
    }
    
    body {
      @apply text-black bg-white;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    :root {
      --border: 0 0% 20%;
    }
    
    .dark {
      --border: 0 0% 80%;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}
